{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"attributes\", \"crossbeam-utils\", \"default\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"tokio1\", \"wasm-bindgen-futures\"]", "declared_features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"docs\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"io_safety\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"surf\", \"tokio02\", \"tokio03\", \"tokio1\", \"unstable\", \"wasm-bindgen-futures\"]", "target": 9139776409365598091, "profile": 2241668132362809309, "path": 15006081452338764564, "deps": [[5103565458935487, "futures_io", false, 14003796425397622648], [1615478164327904835, "pin_utils", false, 11973718540244705271], [1906322745568073236, "pin_project_lite", false, 7084418078783317499], [3129130049864710036, "memchr", false, 10024402093055877215], [3722963349756955755, "once_cell", false, 2363945442117183028], [4468123440088164316, "crossbeam_utils", false, 4246566602502603334], [5195813957092839672, "async_lock", false, 16899305035100580198], [5302544599749092241, "async_channel", false, 5376698456064378825], [5986029879202738730, "log", false, 14504179726639124161], [6955678925937229351, "slab", false, 10116646655836202069], [7425331225454150061, "futures_lite", false, 5720652990634652220], [7620660491849607393, "futures_core", false, 17451578802806441489], [9511937138168509053, "async_attributes", false, 13422225676985540092], [12616986139693676752, "async_io", false, 9506639721574363719], [13330646740533913557, "async_global_executor", false, 7603770269372426853], [17569958903244628888, "kv_log_macro", false, 13511002681067566023]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-std-c3f8570422591e12\\dep-lib-async_std", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}