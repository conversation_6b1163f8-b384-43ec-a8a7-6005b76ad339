# WellTrack Pharmacy Application - Environment Configuration
# Copy this file to src-tauri/.env and update the values for your local development environment

# Database Configuration
DATABASE_URL=postgres://postgres:password@localhost:5432/whatsapp_sender_pro
DB_NAME=whatsapp_sender_pro
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Production Database (optional - for production deployments)
DATABASE_URL_PROD=***************************************/whatsapp_sender_pro_prod

# Application Environment
ENV=development

# Tauri Configuration
TAURI_PRIVATE_KEY=
TAURI_KEY_PASSWORD=

# Development Settings
RUST_LOG=debug
RUST_BACKTRACE=1
