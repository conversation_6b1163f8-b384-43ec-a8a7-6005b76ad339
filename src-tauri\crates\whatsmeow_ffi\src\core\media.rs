use base64::{engine::general_purpose::STANDARD as BASE64, Engine as _};
use mime_guess::MimeGuess;
use serde::{Deserialize, Serialize};
use std::path::Path;
use thiserror::Error;

/// WhatsApp media size limits (in bytes)
pub const MAX_IMAGE_SIZE: usize = 16 * 1024 * 1024; // 16MB
pub const MAX_VIDEO_SIZE: usize = 64 * 1024 * 1024; // 64MB
pub const MAX_AUDIO_SIZE: usize = 16 * 1024 * 1024; // 16MB
pub const MAX_DOCUMENT_SIZE: usize = 100 * 1024 * 1024; // 100MB
pub const MAX_STICKER_SIZE: usize = 500 * 1024; // 500KB

/// Supported image formats for WhatsApp
pub const SUPPORTED_IMAGE_FORMATS: &[&str] = &[
    "image/jpeg",
    "image/png", 
    "image/webp",
    "image/gif"
];

/// Supported video formats for WhatsApp
pub const SUPPORTED_VIDEO_FORMATS: &[&str] = &[
    "video/mp4",
    "video/avi",
    "video/mov",
    "video/3gpp",
    "video/quicktime"
];

/// Supported audio formats for WhatsApp
pub const SUPPORTED_AUDIO_FORMATS: &[&str] = &[
    "audio/mpeg",
    "audio/mp3", 
    "audio/aac",
    "audio/ogg",
    "audio/wav",
    "audio/amr",
    "audio/opus"
];

/// Supported document formats for WhatsApp
pub const SUPPORTED_DOCUMENT_FORMATS: &[&str] = &[
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "application/zip",
    "application/x-rar-compressed",
    "application/x-7z-compressed"
];

/// Media validation errors
#[derive(Error, Debug, Clone, PartialEq)]
pub enum MediaError {
    #[error("File not found: {path}")]
    FileNotFound { path: String },
    
    #[error("File too large: {size} bytes (max: {max_size} bytes for {media_type})")]
    FileTooLarge { size: usize, max_size: usize, media_type: String },
    
    #[error("Unsupported format: {mime_type} for {media_type}")]
    UnsupportedFormat { mime_type: String, media_type: String },
    
    #[error("Invalid base64 data: {reason}")]
    InvalidBase64 { reason: String },
    
    #[error("Invalid URL format: {url}")]
    InvalidUrl { url: String },
    
    #[error("IO error: {message}")]
    IoError { message: String },
    
    #[error("MIME type detection failed: {reason}")]
    MimeDetectionFailed { reason: String },
    
    #[error("Media metadata extraction failed: {reason}")]
    MetadataExtractionFailed { reason: String },
}

impl From<std::io::Error> for MediaError {
    fn from(error: std::io::Error) -> Self {
        MediaError::IoError {
            message: error.to_string(),
        }
    }
}

/// Enhanced media source with comprehensive validation
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(tag = "source_type", content = "source")]
pub enum MediaSource {
    #[serde(rename = "local_path")]
    LocalPath { path: String },
    
    #[serde(rename = "remote_url")]
    RemoteUrl { url: String },
    
    #[serde(rename = "base64")]
    Base64 { data: String, mime_type: Option<String> },
    
    #[serde(rename = "bytes")]
    Bytes { data: Vec<u8> },
}

impl MediaSource {
    /// Create MediaSource from file path with validation
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self, MediaError> {
        let path = path.as_ref();
        
        if !path.exists() {
            return Err(MediaError::FileNotFound {
                path: path.display().to_string(),
            });
        }
        
        Ok(Self::LocalPath {
            path: path.display().to_string(),
        })
    }
    
    /// Create MediaSource from URL with basic validation
    pub fn from_url(url: &str) -> Result<Self, MediaError> {
        // Basic URL validation
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Err(MediaError::InvalidUrl {
                url: url.to_string(),
            });
        }
        
        Ok(Self::RemoteUrl {
            url: url.to_string(),
        })
    }
    
    /// Create MediaSource from bytes
    pub fn from_bytes(data: Vec<u8>) -> Self {
        Self::Bytes { data }
    }
    
    /// Create MediaSource from base64 string
    pub fn from_base64(data: &str, mime_type: Option<String>) -> Result<Self, MediaError> {
        // Validate base64 format
        BASE64.decode(data).map_err(|e| MediaError::InvalidBase64 {
            reason: e.to_string(),
        })?;
        
        Ok(Self::Base64 {
            data: data.to_string(),
            mime_type,
        })
    }
    
    /// Get the raw bytes from the media source
    pub fn get_bytes(&self) -> Result<Vec<u8>, MediaError> {
        match self {
            Self::LocalPath { path } => {
                std::fs::read(path).map_err(|e| MediaError::IoError {
                    message: format!("Failed to read file {}: {}", path, e),
                })
            }
            Self::Base64 { data, .. } => {
                BASE64.decode(data).map_err(|e| MediaError::InvalidBase64 {
                    reason: e.to_string(),
                })
            }
            Self::Bytes { data } => Ok(data.clone()),
            Self::RemoteUrl { .. } => {
                Err(MediaError::IoError {
                    message: "Cannot read bytes from remote URL without downloading".to_string(),
                })
            }
        }
    }
    
    /// Get file size in bytes
    pub fn get_size(&self) -> Result<usize, MediaError> {
        match self {
            Self::LocalPath { path } => {
                let metadata = std::fs::metadata(path)?;
                Ok(metadata.len() as usize)
            }
            Self::Base64 { data, .. } => {
                // Approximate decoded size (base64 encoding adds ~33% overhead)
                Ok((data.len() * 3) / 4)
            }
            Self::Bytes { data } => Ok(data.len()),
            Self::RemoteUrl { .. } => {
                Err(MediaError::IoError {
                    message: "Cannot determine size of remote URL without downloading".to_string(),
                })
            }
        }
    }
    
    /// Detect MIME type using multiple methods
    pub fn detect_mime_type(&self) -> Result<String, MediaError> {
        match self {
            Self::LocalPath { path } => {
                // First try mime_guess from file extension
                if let Some(mime) = MimeGuess::from_path(path).first() {
                    return Ok(mime.to_string());
                }
                
                // Fallback to file signature detection
                let bytes = self.get_bytes()?;
                Ok(detect_mime_from_signature(&bytes))
            }
            Self::Base64 { mime_type: Some(mime), .. } => Ok(mime.clone()),
            Self::Base64 { data, mime_type: None } => {
                let bytes = BASE64.decode(data).map_err(|e| MediaError::InvalidBase64 {
                    reason: e.to_string(),
                })?;
                Ok(detect_mime_from_signature(&bytes))
            }
            Self::Bytes { data } => Ok(detect_mime_from_signature(data)),
            Self::RemoteUrl { url } => {
                // Try to guess from URL extension
                if let Some(mime) = MimeGuess::from_path(url).first() {
                    Ok(mime.to_string())
                } else {
                    Ok("application/octet-stream".to_string())
                }
            }
        }
    }
    
    /// Validate media source for specific media type
    pub fn validate_for_type(&self, media_type: &MediaType) -> Result<(), MediaError> {
        let size = self.get_size()?;
        let mime_type = self.detect_mime_type()?;
        
        // Check size limits
        let max_size = match media_type {
            MediaType::Image => MAX_IMAGE_SIZE,
            MediaType::Video => MAX_VIDEO_SIZE,
            MediaType::Audio => MAX_AUDIO_SIZE,
            MediaType::Document => MAX_DOCUMENT_SIZE,
            MediaType::Sticker => MAX_STICKER_SIZE,
        };
        
        if size > max_size {
            return Err(MediaError::FileTooLarge {
                size,
                max_size,
                media_type: format!("{:?}", media_type),
            });
        }
        
        // Check format support
        let supported_formats = match media_type {
            MediaType::Image => SUPPORTED_IMAGE_FORMATS,
            MediaType::Video => SUPPORTED_VIDEO_FORMATS,
            MediaType::Audio => SUPPORTED_AUDIO_FORMATS,
            MediaType::Document => SUPPORTED_DOCUMENT_FORMATS,
            MediaType::Sticker => SUPPORTED_IMAGE_FORMATS, // Stickers use image formats
        };
        
        if !supported_formats.contains(&mime_type.as_str()) {
            return Err(MediaError::UnsupportedFormat {
                mime_type,
                media_type: format!("{:?}", media_type),
            });
        }
        
        Ok(())
    }
}

/// Media type enumeration for validation
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MediaType {
    Image,
    Video,
    Audio,
    Document,
    Sticker,
}

/// Enhanced message content with comprehensive media support
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(tag = "type")]
pub enum MessageContent {
    #[serde(rename = "text")]
    Text {
        text: String,
        quoted_message_id: Option<String>,
        mentioned_numbers: Option<Vec<String>>,
    },
    
    #[serde(rename = "image")]
    Image {
        #[serde(flatten)]
        source: MediaSource,
        caption: Option<String>,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
        width: Option<u32>,
        height: Option<u32>,
    },
    
    #[serde(rename = "video")]
    Video {
        #[serde(flatten)]
        source: MediaSource,
        caption: Option<String>,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
        duration: Option<u32>, // Duration in seconds
        width: Option<u32>,
        height: Option<u32>,
    },
    
    #[serde(rename = "audio")]
    Audio {
        #[serde(flatten)]
        source: MediaSource,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
        duration: Option<u32>, // Duration in seconds
        is_voice: bool,        // True for voice messages, false for music
    },
    
    #[serde(rename = "document")]
    Document {
        #[serde(flatten)]
        source: MediaSource,
        caption: Option<String>,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
        filename: Option<String>,
        file_size: Option<u64>,
    },
    
    #[serde(rename = "sticker")]
    Sticker {
        #[serde(flatten)]
        source: MediaSource,
        mime_type: Option<String>,
        width: Option<u32>,
        height: Option<u32>,
    },
}

impl MessageContent {
    /// Validate the message content
    pub fn validate(&self) -> Result<(), MediaError> {
        match self {
            Self::Text { text, .. } => {
                if text.is_empty() {
                    return Err(MediaError::IoError {
                        message: "Text message cannot be empty".to_string(),
                    });
                }
                Ok(())
            }
            Self::Image { source, .. } => source.validate_for_type(&MediaType::Image),
            Self::Video { source, .. } => source.validate_for_type(&MediaType::Video),
            Self::Audio { source, .. } => source.validate_for_type(&MediaType::Audio),
            Self::Document { source, .. } => source.validate_for_type(&MediaType::Document),
            Self::Sticker { source, .. } => source.validate_for_type(&MediaType::Sticker),
        }
    }
    
    /// Get the media source if this is a media message
    pub fn get_media_source(&self) -> Option<&MediaSource> {
        match self {
            Self::Text { .. } => None,
            Self::Image { source, .. }
            | Self::Video { source, .. }
            | Self::Audio { source, .. }
            | Self::Document { source, .. }
            | Self::Sticker { source, .. } => Some(source),
        }
    }
    
    /// Get the MIME type if available
    pub fn get_mime_type(&self) -> Option<String> {
        match self {
            Self::Text { .. } => None,
            Self::Image { mime_type, source, .. }
            | Self::Video { mime_type, source, .. }
            | Self::Audio { mime_type, source, .. }
            | Self::Document { mime_type, source, .. }
            | Self::Sticker { mime_type, source, .. } => {
                mime_type.clone().or_else(|| source.detect_mime_type().ok())
            }
        }
    }
    
    /// Convert to JSON string
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }
    
    /// Create from JSON string
    pub fn from_json(json: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json)
    }
}

/// Detect MIME type from file signature (magic bytes)
pub fn detect_mime_from_signature(data: &[u8]) -> String {
    if data.len() < 4 {
        return "application/octet-stream".to_string();
    }
    
    // Image formats
    if data.starts_with(&[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]) {
        return "image/png".to_string();
    }
    if data.starts_with(&[0xFF, 0xD8, 0xFF]) {
        return "image/jpeg".to_string();
    }
    if data.len() >= 12
        && data[0..4] == [0x52, 0x49, 0x46, 0x46]
        && data[8..12] == [0x57, 0x45, 0x42, 0x50]
    {
        return "image/webp".to_string();
    }
    if data.starts_with(b"GIF87a") || data.starts_with(b"GIF89a") {
        return "image/gif".to_string();
    }
    
    // Video formats
    if data.len() >= 12 && data[4..8] == [0x66, 0x74, 0x79, 0x70] {
        // Check for specific MP4 subtypes
        if data[8..12] == *b"isom" || data[8..12] == *b"mp41" || data[8..12] == *b"mp42" {
            return "video/mp4".to_string();
        }
        if data[8..12] == *b"qt  " {
            return "video/quicktime".to_string();
        }
        return "video/mp4".to_string(); // Default for ftyp
    }
    if data.starts_with(&[0x00, 0x00, 0x00, 0x14, 0x66, 0x74, 0x79, 0x70, 0x33, 0x67, 0x70]) {
        return "video/3gpp".to_string();
    }
    
    // Audio formats
    if data.starts_with(&[0xFF, 0xFB]) || data.starts_with(&[0xFF, 0xF3]) || data.starts_with(&[0xFF, 0xF2]) {
        return "audio/mpeg".to_string();
    }
    if data.starts_with(b"OggS") {
        return "audio/ogg".to_string();
    }
    if data.starts_with(b"RIFF") && data.len() >= 12 && data[8..12] == *b"WAVE" {
        return "audio/wav".to_string();
    }
    if data.starts_with(&[0x23, 0x21, 0x41, 0x4D, 0x52]) {
        return "audio/amr".to_string();
    }
    
    // Document formats
    if data.starts_with(b"%PDF") {
        return "application/pdf".to_string();
    }
    if data.starts_with(&[0x50, 0x4B, 0x03, 0x04]) || data.starts_with(&[0x50, 0x4B, 0x05, 0x06]) {
        // ZIP-based formats (DOCX, XLSX, etc.)
        return "application/zip".to_string();
    }
    if data.starts_with(&[0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]) {
        // Microsoft Office legacy formats
        return "application/msword".to_string();
    }
    if data.starts_with(&[0x52, 0x61, 0x72, 0x21, 0x1A, 0x07, 0x00]) {
        return "application/x-rar-compressed".to_string();
    }
    if data.starts_with(&[0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C]) {
        return "application/x-7z-compressed".to_string();
    }
    
    "application/octet-stream".to_string()
}

/// Extract basic metadata from media
pub fn extract_media_metadata(source: &MediaSource) -> Result<MediaMetadata, MediaError> {
    let bytes = source.get_bytes()?;
    let mime_type = source.detect_mime_type()?;
    let size = bytes.len();
    
    let mut metadata = MediaMetadata {
        mime_type: mime_type.clone(),
        file_size: size as u64,
        width: None,
        height: None,
        duration: None,
    };
    
    // Extract dimensions for images
    if mime_type.starts_with("image/") {
        if let Ok((width, height)) = extract_image_dimensions(&bytes, &mime_type) {
            metadata.width = Some(width);
            metadata.height = Some(height);
        }
    }
    
    Ok(metadata)
}

/// Basic image dimension extraction
fn extract_image_dimensions(data: &[u8], mime_type: &str) -> Result<(u32, u32), MediaError> {
    match mime_type {
        "image/png" => extract_png_dimensions(data),
        "image/jpeg" => extract_jpeg_dimensions(data),
        "image/gif" => extract_gif_dimensions(data),
        "image/webp" => extract_webp_dimensions(data),
        _ => Err(MediaError::MetadataExtractionFailed {
            reason: format!("Unsupported image format for dimension extraction: {}", mime_type),
        }),
    }
}

fn extract_png_dimensions(data: &[u8]) -> Result<(u32, u32), MediaError> {
    if data.len() < 24 {
        return Err(MediaError::MetadataExtractionFailed {
            reason: "PNG data too short".to_string(),
        });
    }
    
    // PNG dimensions are at bytes 16-23 (big-endian)
    let width = u32::from_be_bytes([data[16], data[17], data[18], data[19]]);
    let height = u32::from_be_bytes([data[20], data[21], data[22], data[23]]);
    
    Ok((width, height))
}

fn extract_jpeg_dimensions(data: &[u8]) -> Result<(u32, u32), MediaError> {
    // Simple JPEG dimension extraction - look for SOF markers
    let mut i = 2; // Skip initial FF D8
    
    while i + 4 < data.len() {
        if data[i] == 0xFF {
            let marker = data[i + 1];
            
            // SOF markers (Start of Frame)
            if (0xC0..=0xC3).contains(&marker) || (0xC5..=0xC7).contains(&marker) || (0xC9..=0xCB).contains(&marker) || (0xCD..=0xCF).contains(&marker) {
                if i + 9 < data.len() {
                    let height = u16::from_be_bytes([data[i + 5], data[i + 6]]) as u32;
                    let width = u16::from_be_bytes([data[i + 7], data[i + 8]]) as u32;
                    return Ok((width, height));
                }
            }
            
            // Skip to next marker
            if i + 2 < data.len() {
                let length = u16::from_be_bytes([data[i + 2], data[i + 3]]) as usize;
                i += 2 + length;
            } else {
                break;
            }
        } else {
            i += 1;
        }
    }
    
    Err(MediaError::MetadataExtractionFailed {
        reason: "Could not find JPEG dimensions".to_string(),
    })
}

fn extract_gif_dimensions(data: &[u8]) -> Result<(u32, u32), MediaError> {
    if data.len() < 10 {
        return Err(MediaError::MetadataExtractionFailed {
            reason: "GIF data too short".to_string(),
        });
    }
    
    // GIF dimensions are at bytes 6-9 (little-endian)
    let width = u16::from_le_bytes([data[6], data[7]]) as u32;
    let height = u16::from_le_bytes([data[8], data[9]]) as u32;
    
    Ok((width, height))
}

fn extract_webp_dimensions(data: &[u8]) -> Result<(u32, u32), MediaError> {
    if data.len() < 30 {
        return Err(MediaError::MetadataExtractionFailed {
            reason: "WebP data too short".to_string(),
        });
    }
    
    // Check WebP format type
    if &data[12..16] == b"VP8 " {
        // Simple WebP format
        if data.len() >= 30 {
            let width = (u16::from_le_bytes([data[26], data[27]]) & 0x3FFF) as u32 + 1;
            let height = (u16::from_le_bytes([data[28], data[29]]) & 0x3FFF) as u32 + 1;
            return Ok((width, height));
        }
    } else if &data[12..16] == b"VP8L" {
        // Lossless WebP format
        if data.len() >= 25 {
            let bits = u32::from_le_bytes([data[21], data[22], data[23], data[24]]);
            let width = (bits & 0x3FFF) + 1;
            let height = ((bits >> 14) & 0x3FFF) + 1;
            return Ok((width, height));
        }
    }
    
    Err(MediaError::MetadataExtractionFailed {
        reason: "Unsupported WebP format".to_string(),
    })
}

/// Media metadata structure
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct MediaMetadata {
    pub mime_type: String,
    pub file_size: u64,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub duration: Option<u32>,
}

#[cfg(test)]
mod tests {
    use super::*;

    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_media_source_from_file() {
        let mut temp_file = NamedTempFile::new().unwrap();
        temp_file.write_all(b"test content").unwrap();
        
        let source = MediaSource::from_file(temp_file.path()).unwrap();
        match source {
            MediaSource::LocalPath { path } => {
                assert_eq!(path, temp_file.path().display().to_string());
            }
            _ => panic!("Expected LocalPath variant"),
        }
    }

    #[test]
    fn test_media_source_from_nonexistent_file() {
        let result = MediaSource::from_file("/nonexistent/file.txt");
        assert!(matches!(result, Err(MediaError::FileNotFound { .. })));
    }

    #[test]
    fn test_media_source_from_url() {
        let source = MediaSource::from_url("https://example.com/image.jpg").unwrap();
        match source {
            MediaSource::RemoteUrl { url } => {
                assert_eq!(url, "https://example.com/image.jpg");
            }
            _ => panic!("Expected RemoteUrl variant"),
        }
    }

    #[test]
    fn test_media_source_from_invalid_url() {
        let result = MediaSource::from_url("invalid-url");
        assert!(matches!(result, Err(MediaError::InvalidUrl { .. })));
    }

    #[test]
    fn test_media_source_from_base64() {
        let data = BASE64.encode(b"test content");
        let source = MediaSource::from_base64(&data, Some("text/plain".to_string())).unwrap();
        
        match source {
            MediaSource::Base64 { data: b64_data, mime_type } => {
                assert_eq!(b64_data, data);
                assert_eq!(mime_type, Some("text/plain".to_string()));
            }
            _ => panic!("Expected Base64 variant"),
        }
    }

    #[test]
    fn test_media_source_from_invalid_base64() {
        let result = MediaSource::from_base64("invalid-base64!", None);
        assert!(matches!(result, Err(MediaError::InvalidBase64 { .. })));
    }

    #[test]
    fn test_media_source_get_bytes() {
        let test_data = b"test content";
        let source = MediaSource::from_bytes(test_data.to_vec());
        
        let bytes = source.get_bytes().unwrap();
        assert_eq!(bytes, test_data);
    }

    #[test]
    fn test_detect_mime_from_signature_png() {
        let png_signature = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];
        let mime_type = detect_mime_from_signature(&png_signature);
        assert_eq!(mime_type, "image/png");
    }

    #[test]
    fn test_detect_mime_from_signature_jpeg() {
        let jpeg_signature = [0xFF, 0xD8, 0xFF, 0xE0];
        let mime_type = detect_mime_from_signature(&jpeg_signature);
        assert_eq!(mime_type, "image/jpeg");
    }

    #[test]
    fn test_detect_mime_from_signature_unknown() {
        let unknown_data = [0x00, 0x01, 0x02, 0x03];
        let mime_type = detect_mime_from_signature(&unknown_data);
        assert_eq!(mime_type, "application/octet-stream");
    }

    #[test]
    fn test_message_content_validation_text() {
        let content = MessageContent::Text {
            text: "Hello, World!".to_string(),
            quoted_message_id: None,
            mentioned_numbers: None,
        };
        
        assert!(content.validate().is_ok());
    }

    #[test]
    fn test_message_content_validation_empty_text() {
        let content = MessageContent::Text {
            text: "".to_string(),
            quoted_message_id: None,
            mentioned_numbers: None,
        };
        
        assert!(content.validate().is_err());
    }

    #[test]
    fn test_message_content_serialization() {
        let content = MessageContent::Text {
            text: "Hello, World!".to_string(),
            quoted_message_id: None,
            mentioned_numbers: None,
        };
        
        let json = content.to_json().unwrap();
        let deserialized = MessageContent::from_json(&json).unwrap();
        
        assert_eq!(content, deserialized);
    }

    #[test]
    fn test_media_validation_size_limit() {
        // Create a large byte array that exceeds image size limit
        let large_data = vec![0u8; MAX_IMAGE_SIZE + 1];
        let source = MediaSource::from_bytes(large_data);
        
        let result = source.validate_for_type(&MediaType::Image);
        assert!(matches!(result, Err(MediaError::FileTooLarge { .. })));
    }

    #[test]
    fn test_png_dimension_extraction() {
        // Minimal PNG header with dimensions 100x200
        let png_data = vec![
            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
            0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
            0x49, 0x48, 0x44, 0x52, // IHDR
            0x00, 0x00, 0x00, 0x64, // Width: 100
            0x00, 0x00, 0x00, 0xC8, // Height: 200
        ];
        
        let (width, height) = extract_png_dimensions(&png_data).unwrap();
        assert_eq!(width, 100);
        assert_eq!(height, 200);
    }

    #[test]
    fn test_gif_dimension_extraction() {
        // Minimal GIF header with dimensions 150x250
        let gif_data = vec![
            0x47, 0x49, 0x46, 0x38, 0x39, 0x61, // GIF89a signature
            0x96, 0x00, // Width: 150 (little-endian)
            0xFA, 0x00, // Height: 250 (little-endian)
        ];
        
        let (width, height) = extract_gif_dimensions(&gif_data).unwrap();
        assert_eq!(width, 150);
        assert_eq!(height, 250);
    }

    #[test]
    fn test_media_source_validation_image_format() {
        // Create a PNG-like source
        let png_data = vec![0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];
        let source = MediaSource::from_bytes(png_data);
        
        // Should pass validation for image type
        assert!(source.validate_for_type(&MediaType::Image).is_ok());
    }

    #[test]
    fn test_media_source_validation_unsupported_format() {
        // Create an unsupported format
        let unknown_data = vec![0x00, 0x01, 0x02, 0x03];
        let source = MediaSource::from_bytes(unknown_data);
        
        // Should fail validation for image type due to unsupported format
        let result = source.validate_for_type(&MediaType::Image);
        assert!(matches!(result, Err(MediaError::UnsupportedFormat { .. })));
    }

    #[test]
    fn test_media_source_size_validation_different_types() {
        // Test size limits for different media types with PNG signature
        let mut small_data = vec![0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]; // PNG signature
        small_data.extend(vec![0u8; 1000]); // Add 1KB of data
        let source = MediaSource::from_bytes(small_data);
        
        // Should pass for all types since PNG is supported for images and small size
        assert!(source.validate_for_type(&MediaType::Image).is_ok());
        // For other types, it will fail due to format but that's expected
        // Let's test with appropriate formats for each type
        
        // Test video with MP4 signature
        let mut video_data = vec![0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70]; // MP4 signature
        video_data.extend(vec![0u8; 1000]);
        let video_source = MediaSource::from_bytes(video_data);
        assert!(video_source.validate_for_type(&MediaType::Video).is_ok());
        
        // Test audio with MP3 signature
        let mut audio_data = vec![0xFF, 0xFB]; // MP3 signature
        audio_data.extend(vec![0u8; 1000]);
        let audio_source = MediaSource::from_bytes(audio_data);
        assert!(audio_source.validate_for_type(&MediaType::Audio).is_ok());
        
        // Test document with PDF signature
        let mut doc_data = b"%PDF".to_vec();
        doc_data.extend(vec![0u8; 1000]);
        let doc_source = MediaSource::from_bytes(doc_data);
        assert!(doc_source.validate_for_type(&MediaType::Document).is_ok());
    }

    #[test]
    fn test_media_source_sticker_size_limit() {
        // Create data larger than sticker limit (500KB)
        let large_data = vec![0u8; MAX_STICKER_SIZE + 1];
        let source = MediaSource::from_bytes(large_data);
        
        // Should fail for sticker type
        let result = source.validate_for_type(&MediaType::Sticker);
        assert!(matches!(result, Err(MediaError::FileTooLarge { .. })));
    }

    #[test]
    fn test_message_content_image_with_metadata() {
        let source = MediaSource::from_bytes(vec![0x89, 0x50, 0x4E, 0x47]); // PNG signature
        let content = MessageContent::Image {
            source,
            caption: Some("Test image".to_string()),
            quoted_message_id: None,
            mime_type: Some("image/png".to_string()),
            width: Some(100),
            height: Some(200),
        };
        
        assert_eq!(content.get_mime_type(), Some("image/png".to_string()));
        assert!(content.get_media_source().is_some());
    }

    #[test]
    fn test_message_content_video_with_metadata() {
        let source = MediaSource::from_bytes(vec![0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70]); // MP4 signature
        let content = MessageContent::Video {
            source,
            caption: Some("Test video".to_string()),
            quoted_message_id: None,
            mime_type: Some("video/mp4".to_string()),
            duration: Some(30),
            width: Some(1920),
            height: Some(1080),
        };
        
        assert_eq!(content.get_mime_type(), Some("video/mp4".to_string()));
        assert!(content.get_media_source().is_some());
    }

    #[test]
    fn test_message_content_audio_voice_vs_music() {
        let source = MediaSource::from_bytes(vec![0xFF, 0xFB]); // MP3 signature
        
        // Voice message
        let voice_content = MessageContent::Audio {
            source: source.clone(),
            quoted_message_id: None,
            mime_type: Some("audio/mpeg".to_string()),
            duration: Some(10),
            is_voice: true,
        };
        
        // Music message
        let music_content = MessageContent::Audio {
            source,
            quoted_message_id: None,
            mime_type: Some("audio/mpeg".to_string()),
            duration: Some(180),
            is_voice: false,
        };
        
        // Both should be valid but have different is_voice flags
        assert!(voice_content.validate().is_ok());
        assert!(music_content.validate().is_ok());
    }

    #[test]
    fn test_message_content_document_with_file_info() {
        let source = MediaSource::from_bytes(b"%PDF-1.4".to_vec()); // PDF signature
        let content = MessageContent::Document {
            source,
            caption: Some("Important document".to_string()),
            quoted_message_id: None,
            mime_type: Some("application/pdf".to_string()),
            filename: Some("document.pdf".to_string()),
            file_size: Some(1024),
        };
        
        assert_eq!(content.get_mime_type(), Some("application/pdf".to_string()));
        assert!(content.get_media_source().is_some());
    }

    #[test]
    fn test_message_content_sticker_with_dimensions() {
        let source = MediaSource::from_bytes(vec![0x52, 0x49, 0x46, 0x46, 0x00, 0x00, 0x00, 0x00, 0x57, 0x45, 0x42, 0x50]); // WebP signature
        let content = MessageContent::Sticker {
            source,
            mime_type: Some("image/webp".to_string()),
            width: Some(512),
            height: Some(512),
        };
        
        assert_eq!(content.get_mime_type(), Some("image/webp".to_string()));
        assert!(content.get_media_source().is_some());
    }

    #[test]
    fn test_media_metadata_extraction() {
        // Create a PNG with known dimensions
        let png_data = vec![
            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
            0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
            0x49, 0x48, 0x44, 0x52, // IHDR
            0x00, 0x00, 0x00, 0x64, // Width: 100
            0x00, 0x00, 0x00, 0xC8, // Height: 200
        ];
        
        let source = MediaSource::from_bytes(png_data);
        let metadata = extract_media_metadata(&source).unwrap();
        
        assert_eq!(metadata.mime_type, "image/png");
        assert_eq!(metadata.width, Some(100));
        assert_eq!(metadata.height, Some(200));
        assert!(metadata.file_size > 0);
    }

    #[test]
    fn test_detect_mime_from_signature_comprehensive() {
        // Test various file signatures
        let test_cases = vec![
            (vec![0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A], "image/png"),
            (vec![0xFF, 0xD8, 0xFF, 0xE0], "image/jpeg"),
            (b"GIF89a".to_vec(), "image/gif"),
            (vec![0x52, 0x49, 0x46, 0x46, 0x00, 0x00, 0x00, 0x00, 0x57, 0x45, 0x42, 0x50], "image/webp"),
            (b"%PDF-1.4".to_vec(), "application/pdf"),
            (vec![0x50, 0x4B, 0x03, 0x04], "application/zip"),
            (vec![0xFF, 0xFB, 0x90, 0x00], "audio/mpeg"),
            (b"OggS".to_vec(), "audio/ogg"),
            (b"RIFF\x00\x00\x00\x00WAVE".to_vec(), "audio/wav"),
        ];
        
        for (data, expected_mime) in test_cases {
            let detected = detect_mime_from_signature(&data);
            assert_eq!(detected, expected_mime, "Failed for data: {:?}", data);
        }
    }

    #[test]
    fn test_media_source_from_url_validation() {
        // Valid URLs
        assert!(MediaSource::from_url("https://example.com/image.jpg").is_ok());
        assert!(MediaSource::from_url("http://example.com/video.mp4").is_ok());
        
        // Invalid URLs
        assert!(MediaSource::from_url("ftp://example.com/file.txt").is_err());
        assert!(MediaSource::from_url("not-a-url").is_err());
        assert!(MediaSource::from_url("").is_err());
    }

    #[test]
    fn test_media_source_base64_with_mime_type() {
        let data = BASE64.encode(b"test content");
        let source = MediaSource::from_base64(&data, Some("text/plain".to_string())).unwrap();
        
        // Should use provided MIME type
        assert_eq!(source.detect_mime_type().unwrap(), "text/plain");
        
        // Should be able to get bytes back
        let bytes = source.get_bytes().unwrap();
        assert_eq!(bytes, b"test content");
    }

    #[test]
    fn test_webp_dimension_extraction() {
        // Simple WebP VP8 format with dimensions
        let webp_data = vec![
            0x52, 0x49, 0x46, 0x46, // RIFF
            0x1A, 0x00, 0x00, 0x00, // File size
            0x57, 0x45, 0x42, 0x50, // WEBP
            0x56, 0x50, 0x38, 0x20, // VP8 
            0x0E, 0x00, 0x00, 0x00, // Chunk size
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Padding
            0x63, 0x00, 0x00, 0x00, // Width: 100-1 (encoded as 99)
            0xC7, 0x00, 0x00, 0x00, // Height: 200-1 (encoded as 199)
        ];
        
        let result = extract_webp_dimensions(&webp_data);
        assert!(result.is_ok());
        let (width, height) = result.unwrap();
        assert_eq!(width, 100);
        assert_eq!(height, 200);
    }

    #[test]
    fn test_jpeg_dimension_extraction_with_sof_marker() {
        // JPEG with SOF0 marker
        let jpeg_data = vec![
            0xFF, 0xD8, // SOI
            0xFF, 0xE0, 0x00, 0x10, // APP0 marker
            0x4A, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00,
            0xFF, 0xC0, // SOF0 marker
            0x00, 0x11, // Length
            0x08, // Precision
            0x00, 0xC8, // Height: 200
            0x00, 0x64, // Width: 100
            0x03, // Number of components
        ];
        
        let (width, height) = extract_jpeg_dimensions(&jpeg_data).unwrap();
        assert_eq!(width, 100);
        assert_eq!(height, 200);
    }

    #[test]
    fn test_message_content_json_roundtrip() {
        let original = MessageContent::Image {
            source: MediaSource::Base64 {
                data: BASE64.encode(b"test image data"),
                mime_type: Some("image/png".to_string()),
            },
            caption: Some("Test caption".to_string()),
            quoted_message_id: Some("msg123".to_string()),
            mime_type: Some("image/png".to_string()),
            width: Some(800),
            height: Some(600),
        };
        
        let json = original.to_json().unwrap();
        let deserialized = MessageContent::from_json(&json).unwrap();
        
        assert_eq!(original, deserialized);
    }

    #[test]
    fn test_media_error_types() {
        // Test different error types
        let file_not_found = MediaError::FileNotFound {
            path: "/nonexistent/file.jpg".to_string(),
        };
        assert!(file_not_found.to_string().contains("File not found"));
        
        let file_too_large = MediaError::FileTooLarge {
            size: 1000000,
            max_size: 500000,
            media_type: "Image".to_string(),
        };
        assert!(file_too_large.to_string().contains("File too large"));
        
        let unsupported_format = MediaError::UnsupportedFormat {
            mime_type: "application/unknown".to_string(),
            media_type: "Image".to_string(),
        };
        assert!(unsupported_format.to_string().contains("Unsupported format"));
    }

    #[test]
    fn test_supported_formats_constants() {
        // Test that our supported format constants contain expected formats
        assert!(SUPPORTED_IMAGE_FORMATS.contains(&"image/jpeg"));
        assert!(SUPPORTED_IMAGE_FORMATS.contains(&"image/png"));
        assert!(SUPPORTED_IMAGE_FORMATS.contains(&"image/webp"));
        assert!(SUPPORTED_IMAGE_FORMATS.contains(&"image/gif"));
        
        assert!(SUPPORTED_VIDEO_FORMATS.contains(&"video/mp4"));
        assert!(SUPPORTED_VIDEO_FORMATS.contains(&"video/avi"));
        assert!(SUPPORTED_VIDEO_FORMATS.contains(&"video/mov"));
        
        assert!(SUPPORTED_AUDIO_FORMATS.contains(&"audio/mpeg"));
        assert!(SUPPORTED_AUDIO_FORMATS.contains(&"audio/ogg"));
        assert!(SUPPORTED_AUDIO_FORMATS.contains(&"audio/wav"));
        
        assert!(SUPPORTED_DOCUMENT_FORMATS.contains(&"application/pdf"));
        assert!(SUPPORTED_DOCUMENT_FORMATS.contains(&"text/plain"));
    }

    #[test]
    fn test_media_size_limits_constants() {
        // Test that size limits are reasonable
        assert!(MAX_IMAGE_SIZE > 0);
        assert!(MAX_VIDEO_SIZE > MAX_IMAGE_SIZE);
        assert!(MAX_DOCUMENT_SIZE > MAX_VIDEO_SIZE);
        assert!(MAX_STICKER_SIZE < MAX_IMAGE_SIZE);
        assert_eq!(MAX_STICKER_SIZE, 500 * 1024); // 500KB
    }
}