{"rustc": 16591470773350601817, "features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlx-postgres\"]", "declared_features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlite-use-returning-for-3_35\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 4990465507743545796, "profile": 2241668132362809309, "path": 17667846755963464982, "deps": [[3405707034081185165, "dotenvy", false, 4397146467665378413], [6991425963735389556, "sea_orm", false, 16156525336095497446], [8606274917505247608, "tracing", false, 702195515399835879], [10686516203231854806, "sea_schema", false, 15428724985988263515], [11946729385090170470, "async_trait", false, 2389517705436373950], [16179757479889401155, "sea_orm_cli", false, 14979695786636510038], [16230660778393187092, "tracing_subscriber", false, 159690804789844003], [17612818546626403359, "clap", false, 11906228538422244071]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-migration-ee23abedd1081b2f\\dep-lib-sea_orm_migration", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}