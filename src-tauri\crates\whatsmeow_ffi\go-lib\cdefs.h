#ifndef WHATSAPP_FFI_CDEFS_H
#define WHATSAPP_FFI_CDEFS_H

#include <stdlib.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Event types
typedef enum {
    EVENT_QR_CODE = 1,
    EVENT_MESSAGE_RECEIVED = 2,
    EVENT_CONNECTION_STATUS = 3,
    EVENT_ERROR = 4
} EventType;

// Connection status
typedef enum {
    STATUS_DISCONNECTED = 0,
    STATUS_CONNECTING = 1,
    STATUS_CONNECTED = 2,
    STATUS_LOGGED_OUT = 3
} ConnectionStatus;

// Event callback function pointer
typedef void (*EventCallback)(int event_type, const char* data, void* user_data);

// Error codes
typedef enum {
    ERROR_NONE = 0,
    ERROR_INVALID_HANDLE = 1,
    ERROR_NOT_CONNECTED = 2,
    ERROR_INVALID_JID = 3,
    ERROR_SEND_FAILED = 4,
    ERROR_INTERNAL = 5
} ErrorCode;

// Log levels
typedef enum {
    LOG_LEVEL_OFF = 0,
    LOG_LEVEL_ERROR = 1,
    LOG_LEVEL_WARN = 2,
    LOG_LEVEL_INFO = 3,
    LOG_LEVEL_DEBUG = 4
} LogLevel;

// C shim to call the function pointer safely
static inline void call_event_callback(EventCallback cb, int event_type, const char* data, void* user_data) {
    if (cb != NULL) {
        cb(event_type, data, user_data);
    }
}

#ifdef __cplusplus
}
#endif

#endif // WHATSAPP_FFI_CDEFS_H
