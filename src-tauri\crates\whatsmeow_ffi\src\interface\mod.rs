use std::sync::Arc;

use tokio::sync::mpsc;
use tracing::info;

use crate::{
    core::{
        entities::{LogLevel, MediaSource, MessageContent, WhatsAppEvent},
        errors::{Result, WhatsAppError},
        ports::WhatsAppClientPort,
    },
    infra::whatsapp_adapter::WhatsAppAdapter,
};

/// High-level WhatsApp client interface
pub struct WhatsAppClient {
    adapter: Arc<dyn WhatsAppClientPort + Send + Sync>,
    event_stream: Option<mpsc::UnboundedReceiver<WhatsAppEvent>>,
}

impl WhatsAppClient {
    /// Create a new WhatsApp client
    pub fn new(library_path: &str, db_path: &str) -> Result<Self> {
        let mut adapter = WhatsAppAdapter::new(library_path, db_path)?;
        let event_stream = Some(adapter.event_receiver()?);

        Ok(Self {
            adapter: Arc::new(adapter),
            event_stream,
        })
    }

    /// Connect to WhatsApp
    pub async fn connect(&self) -> Result<()> {
        info!("Connecting to WhatsApp...");
        self.adapter.connect().await?;
        info!("Connection initiated");
        Ok(())
    }

    /// Disconnect from WhatsApp
    pub async fn disconnect(&self) -> Result<()> {
        info!("Disconnecting from WhatsApp...");
        self.adapter.disconnect().await?;
        info!("Disconnected");
        Ok(())
    }

    /// Send a text message (backward compatibility)
    pub async fn send_message(&self, phone_number: &str, text: &str) -> Result<()> {
        let content = MessageContent::Text {
            text: text.to_string(),
            quoted_message_id: None,
            mentioned_numbers: None,
        };
        self.send_message_content(phone_number, &content).await
    }

    /// Send a message with enhanced content (text, image, etc.)
    pub async fn send_message_content(
        &self,
        phone_number: &str,
        content: &MessageContent,
    ) -> Result<()> {
        // Validate the content first
        content.validate().map_err(WhatsAppError::Media)?;

        // Format phone number as JID (add @s.whatsapp.net if not present)
        let jid = if phone_number.contains('@') {
            phone_number.to_string()
        } else {
            format!("{}@s.whatsapp.net", phone_number)
        };

        let json_content = content.to_json().map_err(|e| {
            WhatsAppError::Internal(format!("Failed to serialize message content: {}", e))
        })?;

        if json_content.is_empty() {
            return Err(WhatsAppError::Internal(
                "Failed to serialize message content".to_string(),
            ));
        }

        info!("Sending enhanced message to {}: {:?}", jid, content);

        // For now, use the existing send_message FFI with JSON content
        // This will be enhanced when we add specific media FFI functions
        self.adapter.send_message(&jid, &json_content).await?;
        info!("Enhanced message sent successfully");
        Ok(())
    }

    /// Send an image message with optional caption and MIME type
    pub async fn send_image(
        &self,
        phone_number: &str,
        source: MediaSource,
        caption: Option<String>,
    ) -> Result<()> {
        // Auto-detect MIME type using enhanced detection
        let mime_type = source.detect_mime_type().ok();

        // Extract metadata for dimensions
        let metadata = crate::core::media::extract_media_metadata(&source).ok();
        let (width, height) = metadata
            .as_ref()
            .map(|m| (m.width, m.height))
            .unwrap_or((None, None));

        let content = MessageContent::Image {
            source,
            caption,
            quoted_message_id: None,
            mime_type,
            width,
            height,
        };
        self.send_message_content(phone_number, &content).await
    }

    /// Send an image from a local file path
    pub async fn send_image_from_file(
        &self,
        phone_number: &str,
        file_path: &str,
        caption: Option<String>,
    ) -> Result<()> {
        let source = MediaSource::from_file(file_path).map_err(WhatsAppError::Media)?;
        self.send_image(phone_number, source, caption).await
    }

    /// Send a video message with optional caption
    pub async fn send_video(
        &self,
        phone_number: &str,
        source: MediaSource,
        caption: Option<String>,
        duration: Option<u32>,
    ) -> Result<()> {
        // Auto-detect MIME type for video
        let mime_type = source.detect_mime_type().ok();

        // Extract metadata for dimensions
        let metadata = crate::core::media::extract_media_metadata(&source).ok();
        let (width, height) = metadata
            .as_ref()
            .map(|m| (m.width, m.height))
            .unwrap_or((None, None));

        let content = MessageContent::Video {
            source,
            caption,
            quoted_message_id: None,
            mime_type,
            duration,
            width,
            height,
        };
        self.send_message_content(phone_number, &content).await
    }

    /// Send an audio message (music or voice)
    pub async fn send_audio(
        &self,
        phone_number: &str,
        source: MediaSource,
        duration: Option<u32>,
        is_voice: bool,
    ) -> Result<()> {
        // Auto-detect MIME type for audio
        let mime_type = source.detect_mime_type().ok();

        let content = MessageContent::Audio {
            source,
            quoted_message_id: None,
            mime_type,
            duration,
            is_voice,
        };
        self.send_message_content(phone_number, &content).await
    }

    /// Send a voice message (convenience method)
    pub async fn send_voice(
        &self,
        phone_number: &str,
        source: MediaSource,
        duration: Option<u32>,
    ) -> Result<()> {
        self.send_audio(phone_number, source, duration, true).await
    }

    /// Send a document message
    pub async fn send_document(
        &self,
        phone_number: &str,
        source: MediaSource,
        filename: Option<String>,
        caption: Option<String>,
    ) -> Result<()> {
        // Auto-detect MIME type for documents
        let mime_type = source.detect_mime_type().ok();

        // Get file size
        let file_size = source.get_size().ok().map(|s| s as u64);

        let content = MessageContent::Document {
            source,
            caption,
            quoted_message_id: None,
            mime_type,
            filename,
            file_size,
        };
        self.send_message_content(phone_number, &content).await
    }

    /// Send a sticker message
    pub async fn send_sticker(&self, phone_number: &str, source: MediaSource) -> Result<()> {
        // Auto-detect MIME type for stickers
        let mime_type = source.detect_mime_type().ok();

        // Extract metadata for dimensions
        let metadata = crate::core::media::extract_media_metadata(&source).ok();
        let (width, height) = metadata
            .as_ref()
            .map(|m| (m.width, m.height))
            .unwrap_or((None, None));

        let content = MessageContent::Sticker {
            source,
            mime_type,
            width,
            height,
        };
        self.send_message_content(phone_number, &content).await
    }

    /// Send an image from bytes
    pub async fn send_image_from_bytes(
        &self,
        phone_number: &str,
        image_data: &[u8],
        caption: Option<String>,
    ) -> Result<()> {
        let source = MediaSource::from_bytes(image_data.to_vec());
        self.send_image(phone_number, source, caption).await
    }

    /// Check if the client is connected
    pub async fn is_connected(&self) -> Result<bool> {
        self.adapter.is_connected().await
    }

    /// Get an async stream of events
    pub fn event_stream(&mut self) -> Option<mpsc::UnboundedReceiver<WhatsAppEvent>> {
        self.event_stream.take()
    }

    /// Set global log level for all WhatsApp clients
    pub fn set_global_log_level(&self, level: LogLevel) -> Result<()> {
        self.adapter.set_global_log_level(level)
    }

    /// Get current global log level
    pub fn get_global_log_level(&self) -> Result<LogLevel> {
        self.adapter.get_global_log_level()
    }

    /// Set log level for this specific client
    pub fn set_log_level(&self, level: LogLevel) -> Result<()> {
        self.adapter.set_client_log_level(level)
    }

    /// Get log level for this specific client
    pub fn get_log_level(&self) -> Result<LogLevel> {
        self.adapter.get_client_log_level()
    }

    /// Suppress all Go-side logging by setting global level to Off
    pub fn suppress_go_logs(&self) -> Result<()> {
        self.set_global_log_level(LogLevel::Off)
    }

    /// Destroy the client and clean up resources asynchronously
    ///
    /// This method properly cleans up FFI resources without blocking the async runtime.
    /// It should be called when the client is no longer needed to prevent resource leaks.
    pub async fn destroy(&self) -> Result<()> {
        if let Some(adapter) = self.adapter.as_any().downcast_ref::<WhatsAppAdapter>() {
            adapter.destroy_async().await
        } else {
            Err(WhatsAppError::Internal(
                "Unable to downcast adapter for destroy operation".to_string(),
            ))
        }
    }

    /// Check if the client has been destroyed
    pub fn is_destroyed(&self) -> bool {
        if let Some(adapter) = self.adapter.as_any().downcast_ref::<WhatsAppAdapter>() {
            adapter.is_destroyed()
        } else {
            false
        }
    }
}

/// Builder for WhatsApp client configuration
pub struct WhatsAppClientBuilder {
    library_path: Option<String>,
    db_path: Option<String>,
    log_level: Option<LogLevel>,
}

impl WhatsAppClientBuilder {
    /// Create a new builder
    pub fn new() -> Self {
        Self {
            library_path: None,
            db_path: None,
            log_level: None,
        }
    }

    /// Set the path to the FFI library
    pub fn library_path<P: Into<String>>(mut self, path: P) -> Self {
        self.library_path = Some(path.into());
        self
    }

    /// Set the database path
    pub fn db_path<P: Into<String>>(mut self, path: P) -> Self {
        self.db_path = Some(path.into());
        self
    }

    /// Set the log level for the client
    pub fn with_log_level(mut self, level: LogLevel) -> Self {
        self.log_level = Some(level);
        self
    }

    /// Suppress all Go-side logging
    pub fn suppress_go_logs(mut self) -> Self {
        self.log_level = Some(LogLevel::Off);
        self
    }

    /// Build the WhatsApp client
    pub fn build(self) -> Result<WhatsAppClient> {
        let library_path = self
            .library_path
            .ok_or_else(|| WhatsAppError::Internal("Library path not set".to_string()))?;
        let db_path = self
            .db_path
            .ok_or_else(|| WhatsAppError::Internal("Database path not set".to_string()))?;

        let client = WhatsAppClient::new(&library_path, &db_path)?;

        // Set log level if specified (use global log level for broader suppression)
        if let Some(level) = self.log_level {
            client.set_global_log_level(level)?;
        }

        Ok(client)
    }
}

impl Default for WhatsAppClientBuilder {
    fn default() -> Self {
        Self::new()
    }
}
