//! WhatsApp service layer for Tauri integration
//!
//! This module provides the service layer that bridges the WhatsApp FFI client
//! with the Tauri frontend, handling command invocation, state management,
//! and event emission.

pub mod commands;
pub mod events;
pub mod service;
pub mod state;

#[cfg(test)]
mod tests;

// Re-export main types for convenience
pub use commands::get_command_handlers;
pub use events::{EventEmitter, TauriEventEmitterImpl};
pub use service::WhatsAppService;
pub use state::{ServiceConfig, ServiceState, ServiceStatus};
