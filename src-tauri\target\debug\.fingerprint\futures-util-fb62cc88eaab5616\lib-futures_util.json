{"rustc": 16591470773350601817, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 11226960194569101119, "deps": [[5103565458935487, "futures_io", false, 15671947253275539370], [1615478164327904835, "pin_utils", false, 15291338659036758343], [1906322745568073236, "pin_project_lite", false, 8749487287775455052], [3129130049864710036, "memchr", false, 2482764352869297290], [6955678925937229351, "slab", false, 17115650866986596118], [7013762810557009322, "futures_sink", false, 10370169808994504777], [7620660491849607393, "futures_core", false, 7480744780212006673], [16240732885093539806, "futures_task", false, 13507496711544353015]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-fb62cc88eaab5616\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}