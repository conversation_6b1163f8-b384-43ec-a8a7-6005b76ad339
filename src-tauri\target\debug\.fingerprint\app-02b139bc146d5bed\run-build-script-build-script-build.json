{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 16992196846489127371], [12092653563678505622, "build_script_build", false, 14190772732382438435], [16702348383442838006, "build_script_build", false, 12014944601460561984]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-02b139bc146d5bed\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}