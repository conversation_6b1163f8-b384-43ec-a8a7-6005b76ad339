# WhatsApp Sender Pro

A modern desktop application built with <PERSON><PERSON> and <PERSON><PERSON> for managing WhatsApp messaging campaigns. This application combines the power of Rust backend with a sleek React frontend to provide a robust solution for bulk WhatsApp messaging.

## 🚀 Features

- **Desktop Application**: Cross-platform desktop app built with Tauri
- **WhatsApp Integration**: Native WhatsApp messaging through WhatsMeow Go library
- **Bulk Messaging**: High-performance bulk WhatsApp message sending with rate limiting
- **Authentication System**: QR code authentication with session persistence
- **Contact & Group Management**: Sync contacts and manage WhatsApp groups
- **Media Support**: Send and receive images, videos, audio, and documents
- **Real-time Events**: Live message status updates and presence tracking
- **Modern UI**: React-based frontend with Tailwind CSS and Radix UI components
- **Database Integration**: PostgreSQL database with SeaORM for data persistence
- **Type Safety**: Full TypeScript support across the entire stack
- **Routing**: File-based routing with TanStack Router
- **State Management**: Zustand for client-side state management
- **Form Handling**: React Hook Form with Zod validation
- **Testing**: Comprehensive testing setup with Vitest
- **Code Quality**: Biome for linting and formatting

## 🛠️ Tech Stack

### Frontend

- **React 19** - Modern React with latest features
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **TanStack Router** - Type-safe routing
- **TanStack Query** - Data fetching and caching
- **React Hook Form** - Performant forms with easy validation
- **Zustand** - Lightweight state management
- **Framer Motion** - Animation library

### Backend

- **Tauri** - Rust-based desktop app framework
- **Rust** - Systems programming language
- **WhatsMeow** - Go library for WhatsApp Web API
- **FFI Bindings** - Custom Rust-Go FFI layer for WhatsApp integration
- **SeaORM** - Async ORM for Rust
- **PostgreSQL** - Relational database
- **Tokio** - Async runtime for Rust

### Development Tools

- **Vite** - Fast build tool
- **Biome** - Fast linter and formatter
- **Drizzle Kit** - Database toolkit
- **Vitest** - Fast unit testing framework

## 📋 Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (v18 or higher)
- **pnpm** (recommended package manager)
- **Rust** (latest stable version)
- **Go** (v1.21 or higher) - Required for WhatsApp integration
- **PostgreSQL** (for database)
- **C/C++ Compiler** - Required for FFI bindings (Visual Studio Build Tools on Windows)

## 🚀 Getting Started

### 1. Clone the repository

```bash
git clone <repository-url>
cd whatsapp-sender-pro
```

### 2. Install dependencies

```bash
pnpm install
```

### 3. Set up the database

```bash
# Copy environment file
cp src-tauri/.env.example src-tauri/.env

# Edit the .env file with your database credentials
# Then run database migrations
pnpm db:studio
```

### 4. Development

#### Start the development server

```bash
pnpm dev
```

This will start both the Vite development server and the Tauri development environment.

#### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm test` - Run tests
- `pnpm lint` - Lint code with Biome
- `pnpm format` - Format code with Biome
- `pnpm check` - Run Biome checks
- `pnpm typecheck` - Run TypeScript type checking
- `pnpm db:studio` - Open Drizzle Studio for database management

## 🏗️ Project Structure

```
whatsapp-sender-pro/
├── src/                    # React frontend source
│   ├── api/               # API layer
│   ├── components/        # Reusable UI components
│   ├── lib/              # Utility libraries
│   ├── routes/           # Application routes
│   └── main.tsx          # Application entry point
├── src-tauri/            # Tauri backend
│   ├── src/              # Main Rust application code
│   ├── crates/           # Custom Rust crates
│   │   ├── config/       # Application configuration
│   │   └── whatsmeow-ffi/ # WhatsApp integration FFI bindings
│   │       ├── src/      # Rust FFI implementation
│   │       ├── go-src/   # Go library source (WhatsMeow)
│   │       └── examples/ # Usage examples
│   ├── db/               # Database entities and migrations
│   │   ├── entity/       # SeaORM entities
│   │   ├── migration/    # Database migrations
│   │   └── service/      # Database services
│   └── Cargo.toml        # Rust dependencies
├── public/               # Static assets
└── dist/                 # Build output
```

## 📱 WhatsApp Integration

The application includes a powerful WhatsApp integration through the `whatsmeow_ffi` crate, which provides FFI bindings to the WhatsMeow Go library.

### Key Features

- **QR Code Authentication**: Secure authentication using WhatsApp's QR code system
- **Session Management**: Persistent sessions with automatic reconnection
- **Bulk Messaging**: Send messages to multiple contacts with rate limiting
- **Media Support**: Send and receive images, videos, audio files, and documents
- **Contact Sync**: Synchronize contacts from WhatsApp
- **Group Management**: Create and manage WhatsApp groups
- **Real-time Events**: Live updates for message delivery, read receipts, and presence

### Authentication Flow

1. **Initial Setup**: The app generates a QR code for WhatsApp authentication
2. **QR Scanning**: User scans the QR code with their WhatsApp mobile app
3. **Session Storage**: Authentication session is securely stored in the database
4. **Auto-reconnect**: The app automatically reconnects using stored session data

### Usage Examples

The WhatsApp functionality is integrated into the Tauri backend and exposed through commands. Here are some key operations:

#### Send Text Message
```rust
// Backend implementation available through Tauri commands
client.send_text_message("1234567890", "Hello from WhatsApp Sender Pro!").await?;
```

#### Bulk Messaging
```rust
// Start a bulk messaging campaign
let recipients = vec!["1234567890", "0987654321"];
let job_id = client.start_bulk_message_job(
    "Campaign Name",
    "Your message content",
    &recipients
).await?;
```

#### Media Messages
```rust
// Send media with caption
client.send_media_message(
    "1234567890",
    "/path/to/image.jpg",
    Some("Check this out!")
).await?;
```

For detailed API documentation and examples, see the [WhatsApp FFI documentation](src-tauri/crates/whatsmeow-ffi/README.md).

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the `src-tauri` directory with the following variables:

```env
DATABASE_URL=postgresql://username:password@localhost/whatsapp_sender_pro
WHATSAPP_SESSION_DIR=./sessions
WHATSAPP_MEDIA_DIR=./media
WHATSAPP_DEBUG=false
```

### Database Setup

The application uses PostgreSQL with SeaORM. Database migrations are handled automatically during development.

### WhatsApp Configuration

The WhatsApp client can be configured through the application settings:

- **Device Name**: How your device appears in WhatsApp
- **Session Directory**: Where authentication sessions are stored
- **Media Directory**: Where downloaded media files are saved
- **Rate Limiting**: Configure message sending limits to avoid blocks

## 🧪 Testing

Run the test suite:

```bash
pnpm test
```

The project uses Vitest for unit testing with React Testing Library for component testing.

## 📦 Building for Production

### Build the application

```bash
pnpm build
```

### Build Tauri application

```bash
pnpm tauri build
```

This will create platform-specific installers in the `src-tauri/target/release/bundle/` directory.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 Code Style

This project uses Biome for code formatting and linting. Run the following commands to maintain code quality:

```bash
pnpm format  # Format code
pnpm lint    # Lint code
pnpm check   # Run all checks
```

## 📄 License

This project is private and proprietary.

## 🆘 Support

If you encounter any issues or have questions, please create an issue in the repository.

---

Built with ❤️ using Tauri and React
