use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::time::{sleep, Duration};
use whatsapp_ffi_client::core::{
    entities::{ConnectionStatus, WhatsAppEvent},
    errors::Result,
    event_aggregator::{
        EventAggregator, EventAggregatorConfig, EventFilter, EventProcessor, EventStore,
        EventTransformation, EventType, InMemoryEventStore, ProcessedEvent,
    },
};

// Custom event processor example
struct LoggingEventProcessor {
    name: String,
}

#[async_trait]
impl EventProcessor for LoggingEventProcessor {
    async fn process(&self, event: &WhatsAppEvent) -> Result<Option<ProcessedEvent>> {
        println!("🔄 Processing event: {:?}", event);

        // Create a processed event with some custom metadata
        let processed_event = ProcessedEvent {
            id: format!("processed_{}", uuid::Uuid::new_v4()),
            event_type: EventType::from(event),
            original_event: event.clone(),
            transformed_event: None,
            timestamp: chrono::Utc::now(),
            processing_metadata:
                whatsapp_ffi_client::core::event_aggregator::EventProcessingMetadata {
                    subscription_id: None,
                    route_id: None,
                    transformation_applied: false,
                    processing_duration_ms: 10, // Simulated processing time
                    retry_count: 0,
                    error: None,
                },
        };

        Ok(Some(processed_event))
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn can_process(&self, _event_type: &EventType) -> bool {
        true // Process all event types
    }

    fn priority(&self) -> i32 {
        10 // High priority
    }
}

// Simple UUID implementation for the example
mod uuid {
    use std::fmt;

    pub struct Uuid([u8; 16]);

    impl Uuid {
        pub fn new_v4() -> Self {
            let mut bytes = [0u8; 16];
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_nanos() as u64;

            for (i, byte) in bytes.iter_mut().enumerate() {
                *byte = ((now.wrapping_add(i as u64))
                    .wrapping_mul(23)
                    .wrapping_add(67)) as u8;
            }
            Self(bytes)
        }
    }

    impl fmt::Display for Uuid {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            write!(f, "{:02x}{:02x}{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}{:02x}{:02x}{:02x}{:02x}",
                self.0[0], self.0[1], self.0[2], self.0[3],
                self.0[4], self.0[5],
                self.0[6], self.0[7],
                self.0[8], self.0[9],
                self.0[10], self.0[11], self.0[12], self.0[13], self.0[14], self.0[15]
            )
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing for logging
    tracing_subscriber::fmt::init();

    println!("🚀 Event Aggregator Usage Example");
    println!("==================================");

    // Create event aggregator configuration
    let config = EventAggregatorConfig {
        max_buffer_size: 1000,
        enable_persistence: true,
        cleanup_interval_seconds: 60,
        max_event_age_seconds: 3600,
        enable_batching: false,
        batch_size: 10,
        batch_timeout_ms: 100,
    };

    // Create event store
    let event_store = Arc::new(InMemoryEventStore::default());

    // Create event aggregator
    let aggregator = EventAggregator::new(config, event_store.clone());

    // Add a custom event processor
    let logging_processor = LoggingEventProcessor {
        name: "logging_processor".to_string(),
    };
    aggregator
        .add_processor(Box::new(logging_processor))
        .await?;

    // Start the event aggregator
    aggregator.start().await?;
    println!("✅ Event aggregator started");

    // Get event sender for publishing events
    let event_sender = aggregator.get_event_sender();

    // Example 1: Subscribe to all events
    println!("\n📡 Example 1: Subscribe to all events");
    let (all_events_sub_id, mut all_events_receiver) =
        aggregator.subscribe(EventFilter::All, None).await?;
    println!("✅ Subscribed to all events with ID: {}", all_events_sub_id);

    // Example 2: Subscribe to specific event types
    println!("\n📡 Example 2: Subscribe to connection events only");
    let (connection_sub_id, mut connection_receiver) = aggregator
        .subscribe(
            EventFilter::ByType(EventType::ConnectionStatusChanged),
            None,
        )
        .await?;
    println!(
        "✅ Subscribed to connection events with ID: {}",
        connection_sub_id
    );

    // Example 3: Subscribe with transformation
    println!("\n📡 Example 3: Subscribe with event enrichment");
    let mut enrichment_fields = HashMap::new();
    enrichment_fields.insert(
        "source".to_string(),
        serde_json::Value::String("example_app".to_string()),
    );
    enrichment_fields.insert(
        "version".to_string(),
        serde_json::Value::String("1.0.0".to_string()),
    );

    let transformation = EventTransformation::Enrich {
        additional_fields: enrichment_fields,
    };

    let (enriched_sub_id, mut enriched_receiver) = aggregator
        .subscribe(
            EventFilter::ByType(EventType::QRCodeGenerated),
            Some(transformation),
        )
        .await?;
    println!(
        "✅ Subscribed to QR events with enrichment, ID: {}",
        enriched_sub_id
    );

    // Example 4: Complex filtering
    println!("\n📡 Example 4: Subscribe with complex filtering");
    let complex_filter = EventFilter::Or(vec![
        EventFilter::ByType(EventType::Error),
        EventFilter::ByType(EventType::QRCodeGenerated),
    ]);

    let (complex_sub_id, mut complex_receiver) = aggregator.subscribe(complex_filter, None).await?;
    println!("✅ Subscribed with complex filter, ID: {}", complex_sub_id);

    // Spawn tasks to handle received events
    let all_events_task = tokio::spawn(async move {
        let mut count = 0;
        while let Some(event) = all_events_receiver.recv().await {
            count += 1;
            println!("📥 [ALL] Received event #{}: {:?}", count, event.event_type);
            if count >= 5 {
                break;
            } // Limit for demo
        }
    });

    let connection_events_task = tokio::spawn(async move {
        let mut count = 0;
        while let Some(event) = connection_receiver.recv().await {
            count += 1;
            println!(
                "📥 [CONNECTION] Received event #{}: {:?}",
                count, event.event_type
            );
            if count >= 2 {
                break;
            } // Limit for demo
        }
    });

    let enriched_events_task = tokio::spawn(async move {
        let mut count = 0;
        while let Some(event) = enriched_receiver.recv().await {
            count += 1;
            println!(
                "📥 [ENRICHED] Received event #{}: {:?}",
                count, event.event_type
            );
            println!(
                "    Transformation applied: {}",
                event.processing_metadata.transformation_applied
            );
            if let Some(transformed) = &event.transformed_event {
                println!("    Transformed data: {}", transformed);
            }
            if count >= 1 {
                break;
            } // Limit for demo
        }
    });

    let complex_events_task = tokio::spawn(async move {
        let mut count = 0;
        while let Some(event) = complex_receiver.recv().await {
            count += 1;
            println!(
                "📥 [COMPLEX] Received event #{}: {:?}",
                count, event.event_type
            );
            if count >= 2 {
                break;
            } // Limit for demo
        }
    });

    // Send various events
    println!("\n📤 Sending events...");

    // Send connection status events
    event_sender
        .send(WhatsAppEvent::ConnectionStatusChanged {
            status: ConnectionStatus::Connecting,
            reason: Some("Initializing connection".to_string()),
        })
        .unwrap();

    event_sender
        .send(WhatsAppEvent::ConnectionStatusChanged {
            status: ConnectionStatus::Connected,
            reason: None,
        })
        .unwrap();

    // Send QR code event
    event_sender
        .send(WhatsAppEvent::QRCode("sample_qr_code_data".to_string()))
        .unwrap();

    // Send error event
    event_sender
        .send(WhatsAppEvent::Error {
            code: 404,
            message: "Sample error for demonstration".to_string(),
        })
        .unwrap();

    // Send another QR code event
    event_sender
        .send(WhatsAppEvent::QRCode("another_qr_code".to_string()))
        .unwrap();

    // Wait for event processing
    println!("⏳ Waiting for event processing...");
    sleep(Duration::from_millis(500)).await;

    // Wait for all tasks to complete
    let _ = tokio::join!(
        all_events_task,
        connection_events_task,
        enriched_events_task,
        complex_events_task
    );

    // Show aggregator statistics
    println!("\n📊 Event Aggregator Statistics:");
    let stats = aggregator.get_stats();
    println!("  Total events processed: {}", stats.total_events_processed);
    println!("  Active subscriptions: {}", stats.active_subscriptions);
    println!(
        "  Total subscriptions created: {}",
        stats.total_subscriptions_created
    );
    println!("  Processing errors: {}", stats.processing_errors);
    println!(
        "  Average processing time: {:.2}ms",
        stats.average_processing_time_ms
    );

    if let Some(last_event) = stats.last_event_timestamp {
        println!("  Last event timestamp: {}", last_event);
    }

    println!("\n  Events by type:");
    for (event_type, count) in &stats.events_by_type {
        println!("    {:?}: {}", event_type, count);
    }

    // Demonstrate event persistence and replay
    println!("\n🔄 Event Persistence and Replay:");
    let stored_count = event_store.get_event_count().await?;
    println!("  Events stored: {}", stored_count);

    // Replay all events
    let replayed_events = aggregator.replay_events(EventFilter::All, None).await?;
    println!("  Replayed {} events:", replayed_events.len());
    for (i, event) in replayed_events.iter().enumerate() {
        println!(
            "    {}: {:?} at {}",
            i + 1,
            event.event_type,
            event.timestamp
        );
    }

    // Replay only QR code events
    let qr_events = aggregator
        .replay_events(EventFilter::ByType(EventType::QRCodeGenerated), None)
        .await?;
    println!("  QR code events: {}", qr_events.len());

    // Unsubscribe from some subscriptions
    println!("\n🔌 Unsubscribing from events...");
    if let Err(e) = aggregator.unsubscribe(&connection_sub_id).await {
        println!("  Warning: Failed to unsubscribe connection events: {}", e);
    }
    if let Err(e) = aggregator.unsubscribe(&enriched_sub_id).await {
        println!("  Warning: Failed to unsubscribe enriched events: {}", e);
    }

    let final_stats = aggregator.get_stats();
    println!(
        "  Active subscriptions after unsubscribe: {}",
        final_stats.active_subscriptions
    );

    // Shutdown the aggregator
    println!("\n🛑 Shutting down event aggregator...");
    aggregator.shutdown().await?;

    println!("✅ Event aggregator example completed successfully!");
    Ok(())
}
