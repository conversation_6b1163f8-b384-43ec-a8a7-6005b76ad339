{"rustc": 16591470773350601817, "features": "[\"bigdecimal\", \"chrono\", \"postgres-array\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-postgres\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"ipnetwork\", \"mac_address\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-any\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 2357794343378131723, "profile": 2241668132362809309, "path": 10650141606276709978, "deps": [[2995469292676432503, "uuid", false, 13329635685914659949], [7161281228672193341, "sea_query", false, 6679749599639037576], [9897246384292347999, "chrono", false, 8330261631647650351], [12409575957772518135, "time", false, 6202611441483771785], [14647456484942590313, "bigdecimal", false, 1477368331990517333], [16119793329258425851, "rust_decimal", false, 13035123693571945880], [16362055519698394275, "serde_json", false, 10974075219193167317], [17982831385697850842, "sqlx", false, 1919210647607020814]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-query-binder-d00669ae79322129\\dep-lib-sea_query_binder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}