{"name": "whatsapp-sender-pro", "private": true, "type": "module", "scripts": {"dev": "vite --port 1420", "start": "vite --port 1420", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "format": "biome format", "lint": "biome lint", "check": "biome check", "typecheck": "npx tsc --noEmit", "tauri": "tauri", "db:studio": "drizzle-kit studio"}, "dependencies": {"@faker-js/faker": "9.9.0", "@hookform/resolvers": "5.2.1", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "1.2.7", "@tailwindcss/vite": "4.1.11", "@tanstack/match-sorter-utils": "8.19.4", "@tanstack/react-query": "5.84.2", "@tanstack/react-query-devtools": "5.84.2", "@tanstack/react-router": "1.131.5", "@tanstack/react-router-devtools": "1.131.5", "@tanstack/react-table": "8.21.3", "@tanstack/react-virtual": "3.13.12", "@tanstack/router-plugin": "1.131.5", "@tauri-apps/api": "2.7.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "framer-motion": "12.23.12", "fuse.js": "7.1.0", "humantime": "2.4.0", "immer": "10.1.1", "lucide-react": "0.539.0", "next-themes": "0.4.6", "react": "19.1.1", "react-day-picker": "9.8.1", "react-dom": "19.1.1", "react-hook-form": "7.62.0", "recharts": "3.1.2", "sonner": "2.0.7", "swapy": "1.0.5", "tailwind-merge": "3.3.1", "tailwindcss": "4.1.11", "tailwindcss-animate": "1.0.7", "zod": "4.0.17", "zustand": "5.0.7"}, "devDependencies": {"@biomejs/biome": "2.1.4", "@tauri-apps/cli": "2.7.1", "@testing-library/dom": "10.4.1", "@testing-library/react": "16.3.0", "@types/node": "24.2.1", "@types/pg": "8.15.5", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "@vitejs/plugin-react": "5.0.0", "csv-parse": "6.1.0", "dotenv": "17.2.1", "drizzle-kit": "0.31.4", "drizzle-orm": "0.44.4", "jsdom": "26.1.0", "pg": "8.16.3", "prettier": "3.6.2", "prettier-plugin-tailwindcss": "0.6.14", "typescript": "5.9.2", "uuid": "11.1.0", "vite": "7.1.1", "vitest": "3.2.4", "web-vitals": "5.1.0"}}