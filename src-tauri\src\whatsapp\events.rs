//! Event emission for Tauri frontend integration

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tauri::{AppHandle, Emitter};
use tracing::{error, info, instrument};
use whatsapp_ffi_client::{ConnectionStatus, Message};

use super::state::ServiceStatus;

/// Event names for Tauri event emission
pub mod event_names {
    pub const QR_CODE: &str = "whatsapp:qr-code";
    pub const MESSAGE_RECEIVED: &str = "whatsapp:message-received";
    pub const MESSAGE_SENT: &str = "whatsapp:message-sent";
    pub const CONNECTION_STATUS_CHANGED: &str = "whatsapp:connection-status-changed";
    pub const SERVICE_STATUS_CHANGED: &str = "whatsapp:service-status-changed";
    pub const SERVICE_ERROR: &str = "whatsapp:service-error";
}

/// QR code event payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QRCodeEvent {
    pub qr_code: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Message received event payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageReceivedEvent {
    pub from: String,
    pub message: String,
    pub message_id: String,
    pub timestamp: i64,
}

/// Message sent event payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageSentEvent {
    pub to: String,
    pub message: String,
    pub message_id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Connection status changed event payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionStatusChangedEvent {
    pub status: ConnectionStatus,
    pub reason: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Service status changed event payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceStatusChangedEvent {
    pub status: ServiceStatus,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Service error event payload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceErrorEvent {
    pub error: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Enum for different event emitter implementations
#[derive(Debug)]
pub enum EventEmitter {
    Tauri(TauriEventEmitterImpl),
    Mock(MockEventEmitter),
}

impl EventEmitter {
    /// Emit QR code event
    pub async fn emit_qr_code(&self, qr_code: &str) {
        match self {
            EventEmitter::Tauri(emitter) => emitter.emit_qr_code(qr_code).await,
            EventEmitter::Mock(emitter) => emitter.emit_qr_code(qr_code).await,
        }
    }

    /// Emit message received event
    pub async fn emit_message_received(&self, message: &Message) {
        match self {
            EventEmitter::Tauri(emitter) => emitter.emit_message_received(message).await,
            EventEmitter::Mock(emitter) => emitter.emit_message_received(message).await,
        }
    }

    /// Emit message sent event
    pub async fn emit_message_sent(&self, to: &str, message: &str, message_id: &str) {
        match self {
            EventEmitter::Tauri(emitter) => {
                emitter.emit_message_sent(to, message, message_id).await
            }
            EventEmitter::Mock(emitter) => emitter.emit_message_sent(to, message, message_id).await,
        }
    }

    /// Emit connection status changed event
    pub async fn emit_connection_status_changed(
        &self,
        status: ConnectionStatus,
        reason: Option<String>,
    ) {
        match self {
            EventEmitter::Tauri(emitter) => {
                emitter.emit_connection_status_changed(status, reason).await
            }
            EventEmitter::Mock(emitter) => {
                emitter.emit_connection_status_changed(status, reason).await
            }
        }
    }

    /// Emit service status changed event
    pub async fn emit_service_status_changed(&self, status: ServiceStatus) {
        match self {
            EventEmitter::Tauri(emitter) => emitter.emit_service_status_changed(status).await,
            EventEmitter::Mock(emitter) => emitter.emit_service_status_changed(status).await,
        }
    }

    /// Emit service error event
    pub async fn emit_service_error(&self, error: &str) {
        match self {
            EventEmitter::Tauri(emitter) => emitter.emit_service_error(error).await,
            EventEmitter::Mock(emitter) => emitter.emit_service_error(error).await,
        }
    }
}

/// Trait for emitting events to the Tauri frontend
#[async_trait]
pub trait TauriEventEmitter: Send + Sync {
    /// Emit QR code event
    async fn emit_qr_code(&self, qr_code: &str);

    /// Emit message received event
    async fn emit_message_received(&self, message: &Message);

    /// Emit message sent event
    async fn emit_message_sent(&self, to: &str, message: &str, message_id: &str);

    /// Emit connection status changed event
    async fn emit_connection_status_changed(
        &self,
        status: ConnectionStatus,
        reason: Option<String>,
    );

    /// Emit service status changed event
    async fn emit_service_status_changed(&self, status: ServiceStatus);

    /// Emit service error event
    async fn emit_service_error(&self, error: &str);
}

/// Tauri event emitter implementation
#[derive(Debug)]
pub struct TauriEventEmitterImpl {
    app_handle: AppHandle,
}

impl TauriEventEmitterImpl {
    /// Create a new Tauri event emitter
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle }
    }
}

#[async_trait]
impl TauriEventEmitter for TauriEventEmitterImpl {
    #[instrument(skip(self, qr_code))]
    async fn emit_qr_code(&self, qr_code: &str) {
        let event = QRCodeEvent {
            qr_code: qr_code.to_string(),
            timestamp: chrono::Utc::now(),
        };

        if let Err(e) = self.app_handle.emit(event_names::QR_CODE, &event) {
            error!("Failed to emit QR code event: {}", e);
        } else {
            info!("Emitted QR code event");
        }
    }

    #[instrument(skip(self, message))]
    async fn emit_message_received(&self, message: &Message) {
        let event = MessageReceivedEvent {
            from: message.from.clone(),
            message: message.text(),
            message_id: message.message_id.clone(),
            timestamp: message.timestamp,
        };

        if let Err(e) = self.app_handle.emit(event_names::MESSAGE_RECEIVED, &event) {
            error!("Failed to emit message received event: {}", e);
        } else {
            info!("Emitted message received event from {}", message.from);
        }
    }

    #[instrument(skip(self, to, message, message_id))]
    async fn emit_message_sent(&self, to: &str, message: &str, message_id: &str) {
        let event = MessageSentEvent {
            to: to.to_string(),
            message: message.to_string(),
            message_id: message_id.to_string(),
            timestamp: chrono::Utc::now(),
        };

        if let Err(e) = self.app_handle.emit(event_names::MESSAGE_SENT, &event) {
            error!("Failed to emit message sent event: {}", e);
        } else {
            info!("Emitted message sent event to {}", to);
        }
    }

    #[instrument(skip(self))]
    async fn emit_connection_status_changed(
        &self,
        status: ConnectionStatus,
        reason: Option<String>,
    ) {
        let event = ConnectionStatusChangedEvent {
            status,
            reason,
            timestamp: chrono::Utc::now(),
        };

        if let Err(e) = self
            .app_handle
            .emit(event_names::CONNECTION_STATUS_CHANGED, &event)
        {
            error!("Failed to emit connection status changed event: {}", e);
        } else {
            info!("Emitted connection status changed event: {:?}", status);
        }
    }

    #[instrument(skip(self))]
    async fn emit_service_status_changed(&self, status: ServiceStatus) {
        let event = ServiceStatusChangedEvent {
            status,
            timestamp: chrono::Utc::now(),
        };

        if let Err(e) = self
            .app_handle
            .emit(event_names::SERVICE_STATUS_CHANGED, &event)
        {
            error!("Failed to emit service status changed event: {}", e);
        } else {
            info!("Emitted service status changed event: {:?}", status);
        }
    }

    #[instrument(skip(self, error))]
    async fn emit_service_error(&self, error: &str) {
        let event = ServiceErrorEvent {
            error: error.to_string(),
            timestamp: chrono::Utc::now(),
        };

        if let Err(e) = self.app_handle.emit(event_names::SERVICE_ERROR, &event) {
            error!("Failed to emit service error event: {}", e);
        } else {
            info!("Emitted service error event: {}", error);
        }
    }
}

/// Mock event emitter for testing
#[derive(Debug, Default)]
pub struct MockEventEmitter {
    pub events: Arc<tokio::sync::Mutex<Vec<String>>>,
}

impl MockEventEmitter {
    pub fn new() -> Self {
        Self {
            events: Arc::new(tokio::sync::Mutex::new(Vec::new())),
        }
    }

    pub async fn get_events(&self) -> Vec<String> {
        self.events.lock().await.clone()
    }

    pub async fn clear_events(&self) {
        self.events.lock().await.clear();
    }

    async fn record_event(&self, event_name: &str) {
        self.events.lock().await.push(event_name.to_string());
    }
}

#[async_trait]
impl TauriEventEmitter for MockEventEmitter {
    async fn emit_qr_code(&self, _qr_code: &str) {
        self.record_event("qr_code").await;
    }

    async fn emit_message_received(&self, _message: &Message) {
        self.record_event("message_received").await;
    }

    async fn emit_message_sent(&self, _to: &str, _message: &str, _message_id: &str) {
        self.record_event("message_sent").await;
    }

    async fn emit_connection_status_changed(
        &self,
        _status: ConnectionStatus,
        _reason: Option<String>,
    ) {
        self.record_event("connection_status_changed").await;
    }

    async fn emit_service_status_changed(&self, _status: ServiceStatus) {
        self.record_event("service_status_changed").await;
    }

    async fn emit_service_error(&self, _error: &str) {
        self.record_event("service_error").await;
    }
}
