//! Unit tests for WhatsApp service lifecycle management

use std::sync::Arc;
use tokio::time::{Duration, sleep};
use whatsapp_ffi_client::{ConnectionStatus, LogLevel};

use crate::whatsapp::{
    events::{EventEmitter, MockEventEmitter},
    service::WhatsAppService,
    state::{ServiceConfig, ServiceStatus},
};

/// Create a test service with mock event emitter
fn create_test_service() -> (Arc<WhatsAppService>, Arc<MockEventEmitter>) {
    let config = ServiceConfig::new("test_library.dll".to_string(), "test.db".to_string())
        .with_log_level(LogLevel::Debug)
        .with_auto_reconnect(false);

    let mock_emitter = Arc::new(MockEventEmitter::new());
    let event_emitter = Arc::new(EventEmitter::Mock(MockEventEmitter::new()));
    let service = Arc::new(WhatsAppService::new(config, event_emitter));

    (service, mock_emitter)
}

#[tokio::test]
async fn test_service_creation() {
    let (service, _) = create_test_service();

    let state = service.get_state().await;
    assert_eq!(state.status(), ServiceStatus::NotInitialized);
    assert_eq!(state.connection_status(), ConnectionStatus::Disconnected);
    assert!(state.last_error().is_none());
}

#[tokio::test]
async fn test_service_state_transitions() {
    let (service, _event_emitter) = create_test_service();

    // Initial state
    let state = service.get_state().await;
    assert_eq!(state.status(), ServiceStatus::NotInitialized);

    // Note: We can't test actual initialization without a real FFI library
    // This test focuses on state management logic

    // Test configuration retrieval
    let config = service.get_config().await;
    assert_eq!(config.library_path, "test_library.dll");
    assert_eq!(config.db_path, "test.db");
    assert_eq!(config.log_level, LogLevel::Debug);
    assert!(!config.auto_reconnect);
}

#[tokio::test]
async fn test_service_config_update() {
    let (service, _) = create_test_service();

    let mut new_config = service.get_config().await;
    new_config.log_level = LogLevel::Error;
    new_config.auto_reconnect = true;
    new_config.max_reconnect_attempts = 10;

    // Update configuration
    let result = service.update_config(new_config.clone()).await;
    assert!(result.is_ok());

    // Verify configuration was updated
    let updated_config = service.get_config().await;
    assert_eq!(updated_config.log_level, LogLevel::Error);
    assert!(updated_config.auto_reconnect);
    assert_eq!(updated_config.max_reconnect_attempts, 10);
}

#[tokio::test]
async fn test_service_config_validation() {
    let (service, _) = create_test_service();

    // Test invalid configuration
    let invalid_config = ServiceConfig::new(
        "".to_string(), // Empty library path
        "test.db".to_string(),
    );

    let result = service.update_config(invalid_config).await;
    assert!(result.is_err());

    // Verify original configuration is unchanged
    let config = service.get_config().await;
    assert_eq!(config.library_path, "test_library.dll");
}

#[tokio::test]
async fn test_service_connection_status() {
    let (service, _) = create_test_service();

    // Initially not connected
    assert!(!service.is_connected().await);
    assert_eq!(
        service.get_connection_status().await,
        ConnectionStatus::Disconnected
    );
}

#[tokio::test]
async fn test_service_error_handling() {
    let (service, event_emitter) = create_test_service();

    // Test sending message without initialization (should fail)
    let result = service.send_message("1234567890", "test message").await;
    assert!(result.is_err());

    // Verify error was recorded
    let _events = event_emitter.get_events().await;
    // Note: In a real scenario, this would emit an error event
    // For now, we just verify the operation failed
}

#[tokio::test]
async fn test_service_shutdown() {
    let (service, event_emitter) = create_test_service();

    // Test shutdown
    let result = service.shutdown().await;
    assert!(result.is_ok());

    // Verify shutdown event was emitted
    let events = event_emitter.get_events().await;
    assert!(events.contains(&"service_status_changed".to_string()));

    // Verify final state
    let state = service.get_state().await;
    assert_eq!(state.status(), ServiceStatus::Shutdown);
}

#[tokio::test]
async fn test_service_state_statistics() {
    let (service, _) = create_test_service();

    let state = service.get_state().await;
    let stats = state.get_stats();

    assert_eq!(stats.connection_attempts, 0);
    assert_eq!(stats.messages_sent, 0);
    assert_eq!(stats.messages_received, 0);
    assert!(stats.is_healthy);
}

#[tokio::test]
async fn test_service_concurrent_operations() {
    let (service, _) = create_test_service();

    // Test concurrent state access
    let handles: Vec<_> = (0..10)
        .map(|_| {
            let service = Arc::clone(&service);
            tokio::spawn(async move { service.get_state().await })
        })
        .collect();

    // Wait for all tasks to complete
    for handle in handles {
        let state = handle.await.unwrap();
        assert_eq!(state.status(), ServiceStatus::NotInitialized);
    }
}

#[tokio::test]
async fn test_service_config_thread_safety() {
    let (service, _) = create_test_service();

    // Test concurrent configuration updates
    let handles: Vec<_> = (0..5)
        .map(|i| {
            let service = Arc::clone(&service);
            tokio::spawn(async move {
                let mut config = service.get_config().await;
                config.max_reconnect_attempts = 10 + i;
                service.update_config(config).await
            })
        })
        .collect();

    // Wait for all updates to complete
    let mut results = Vec::new();
    for handle in handles {
        results.push(handle.await.unwrap());
    }

    // At least one update should succeed
    assert!(results.iter().any(|r| r.is_ok()));

    // Final configuration should be valid
    let final_config = service.get_config().await;
    assert!(final_config.validate().is_ok());
}

#[tokio::test]
async fn test_service_state_persistence() {
    let (service, _) = create_test_service();

    // Get initial state
    let initial_state = service.get_state().await;
    let initial_time = initial_state.uptime();

    // Wait a bit
    sleep(Duration::from_millis(10)).await;

    // Get state again
    let later_state = service.get_state().await;
    let later_time = later_state.uptime();

    // Uptime should have increased
    assert!(later_time > initial_time);

    // Other fields should remain the same
    assert_eq!(initial_state.status(), later_state.status());
    assert_eq!(
        initial_state.connection_status(),
        later_state.connection_status()
    );
}

#[tokio::test]
async fn test_service_error_recovery() {
    let (service, event_emitter) = create_test_service();

    // Clear any initial events
    event_emitter.clear_events().await;

    // Simulate an error scenario by trying to send without connection
    let result = service.send_message("invalid", "test").await;
    assert!(result.is_err());

    // Verify service can still operate (get state, config, etc.)
    let state = service.get_state().await;
    assert_eq!(state.status(), ServiceStatus::NotInitialized);

    let config = service.get_config().await;
    assert!(config.validate().is_ok());
}
