//! Database initialization utilities for automatic database creation.
//!
//! This module provides functionality to automatically create PostgreSQL databases
//! if they don't exist, ensuring robust application startup even when the target
//! database hasn't been manually created.

use std::time::Duration;

use sea_orm::{ConnectionTrait, Database, DatabaseConnection};
use tracing::{error, info, warn};
use url::Url;

use crate::ServiceError;

/// Configuration for database initialization
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    /// The full database URL including the target database name
    pub database_url: String,
    /// The database name to create if it doesn't exist
    pub database_name: String,
    /// The server URL without the database name (for admin operations)
    pub server_url: String,
}

impl DatabaseConfig {
    /// Creates a new DatabaseConfig from a database URL
    ///
    /// # Arguments
    /// * `database_url` - The full PostgreSQL database URL
    ///
    /// # Returns
    /// * `Result<Self, ServiceError>` - New config or error if URL is invalid
    ///
    /// # Errors
    /// Returns `ServiceError` if the URL cannot be parsed or is invalid
    pub fn from_url(database_url: &str) -> Result<Self, ServiceError> {
        let url = Url::parse(database_url)
            .map_err(|e| ServiceError::DatabaseInit(format!("Invalid database URL: {}", e)))?;

        let database_name = url.path().trim_start_matches('/').to_string();

        if database_name.is_empty() {
            return Err(ServiceError::DatabaseInit(
                "Database name not found in URL".to_string(),
            ));
        }

        // Create server URL without database name for admin operations
        let mut server_url = url.clone();
        server_url.set_path("/postgres"); // Connect to default postgres database

        Ok(Self {
            database_url: database_url.to_string(),
            database_name,
            server_url: server_url.to_string(),
        })
    }
}

/// Checks if a database exists on the PostgreSQL server
///
/// # Arguments
/// * `config` - Database configuration containing server connection details
///
/// # Returns
/// * `Result<bool, ServiceError>` - True if database exists, false otherwise
///
/// # Errors
/// Returns `ServiceError` if:
/// - Cannot connect to PostgreSQL server
/// - Query execution fails
/// - Database permissions are insufficient
async fn database_exists(config: &DatabaseConfig) -> Result<bool, ServiceError> {
    info!("Checking if database '{}' exists", config.database_name);

    // Connect to the PostgreSQL server (not the target database)
    let db = Database::connect(&config.server_url).await?;

    // Query to check if database exists
    let query = format!(
        "SELECT 1 FROM pg_database WHERE datname = '{}'",
        config.database_name
    );

    match db.execute_unprepared(&query).await {
        Ok(result) => {
            let exists = result.rows_affected() > 0;
            info!(
                "Database '{}' existence check: {}",
                config.database_name,
                if exists { "exists" } else { "does not exist" }
            );
            Ok(exists)
        }
        Err(e) => {
            error!("Failed to check database existence: {}", e);
            Err(ServiceError::Database(e))
        }
    }
}

/// Creates a new database on the PostgreSQL server
///
/// # Arguments
/// * `config` - Database configuration containing server connection details
///
/// # Returns
/// * `Result<(), ServiceError>` - Success or error
///
/// # Errors
/// Returns `ServiceError` if:
/// - Cannot connect to PostgreSQL server
/// - Database creation fails
/// - Insufficient permissions to create database
async fn create_database(config: &DatabaseConfig) -> Result<(), ServiceError> {
    info!("Creating database '{}'", config.database_name);

    // Connect to the PostgreSQL server
    let db = Database::connect(&config.server_url).await?;

    // Create database query - database names cannot be parameterized
    let query = format!("CREATE DATABASE \"{}\"", config.database_name);

    match db.execute_unprepared(&query).await {
        Ok(_) => {
            info!("Successfully created database '{}'", config.database_name);
            Ok(())
        }
        Err(e) => {
            error!(
                "Failed to create database '{}': {}",
                config.database_name, e
            );
            Err(ServiceError::Database(e))
        }
    }
}

/// Detects collation version mismatches in the database
///
/// This function queries the PostgreSQL system catalogs to identify any
/// collations that have version mismatches, which typically occur after
/// PostgreSQL server upgrades.
///
/// # Arguments
/// * `db` - Database connection to check
///
/// # Returns
/// * `Result<Vec<String>, ServiceError>` - List of collation names with mismatches
///
/// # Errors
/// Returns `ServiceError` if the query fails or connection is invalid
async fn detect_collation_mismatches(db: &DatabaseConnection) -> Result<Vec<String>, ServiceError> {
    info!("Checking for collation version mismatches");

    let query = r#"
        SELECT collname
        FROM pg_collation
        WHERE collversion IS NOT NULL
        AND collversion <> pg_collation_actual_version(oid)
    "#;

    match db.execute_unprepared(query).await {
        Ok(result) => {
            // For collation detection, we just need to know if there are any mismatches
            // The exact count is sufficient for our purposes
            let mismatch_count = result.rows_affected();

            if mismatch_count == 0 {
                info!("No collation version mismatches detected");
                Ok(vec![])
            } else {
                warn!("Found {} collation version mismatches", mismatch_count);
                // Return a placeholder list indicating mismatches were found
                Ok(vec!["collation_mismatch_detected".to_string()])
            }
        }
        Err(e) => {
            warn!(
                "Failed to check collation versions (this may be expected on older PostgreSQL versions): {}",
                e
            );
            // Return empty vec instead of error for compatibility with older PostgreSQL versions
            Ok(vec![])
        }
    }
}

/// Attempts to refresh individual collations that have version mismatches
///
/// This function tries to refresh collations individually using
/// `ALTER COLLATION ... REFRESH VERSION` for each problematic collation.
///
/// # Arguments
/// * `db` - Database connection
///
/// # Returns
/// * `Result<u32, ServiceError>` - Number of collations successfully refreshed
async fn refresh_individual_collations(db: &DatabaseConnection) -> Result<u32, ServiceError> {
    info!("Attempting to refresh individual collations");

    // Get list of collations with mismatches that we can potentially fix
    let query = r#"
        SELECT collname
        FROM pg_collation
        WHERE collversion IS NOT NULL
        AND collversion <> pg_collation_actual_version(oid)
        AND collname NOT LIKE 'pg_%'
        ORDER BY collname
        LIMIT 50
    "#;

    let mut refreshed_count = 0;

    // Try to get individual collation names for targeted refresh
    match db.execute_unprepared(query).await {
        Ok(result) => {
            let mismatch_count = result.rows_affected();
            info!("Found {} individual collations to refresh", mismatch_count);

            // For now, we'll try a few common collation refresh patterns
            let common_collations = vec!["default", "C", "POSIX", "en_US.utf8", "en_US.UTF-8"];

            for collation in common_collations {
                let refresh_query = format!("ALTER COLLATION \"{}\" REFRESH VERSION", collation);
                match db.execute_unprepared(&refresh_query).await {
                    Ok(_) => {
                        info!("Successfully refreshed collation '{}'", collation);
                        refreshed_count += 1;
                    }
                    Err(e) => {
                        // This is expected for collations that don't exist or can't be refreshed
                        warn!("Could not refresh collation '{}': {}", collation, e);
                    }
                }
            }
        }
        Err(e) => {
            warn!("Could not query individual collations: {}", e);
        }
    }

    Ok(refreshed_count)
}

/// Refreshes collation versions for the entire database
///
/// This function tries multiple approaches to refresh collation versions:
/// 1. Database-level refresh (fastest, requires owner privileges)
/// 2. Individual collation refresh (more targeted)
/// 3. Graceful fallback with informative warnings
///
/// # Arguments
/// * `db` - Database connection
/// * `database_name` - Name of the database to refresh
///
/// # Returns
/// * `Result<(), ServiceError>` - Success or error
///
/// # Errors
/// Returns `ServiceError` only for critical failures that should stop initialization
async fn refresh_collation_versions(
    db: &DatabaseConnection,
    database_name: &str,
) -> Result<(), ServiceError> {
    info!(
        "Refreshing collation versions for database '{}'",
        database_name
    );

    // First, try the database-level refresh (requires owner privileges)
    let db_refresh_query = format!(
        "ALTER DATABASE \"{}\" REFRESH COLLATION VERSION",
        database_name
    );

    let mut db_refresh_success = false;
    match db.execute_unprepared(&db_refresh_query).await {
        Ok(_) => {
            info!(
                "Successfully refreshed collation versions for database '{}'",
                database_name
            );
            db_refresh_success = true;
        }
        Err(e) => {
            let error_msg = e.to_string().to_lowercase();
            if error_msg.contains("permission denied") || error_msg.contains("must be owner") {
                warn!(
                    "Insufficient privileges for database-level collation refresh: {}",
                    e
                );
                info!("Attempting individual collation refresh as fallback");
            } else if error_msg.contains("does not exist") {
                warn!(
                    "Database '{}' does not exist for collation refresh",
                    database_name
                );
                return Ok(());
            } else {
                warn!(
                    "Database-level collation refresh failed: {}. Trying individual refresh.",
                    e
                );
            }
        }
    }

    // If database-level refresh failed, try individual collation refresh
    if !db_refresh_success {
        match refresh_individual_collations(db).await {
            Ok(count) => {
                if count > 0 {
                    info!("Successfully refreshed {} individual collations", count);
                } else {
                    warn!("No individual collations could be refreshed");
                    warn!("This may require database owner or superuser privileges");
                    warn!(
                        "Consider running: ALTER DATABASE {} REFRESH COLLATION VERSION;",
                        database_name
                    );
                }
            }
            Err(e) => {
                warn!("Individual collation refresh also failed: {}", e);
            }
        }
    }

    Ok(())
}

/// Fixes collation version mismatches in the database
///
/// This function detects and attempts to fix collation version mismatches
/// by refreshing the collation versions. It handles various error conditions
/// gracefully to avoid breaking the database initialization process.
///
/// # Arguments
/// * `db` - Database connection
/// * `database_name` - Name of the database to fix
///
/// # Returns
/// * `Result<(), ServiceError>` - Success or error
///
/// # Errors
/// Returns `ServiceError` only for critical failures that should stop initialization
pub async fn fix_collation_mismatches(
    db: &DatabaseConnection,
    database_name: &str,
) -> Result<(), ServiceError> {
    info!(
        "Checking and fixing collation version mismatches for database '{}'",
        database_name
    );

    // First, detect if there are any mismatches
    let mismatches = detect_collation_mismatches(db).await?;

    if mismatches.is_empty() {
        info!("No collation version mismatches to fix");
        return Ok(());
    }

    info!(
        "Attempting to fix {} collation version mismatches",
        mismatches.len()
    );

    // Attempt to refresh collation versions
    refresh_collation_versions(db, database_name).await?;

    // Verify the fix worked
    let remaining_mismatches = detect_collation_mismatches(db).await?;
    if remaining_mismatches.is_empty() {
        info!("Successfully fixed all collation version mismatches");
    } else {
        warn!("Some collation version mismatches remain after refresh attempts");
        warn!("This is typically caused by one of the following:");
        warn!("  1. Insufficient database privileges (need database owner or superuser)");
        warn!("  2. System-level collations that require PostgreSQL restart");
        warn!("  3. Collations from extensions that need manual refresh");
        warn!("");
        warn!("To manually fix remaining issues, try running as database owner:");
        warn!(
            "  ALTER DATABASE {} REFRESH COLLATION VERSION;",
            database_name
        );
        warn!("Or connect as superuser and run:");
        warn!("  SELECT pg_reload_conf();");
        warn!("");
        warn!(
            "These warnings are typically non-critical and won't affect normal database operations."
        );
    }

    Ok(())
}

/// Ensures the target database exists, creating it if necessary
///
/// This function performs the following steps:
/// 1. Parses the database URL to extract connection details
/// 2. Checks if the target database exists
/// 3. Creates the database if it doesn't exist
/// 4. Returns a connection to the target database
///
/// # Arguments
/// * `database_url` - The full PostgreSQL database URL
///
/// # Returns
/// * `Result<DatabaseConnection, ServiceError>` - Connection to the target database
///
/// # Errors
/// Returns `ServiceError` if:
/// - Database URL is invalid
/// - Cannot connect to PostgreSQL server
/// - Database creation fails
/// - Cannot connect to the target database after creation
pub async fn ensure_database_exists(
    database_url: &str,
) -> Result<DatabaseConnection, ServiceError> {
    let config = DatabaseConfig::from_url(database_url)?;

    info!(
        "Initializing database connection to '{}'",
        config.database_name
    );

    // Check if database exists
    let exists = database_exists(&config).await?;

    if !exists {
        info!("Database does not exist, creating it...");
        create_database(&config).await?;

        // Wait a moment for the database to be fully created
        tokio::time::sleep(Duration::from_millis(100)).await;
    }

    // Connect to the target database
    info!("Connecting to database '{}'", config.database_name);
    match Database::connect(&config.database_url).await {
        Ok(connection) => {
            info!(
                "Successfully connected to database '{}'",
                config.database_name
            );

            // Fix any collation version mismatches
            match fix_collation_mismatches(&connection, &config.database_name).await {
                Ok(_) => {
                    // Check final status
                    match detect_collation_mismatches(&connection).await {
                        Ok(remaining) if remaining.is_empty() => {
                            info!(
                                "✅ Database initialization complete - no collation issues detected"
                            );
                        }
                        Ok(_) => {
                            info!(
                                "⚠️  Database initialization complete - some collation warnings remain (non-critical)"
                            );
                        }
                        Err(_) => {
                            info!("✅ Database initialization complete");
                        }
                    }
                }
                Err(e) => {
                    warn!("Failed to fix collation version mismatches: {}", e);
                    // Don't fail the entire initialization for collation issues
                    // as they are typically non-critical warnings
                }
            }

            Ok(connection)
        }
        Err(e) => {
            error!(
                "Failed to connect to database '{}' after ensuring it exists: {}",
                config.database_name, e
            );
            Err(ServiceError::Database(e))
        }
    }
}

/// Provides a comprehensive report on database collation status
///
/// This function checks for collation version mismatches and provides
/// detailed information about the database's collation health.
///
/// # Arguments
/// * `db` - Database connection
/// * `database_name` - Name of the database to check
///
/// # Returns
/// * `Result<bool, ServiceError>` - True if no issues found, false if issues remain
///
/// # Example
/// ```rust
/// let db = ensure_database_exists(&database_url).await?;
/// let config = DatabaseConfig::from_url(&database_url)?;
/// let is_healthy = check_collation_health(&db, &config.database_name).await?;
/// ```
pub async fn check_collation_health(
    db: &DatabaseConnection,
    database_name: &str,
) -> Result<bool, ServiceError> {
    info!(
        "🔍 Checking collation health for database '{}'",
        database_name
    );

    let mismatches = detect_collation_mismatches(db).await?;

    if mismatches.is_empty() {
        info!("✅ Collation health check: All collations are up to date");
        return Ok(true);
    }

    warn!("⚠️  Collation health check: Found version mismatches");
    warn!("📊 Database: {}", database_name);
    warn!("🔢 Mismatched collations detected: This typically happens after PostgreSQL upgrades");
    warn!("");
    warn!("🛠️  Recommended actions:");
    warn!(
        "   1. Run as database owner: ALTER DATABASE {} REFRESH COLLATION VERSION;",
        database_name
    );
    warn!("   2. Or use the fix_collation_mismatches() function");
    warn!("   3. For persistent issues, contact your database administrator");
    warn!("");
    warn!("ℹ️  Impact: These are typically warnings and won't affect normal operations");

    Ok(false)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_database_config_from_url() {
        let url = "postgres://user:pass@localhost:5432/test_db";
        let config = DatabaseConfig::from_url(url).unwrap();

        assert_eq!(config.database_name, "test_db");
        assert_eq!(config.database_url, url);
        assert_eq!(
            config.server_url,
            "postgres://user:pass@localhost:5432/postgres"
        );
    }

    #[test]
    fn test_database_config_invalid_url() {
        let result = DatabaseConfig::from_url("invalid-url");
        assert!(result.is_err());
    }

    #[test]
    fn test_database_config_no_database_name() {
        let result = DatabaseConfig::from_url("postgres://user:pass@localhost:5432/");
        assert!(result.is_err());
    }
}
