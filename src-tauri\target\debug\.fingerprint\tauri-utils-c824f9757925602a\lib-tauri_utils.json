{"rustc": 16591470773350601817, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 7612950396392890533, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 15902693800498252048], [2995469292676432503, "uuid", false, 6998241069174797990], [3060637413840920116, "proc_macro2", false, 12441885387897080324], [3129130049864710036, "memchr", false, 2482764352869297290], [3150220818285335163, "url", false, 14195290360083940607], [3191507132440681679, "serde_untagged", false, 8138717335031239247], [4899080583175475170, "semver", false, 5742074698723764310], [5986029879202738730, "log", false, 2237373700540862790], [6213549728662707793, "serde_with", false, 8329642630732836106], [6606131838865521726, "ctor", false, 10319392224565566374], [6913375703034175521, "schemars", false, 2371191611272397952], [7170110829644101142, "json_patch", false, 7408263245480546190], [8786711029710048183, "toml", false, 10048794690708226692], [9010263965687315507, "http", false, 3354175764241350141], [9451456094439810778, "regex", false, 1995452313642458860], [9556762810601084293, "brotli", false, 16181042358721137845], [9689903380558560274, "serde", false, 16503059047051001516], [10806645703491011684, "thiserror", false, 44651847147331727], [11655476559277113544, "cargo_metadata", false, 12679301754122413017], [11989259058781683633, "dunce", false, 13340844955090052542], [13625485746686963219, "anyhow", false, 8308486080552771400], [14232843520438415263, "html5ever", false, 11151851211359691091], [15088007382495681292, "kuchiki", false, 15331819932349615614], [15622660310229662834, "walkdir", false, 14109848318917712597], [16362055519698394275, "serde_json", false, 14102427500185749966], [17146114186171651583, "infer", false, 5478616977701194921], [17155886227862585100, "glob", false, 16192797392537921819], [17186037756130803222, "phf", false, 2290410019749672425], [17990358020177143287, "quote", false, 9492086704218893313]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-c824f9757925602a\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}