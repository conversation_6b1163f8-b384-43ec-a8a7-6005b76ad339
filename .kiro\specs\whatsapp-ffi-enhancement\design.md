# Design Document

## Overview

This design document outlines the enhanced architecture for the WhatsApp Sender Pro FFI implementation and its integration with the Tauri desktop application. The design focuses on improving reliability, error handling, media support, bulk messaging capabilities, and seamless desktop integration while maintaining the existing FFI bridge to the WhatsMeow Go library.

The enhanced system will provide a robust, production-ready WhatsApp messaging solution with comprehensive error handling, real-time event processing, and an intuitive desktop interface.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Tauri Desktop App"
        UI[React Frontend]
        TC[Tauri Commands]
        TE[Tauri Events]
    end
    
    subgraph "Rust Backend"
        WS[WhatsApp Service]
        WC[WhatsApp Client]
        EA[Event Aggregator]
        BM[Bulk Message Manager]
        CM[Connection Manager]
    end
    
    subgraph "FFI Layer"
        WA[WhatsApp Adapter]
        FFI[FFI Bindings]
    end
    
    subgraph "Go Library"
        WM[WhatsMeow Client]
        DB[(SQLite DB)]
    end
    
    UI --> TC
    TC --> WS
    WS --> WC
    WC --> EA
    WC --> BM
    WC --> CM
    EA --> TE
    TE --> UI
    WC --> WA
    WA --> FFI
    FFI --> WM
    WM --> DB
```

### Component Architecture

The system follows a layered architecture with clear separation of concerns:

1. **Presentation Layer**: React frontend with Tauri integration
2. **Service Layer**: WhatsApp service orchestrating business logic
3. **Domain Layer**: Core WhatsApp client with enhanced capabilities
4. **Infrastructure Layer**: FFI adapter and Go library integration
5. **Data Layer**: SQLite database for session and message persistence

## Components and Interfaces

### 1. Enhanced WhatsApp Client

**Location**: `src-tauri/crates/whatsmeow_ffi/src/interface/client.rs`

```rust
pub struct WhatsAppClient {
    adapter: Arc<dyn WhatsAppClientPort + Send + Sync>,
    connection_manager: Arc<ConnectionManager>,
    bulk_manager: Arc<BulkMessageManager>,
    event_aggregator: Arc<EventAggregator>,
    config: ClientConfig,
}

pub struct ClientConfig {
    pub library_path: String,
    pub db_path: String,
    pub rate_limit: RateLimitConfig,
    pub retry_config: RetryConfig,
    pub log_level: LogLevel,
}
```

**Key Enhancements**:
- Connection management with auto-reconnection
- Bulk messaging with rate limiting
- Event aggregation and processing
- Comprehensive error handling
- Resource management and cleanup

### 2. Connection Manager

**Location**: `src-tauri/crates/whatsmeow_ffi/src/core/connection_manager.rs`

```rust
pub struct ConnectionManager {
    status: Arc<RwLock<ConnectionStatus>>,
    reconnect_config: ReconnectConfig,
    session_store: Arc<dyn SessionStore>,
}

pub enum ConnectionStatus {
    Disconnected,
    Connecting,
    Connected,
    Authenticating,
    Authenticated,
    Reconnecting { attempt: u32, next_retry: Instant },
    Failed { error: String, last_attempt: Instant },
}
```

**Responsibilities**:
- Monitor connection health
- Handle automatic reconnection with exponential backoff
- Manage authentication state and session persistence
- Emit connection status events

### 3. Bulk Message Manager

**Location**: `src-tauri/crates/whatsmeow_ffi/src/core/bulk_manager.rs`

```rust
pub struct BulkMessageManager {
    rate_limiter: Arc<RateLimiter>,
    message_queue: Arc<Mutex<VecDeque<BulkMessage>>>,
    progress_tracker: Arc<ProgressTracker>,
    retry_handler: Arc<RetryHandler>,
}

pub struct BulkMessage {
    pub id: String,
    pub recipient: String,
    pub content: MessageContent,
    pub priority: MessagePriority,
    pub retry_count: u32,
    pub scheduled_time: Option<Instant>,
}
```

**Features**:
- Configurable rate limiting (messages per minute/hour)
- Message queuing and scheduling
- Progress tracking and callbacks
- Retry logic with exponential backoff
- Priority-based message processing

### 4. Event Aggregator

**Location**: `src-tauri/crates/whatsmeow_ffi/src/core/event_aggregator.rs`

```rust
pub struct EventAggregator {
    event_bus: Arc<EventBus>,
    processors: Vec<Box<dyn EventProcessor>>,
    tauri_emitter: Option<Arc<dyn TauriEventEmitter>>,
}

pub enum WhatsAppEvent {
    ConnectionStatusChanged(ConnectionStatus),
    MessageReceived(IncomingMessage),
    MessageStatusUpdate(MessageStatusUpdate),
    QRCodeGenerated(String),
    AuthenticationRequired,
    ContactPresenceUpdate(PresenceUpdate),
    BulkOperationProgress(BulkProgress),
    Error(WhatsAppError),
}
```

**Capabilities**:
- Event processing and filtering
- Real-time event emission to Tauri frontend
- Event persistence for offline scenarios
- Custom event processors for business logic

### 5. Tauri Integration Layer

**Location**: `src-tauri/src/whatsapp/mod.rs`

```rust
pub struct WhatsAppService {
    client: Arc<WhatsAppClient>,
    app_handle: AppHandle,
    state: Arc<RwLock<ServiceState>>,
}

#[tauri::command]
pub async fn whatsapp_connect(
    service: State<'_, WhatsAppService>,
) -> Result<(), WhatsAppError> {
    service.client.connect().await
}

#[tauri::command]
pub async fn whatsapp_send_message(
    service: State<'_, WhatsAppService>,
    recipient: String,
    content: MessageContent,
) -> Result<String, WhatsAppError> {
    service.client.send_message_content(&recipient, &content).await
}
```

**Tauri Commands**:
- `whatsapp_connect()` - Initialize WhatsApp connection
- `whatsapp_disconnect()` - Gracefully disconnect
- `whatsapp_send_message()` - Send individual messages
- `whatsapp_send_bulk_messages()` - Send bulk messages
- `whatsapp_get_connection_status()` - Get current status
- `whatsapp_get_contacts()` - Retrieve contact list
- `whatsapp_create_group()` - Create WhatsApp group
- `whatsapp_get_qr_code()` - Get authentication QR code

### 6. Enhanced Error Handling

**Location**: `src-tauri/crates/whatsmeow_ffi/src/core/errors.rs`

```rust
#[derive(Debug, thiserror::Error, serde::Serialize)]
pub enum WhatsAppError {
    #[error("Connection error: {message}")]
    Connection { message: String, code: i32 },
    
    #[error("Authentication error: {message}")]
    Authentication { message: String, retry_after: Option<Duration> },
    
    #[error("Rate limit exceeded: {message}")]
    RateLimit { message: String, retry_after: Duration },
    
    #[error("Media error: {message}")]
    Media { message: String, file_path: Option<String> },
    
    #[error("FFI error: {message}")]
    Ffi { message: String, error_code: i32 },
    
    #[error("Internal error: {message}")]
    Internal { message: String },
}
```

**Error Context**:
- Detailed error messages with context
- Error codes for programmatic handling
- Retry information for recoverable errors
- Structured error data for frontend display

## Data Models

### 1. Message Content Types

```rust
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub enum MessageContent {
    Text {
        text: String,
        quoted_message_id: Option<String>,
        mentioned_numbers: Option<Vec<String>>,
    },
    Image {
        source: MediaSource,
        caption: Option<String>,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
    },
    Video {
        source: MediaSource,
        caption: Option<String>,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
        duration: Option<u32>,
    },
    Audio {
        source: MediaSource,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
        duration: Option<u32>,
        is_voice: bool,
    },
    Document {
        source: MediaSource,
        caption: Option<String>,
        quoted_message_id: Option<String>,
        mime_type: Option<String>,
        filename: Option<String>,
    },
    Sticker {
        source: MediaSource,
        mime_type: Option<String>,
    },
}
```

### 2. Media Source Handling

```rust
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub enum MediaSource {
    LocalPath { path: String },
    Base64Data { data: String, mime_type: String },
    Url { url: String },
    Bytes { data: Vec<u8> },
}
```

### 3. Contact and Group Management

```rust
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct Contact {
    pub jid: String,
    pub name: Option<String>,
    pub phone_number: String,
    pub is_business: bool,
    pub last_seen: Option<chrono::DateTime<chrono::Utc>>,
    pub presence: PresenceStatus,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct Group {
    pub jid: String,
    pub name: String,
    pub description: Option<String>,
    pub participants: Vec<GroupParticipant>,
    pub admins: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}
```

## Error Handling

### 1. Error Classification

**Connection Errors**:
- Network connectivity issues
- WhatsApp server unavailability
- Authentication failures
- Session expiration

**Rate Limiting Errors**:
- Message rate limits exceeded
- Bulk operation limits
- API quota exhaustion

**Media Errors**:
- File size limitations
- Unsupported formats
- Encoding/decoding failures
- Upload/download timeouts

**FFI Errors**:
- Library loading failures
- Function call errors
- Memory allocation issues
- Type conversion errors

### 2. Error Recovery Strategies

**Automatic Retry**:
- Exponential backoff for transient errors
- Maximum retry limits to prevent infinite loops
- Different retry strategies per error type

**Graceful Degradation**:
- Fallback to basic functionality when advanced features fail
- Queue messages for later delivery during connectivity issues
- Offline mode with local storage

**User Notification**:
- Clear error messages in the UI
- Suggested actions for user resolution
- Progress indicators during recovery attempts

## Testing Strategy

### 1. Unit Testing

**FFI Layer Testing**:
- Mock Go library responses
- Test error code mapping
- Validate memory management
- Test concurrent access scenarios

**Business Logic Testing**:
- Message content validation
- Rate limiting algorithms
- Connection state management
- Event processing logic

### 2. Integration Testing

**End-to-End Scenarios**:
- Complete authentication flow
- Message sending and receiving
- Bulk operation processing
- Error recovery scenarios

**Tauri Integration Testing**:
- Command invocation testing
- Event emission verification
- State management validation
- Frontend-backend communication

### 3. Performance Testing

**Load Testing**:
- Bulk message processing performance
- Memory usage under load
- Connection stability over time
- Event processing throughput

**Stress Testing**:
- Maximum concurrent connections
- Large media file handling
- Extended operation periods
- Resource exhaustion scenarios

### 4. Mock Testing Infrastructure

```rust
pub struct MockWhatsAppAdapter {
    responses: HashMap<String, MockResponse>,
    call_history: Vec<MockCall>,
    event_emitter: mpsc::UnboundedSender<WhatsAppEvent>,
}

pub enum MockResponse {
    Success(serde_json::Value),
    Error(WhatsAppError),
    Delayed(Duration, Box<MockResponse>),
}
```

## Implementation Phases

### Phase 1: Core Infrastructure Enhancement
- Enhanced error handling and logging
- Connection manager with auto-reconnection
- Event aggregator and processing
- Basic Tauri integration

### Phase 2: Media and Messaging Features
- Enhanced media support (images, videos, audio, documents)
- Message content validation and processing
- Contact and group management APIs
- QR code authentication flow

### Phase 3: Bulk Operations and Performance
- Bulk message manager with rate limiting
- Progress tracking and callbacks
- Performance optimizations
- Resource management improvements

### Phase 4: Advanced Features and Polish
- Advanced error recovery mechanisms
- Comprehensive testing suite
- Documentation and examples
- Performance monitoring and metrics

This design provides a solid foundation for a production-ready WhatsApp messaging solution with comprehensive error handling, real-time capabilities, and seamless desktop integration.