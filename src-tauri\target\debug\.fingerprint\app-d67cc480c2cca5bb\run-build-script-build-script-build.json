{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 144463250787869442], [12092653563678505622, "build_script_build", false, 10303223714395961871], [16702348383442838006, "build_script_build", false, 14155356564269005009]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-d67cc480c2cca5bb\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}