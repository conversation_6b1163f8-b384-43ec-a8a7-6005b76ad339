{"rustc": 16591470773350601817, "features": "[\"default\", \"maths\", \"serde\", \"std\"]", "declared_features": "[\"borsh\", \"c-repr\", \"db-diesel-mysql\", \"db-diesel-postgres\", \"db-diesel2-mysql\", \"db-diesel2-postgres\", \"db-postgres\", \"db-tokio-postgres\", \"default\", \"diesel\", \"legacy-ops\", \"macros\", \"maths\", \"maths-nopanic\", \"ndarray\", \"proptest\", \"rand\", \"rand-0_9\", \"rkyv\", \"rkyv-safe\", \"rocket-traits\", \"rust-fuzz\", \"serde\", \"serde-arbitrary-precision\", \"serde-bincode\", \"serde-float\", \"serde-str\", \"serde-with-arbitrary-precision\", \"serde-with-float\", \"serde-with-str\", \"serde_json\", \"std\", \"tokio-pg\", \"tokio-postgres\"]", "target": 10284609753004012519, "profile": 15657897354478470176, "path": 4013162442665791138, "deps": [[5157631553186200874, "num_traits", false, 14695776958905956247], [9689903380558560274, "serde", false, 10996426815917291500], [13847662864258534762, "arrayvec", false, 16556736915935410297], [16119793329258425851, "build_script_build", false, 3136665481941765907]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rust_decimal-61216319a4e11872\\dep-lib-rust_decimal", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}