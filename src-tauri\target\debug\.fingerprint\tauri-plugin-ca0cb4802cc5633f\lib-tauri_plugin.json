{"rustc": 16591470773350601817, "features": "[\"build\"]", "declared_features": "[\"build\", \"runtime\"]", "target": 15996119522804316622, "profile": 2225463790103693989, "path": 985605027777814207, "deps": [[654232091421095663, "tauri_utils", false, 15712907068407191118], [6913375703034175521, "schemars", false, 2371191611272397952], [8786711029710048183, "toml", false, 10048794690708226692], [9689903380558560274, "serde", false, 16503059047051001516], [13625485746686963219, "anyhow", false, 8308486080552771400], [15622660310229662834, "walkdir", false, 14109848318917712597], [16362055519698394275, "serde_json", false, 14102427500185749966], [17155886227862585100, "glob", false, 16192797392537921819]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-ca0cb4802cc5633f\\dep-lib-tauri_plugin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}