{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 1666181205404845645, "deps": [[654232091421095663, "tauri_utils", false, 15712907068407191118], [2704937418414716471, "tauri_codegen", false, 10883145951760208420], [3060637413840920116, "proc_macro2", false, 12441885387897080324], [13077543566650298139, "heck", false, 1670613786372742531], [17990358020177143287, "quote", false, 9492086704218893313], [18149961000318489080, "syn", false, 6062637982592736054]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-d44111613fa6f560\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}