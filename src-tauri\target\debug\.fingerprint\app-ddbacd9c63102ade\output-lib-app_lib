{"$message_type":"diagnostic","message":"unresolved import `async_trait`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\whatsapp\\events.rs","byte_start":57,"byte_end":68,"line_start":3,"line_end":3,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a similar path exists","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\events.rs","byte_start":57,"byte_end":68,"line_start":3,"line_end":3,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":"sea_orm_migration::async_trait","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `async_trait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\events.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse async_trait::async_trait;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a similar path exists: `sea_orm_migration::async_trait`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `Invoke` in crate `tauri`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\whatsapp\\commands.rs","byte_start":10996,"byte_end":11002,"line_start":361,"line_end":361,"column_start":49,"column_end":55,"is_primary":true,"text":[{"text":"pub fn get_command_handlers() -> impl Fn(tauri::Invoke) + Send + Sync + 'static {","highlight_start":49,"highlight_end":55}],"label":"not found in `tauri`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\commands.rs","byte_start":54,"byte_end":54,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use tauri::ipc::Invoke;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `Invoke`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\commands.rs","byte_start":10989,"byte_end":10996,"line_start":361,"line_end":361,"column_start":42,"column_end":49,"is_primary":true,"text":[{"text":"pub fn get_command_handlers() -> impl Fn(tauri::Invoke) + Send + Sync + 'static {","highlight_start":42,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `Invoke` in crate `tauri`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\commands.rs:361:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn get_command_handlers() -> impl Fn(tauri::Invoke) + Send + Sync + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `tauri`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use tauri::ipc::Invoke;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `Invoke`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0mpub fn get_command_handlers() -> impl Fn(\u001b[0m\u001b[0m\u001b[38;5;9mtauri::\u001b[0m\u001b[0mInvoke) + Send + Sync + 'static {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0mpub fn get_command_handlers() -> impl Fn(Invoke) + Send + Sync + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AppHandle`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\whatsapp\\commands.rs","byte_start":125,"byte_end":134,"line_start":5,"line_end":5,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"use tauri::{AppHandle, State};","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\commands.rs","byte_start":125,"byte_end":136,"line_start":5,"line_end":5,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use tauri::{AppHandle, State};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\whatsapp\\commands.rs","byte_start":124,"byte_end":125,"line_start":5,"line_end":5,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use tauri::{AppHandle, State};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\whatsapp\\commands.rs","byte_start":141,"byte_end":142,"line_start":5,"line_end":5,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"use tauri::{AppHandle, State};","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `AppHandle`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\commands.rs:5:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tauri::{AppHandle, State};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\whatsapp\\commands.rs","byte_start":159,"byte_end":164,"line_start":6,"line_end":6,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\commands.rs","byte_start":159,"byte_end":166,"line_start":6,"line_end":6,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, instrument};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\commands.rs:6:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, instrument};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `commands::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":348,"byte_end":359,"line_start":15,"line_end":15,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"pub use commands::*;","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":340,"byte_end":362,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use commands::*;","highlight_start":1,"highlight_end":21},{"text":"pub use events::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `commands::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\mod.rs:15:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use commands::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `events::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":370,"byte_end":379,"line_start":16,"line_end":16,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use events::*;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":362,"byte_end":382,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use events::*;","highlight_start":1,"highlight_end":19},{"text":"pub use service::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `events::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\mod.rs:16:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use events::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `service::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":390,"byte_end":400,"line_start":17,"line_end":17,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"pub use service::*;","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":382,"byte_end":403,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use service::*;","highlight_start":1,"highlight_end":20},{"text":"pub use state::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `service::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\mod.rs:17:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use service::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `state::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":411,"byte_end":419,"line_start":18,"line_end":18,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"pub use state::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\whatsapp\\mod.rs","byte_start":403,"byte_end":420,"line_start":18,"line_end":18,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"pub use state::*;","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `state::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\mod.rs:18:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use state::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `with_library_path` found for struct `ServiceConfig` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1475,"byte_end":1517,"line_start":48,"line_end":49,"column_start":35,"column_end":18,"is_primary":false,"text":[{"text":"            let whatsapp_config = ServiceConfig::default()","highlight_start":35,"highlight_end":59},{"text":"                .with_library_path(\"whatsmeow_ffi.dll\".to_string())","highlight_start":1,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":1517,"byte_end":1534,"line_start":49,"line_end":49,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"                .with_library_path(\"whatsmeow_ffi.dll\".to_string())","highlight_start":18,"highlight_end":35}],"label":"method not found in `ServiceConfig`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\state.rs","byte_start":1097,"byte_end":1121,"line_start":41,"line_end":41,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"pub struct ServiceConfig {","highlight_start":1,"highlight_end":25}],"label":"method `with_library_path` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `with_library_path` found for struct `ServiceConfig` in the current scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:49:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let whatsapp_config = ServiceConfig::default()\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m ___________________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .with_library_path(\"whatsmeow_ffi.dll\".to_string())\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ServiceConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________________|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\whatsapp\\state.rs:41:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct ServiceConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod `with_library_path` not found for this struct\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `manage` found for mutable reference `&mut tauri::App` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2063,"byte_end":2069,"line_start":60,"line_end":60,"column_start":17,"column_end":23,"is_primary":true,"text":[{"text":"            app.manage(whatsapp_service);","highlight_start":17,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is a method `manager` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.7.0\\src\\lib.rs","byte_start":37454,"byte_end":37490,"line_start":1075,"line_end":1075,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    fn manager(&self) -> &AppManager<R>;","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"trait `Manager` which provides `manage` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":318,"byte_end":318,"line_start":9,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"mod prelude;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use tauri::Manager;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `manage` found for mutable reference `&mut tauri::App` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:60:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            app.manage(whatsapp_service);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `manager` with a similar name, but with different arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.7.0\\src\\lib.rs:1075:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1075\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn manager(&self) -> &AppManager<R>;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `Manager` which provides `manage` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10m+ use tauri::Manager;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"expected `impl (Fn({type error})) + Send + Sync` to return `bool`, but it returns `()`","code":{"code":"E0271","explanation":"A type mismatched an associated type of a trait.\n\nErroneous code example:\n\n```compile_fail,E0271\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType=u32> {\n//                    ~~~~~~~~ ~~~~~~~~~~~~~~~~~~\n//                        |            |\n//         This says `foo` can         |\n//           only be used with         |\n//              some type that         |\n//         implements `Trait`.         |\n//                                     |\n//                             This says not only must\n//                             `T` be an impl of `Trait`\n//                             but also that the impl\n//                             must assign the type `u32`\n//                             to the associated type.\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = &'static str; }\n//~~~~~~~~~~~~~~~   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n//      |                             |\n// `i8` does have                     |\n// implementation                     |\n// of `Trait`...                      |\n//                     ... but it is an implementation\n//                     that assigns `&'static str` to\n//                     the associated type.\n\nfoo(3_i8);\n// Here, we invoke `foo` with an `i8`, which does not satisfy\n// the constraint `<i8 as Trait>::AssociatedType=u32`, and\n// therefore the type-checker complains with this error code.\n```\n\nThe issue can be resolved by changing the associated type:\n1) in the `foo` implementation:\n```\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType = &'static str> {\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = &'static str; }\n\nfoo(3_i8);\n```\n\n2) in the `Trait` implementation for `i8`:\n```\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType = u32> {\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = u32; }\n\nfoo(3_i8);\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2156,"byte_end":2178,"line_start":64,"line_end":64,"column_start":25,"column_end":47,"is_primary":true,"text":[{"text":"        .invoke_handler(get_command_handlers())","highlight_start":25,"highlight_end":47}],"label":"expected `bool`, found `()`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":2141,"byte_end":2155,"line_start":64,"line_end":64,"column_start":10,"column_end":24,"is_primary":false,"text":[{"text":"        .invoke_handler(get_command_handlers())","highlight_start":10,"highlight_end":24}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"required by a bound in `tauri::Builder::<R>::invoke_handler`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.7.0\\src\\app.rs","byte_start":50752,"byte_end":50766,"line_start":1517,"line_end":1517,"column_start":10,"column_end":24,"is_primary":false,"text":[{"text":"  pub fn invoke_handler<F>(mut self, invoke_handler: F) -> Self","highlight_start":10,"highlight_end":24}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.7.0\\src\\app.rs","byte_start":50839,"byte_end":50843,"line_start":1519,"line_end":1519,"column_start":25,"column_end":29,"is_primary":true,"text":[{"text":"    F: Fn(Invoke<R>) -> bool + Send + Sync + 'static,","highlight_start":25,"highlight_end":29}],"label":"required by this bound in `Builder::<R>::invoke_handler`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0271]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: expected `impl (Fn({type error})) + Send + Sync` to return `bool`, but it returns `()`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:64:25\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .invoke_handler(get_command_handlers())\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `bool`, found `()`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `tauri::Builder::<R>::invoke_handler`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-2.7.0\\src\\app.rs:1519:25\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1517\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m  pub fn invoke_handler<F>(mut self, invoke_handler: F) -> Self\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1518\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m  where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1519\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    F: Fn(Invoke<R>) -> bool + Send + Sync + 'static,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Builder::<R>::invoke_handler`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`client_guard` does not live long enough","code":{"code":"E0597","explanation":"This error occurs because a value was dropped while it was still borrowed.\n\nErroneous code example:\n\n```compile_fail,E0597\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n{\n    let y = 0;\n    x.x = Some(&y); // error: `y` does not live long enough\n}\nprintln!(\"{:?}\", x.x);\n```\n\nHere, `y` is dropped at the end of the inner scope, but it is borrowed by\n`x` until the `println`. To fix the previous example, just remove the scope\nso that `y` isn't dropped until after the println\n\n```\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n\nlet y = 0;\nx.x = Some(&y);\n\nprintln!(\"{:?}\", x.x);\n```\n"},"level":"error","spans":[{"file_name":"src\\whatsapp\\service.rs","byte_start":2950,"byte_end":2962,"line_start":92,"line_end":92,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"            client_guard","highlight_start":13,"highlight_end":25}],"label":"borrowed value does not live long enough","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":3121,"byte_end":3122,"line_start":96,"line_end":96,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        };","highlight_start":9,"highlight_end":10}],"label":"`client_guard` dropped here while still borrowed","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":2896,"byte_end":2908,"line_start":91,"line_end":91,"column_start":17,"column_end":29,"is_primary":false,"text":[{"text":"            let client_guard = self.client.lock().await;","highlight_start":17,"highlight_end":29}],"label":"binding `client_guard` declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":2868,"byte_end":2874,"line_start":90,"line_end":90,"column_start":13,"column_end":19,"is_primary":false,"text":[{"text":"        let client = {","highlight_start":13,"highlight_end":19}],"label":"borrow later stored here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0597]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `client_guard` does not live long enough\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\service.rs:92:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m90\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mborrow later stored here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m91\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let client_guard = self.client.lock().await;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbinding `client_guard` declared here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            client_guard\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mborrowed value does not live long enough\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        };\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`client_guard` dropped here while still borrowed\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`client_guard` does not live long enough","code":{"code":"E0597","explanation":"This error occurs because a value was dropped while it was still borrowed.\n\nErroneous code example:\n\n```compile_fail,E0597\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n{\n    let y = 0;\n    x.x = Some(&y); // error: `y` does not live long enough\n}\nprintln!(\"{:?}\", x.x);\n```\n\nHere, `y` is dropped at the end of the inner scope, but it is borrowed by\n`x` until the `println`. To fix the previous example, just remove the scope\nso that `y` isn't dropped until after the println\n\n```\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n\nlet y = 0;\nx.x = Some(&y);\n\nprintln!(\"{:?}\", x.x);\n```\n"},"level":"error","spans":[{"file_name":"src\\whatsapp\\service.rs","byte_start":4898,"byte_end":4910,"line_start":149,"line_end":149,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"            client_guard","highlight_start":13,"highlight_end":25}],"label":"borrowed value does not live long enough","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":5069,"byte_end":5070,"line_start":153,"line_end":153,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        };","highlight_start":9,"highlight_end":10}],"label":"`client_guard` dropped here while still borrowed","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":4844,"byte_end":4856,"line_start":148,"line_end":148,"column_start":17,"column_end":29,"is_primary":false,"text":[{"text":"            let client_guard = self.client.lock().await;","highlight_start":17,"highlight_end":29}],"label":"binding `client_guard` declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":4816,"byte_end":4822,"line_start":147,"line_end":147,"column_start":13,"column_end":19,"is_primary":false,"text":[{"text":"        let client = {","highlight_start":13,"highlight_end":19}],"label":"borrow later stored here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0597]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `client_guard` does not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\service.rs:149:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mborrow later stored here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let client_guard = self.client.lock().await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbinding `client_guard` declared here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            client_guard\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mborrowed value does not live long enough\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        };\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`client_guard` dropped here while still borrowed\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`client_guard` does not live long enough","code":{"code":"E0597","explanation":"This error occurs because a value was dropped while it was still borrowed.\n\nErroneous code example:\n\n```compile_fail,E0597\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n{\n    let y = 0;\n    x.x = Some(&y); // error: `y` does not live long enough\n}\nprintln!(\"{:?}\", x.x);\n```\n\nHere, `y` is dropped at the end of the inner scope, but it is borrowed by\n`x` until the `println`. To fix the previous example, just remove the scope\nso that `y` isn't dropped until after the println\n\n```\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n\nlet y = 0;\nx.x = Some(&y);\n\nprintln!(\"{:?}\", x.x);\n```\n"},"level":"error","spans":[{"file_name":"src\\whatsapp\\service.rs","byte_start":6911,"byte_end":6923,"line_start":209,"line_end":209,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"            client_guard","highlight_start":13,"highlight_end":25}],"label":"borrowed value does not live long enough","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":7082,"byte_end":7083,"line_start":213,"line_end":213,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        };","highlight_start":9,"highlight_end":10}],"label":"`client_guard` dropped here while still borrowed","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":6857,"byte_end":6869,"line_start":208,"line_end":208,"column_start":17,"column_end":29,"is_primary":false,"text":[{"text":"            let client_guard = self.client.lock().await;","highlight_start":17,"highlight_end":29}],"label":"binding `client_guard` declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":6829,"byte_end":6835,"line_start":207,"line_end":207,"column_start":13,"column_end":19,"is_primary":false,"text":[{"text":"        let client = {","highlight_start":13,"highlight_end":19}],"label":"borrow later stored here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0597]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `client_guard` does not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\service.rs:209:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mborrow later stored here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m208\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let client_guard = self.client.lock().await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbinding `client_guard` declared here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            client_guard\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mborrowed value does not live long enough\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        };\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`client_guard` dropped here while still borrowed\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`client_guard` does not live long enough","code":{"code":"E0597","explanation":"This error occurs because a value was dropped while it was still borrowed.\n\nErroneous code example:\n\n```compile_fail,E0597\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n{\n    let y = 0;\n    x.x = Some(&y); // error: `y` does not live long enough\n}\nprintln!(\"{:?}\", x.x);\n```\n\nHere, `y` is dropped at the end of the inner scope, but it is borrowed by\n`x` until the `println`. To fix the previous example, just remove the scope\nso that `y` isn't dropped until after the println\n\n```\nstruct Foo<'a> {\n    x: Option<&'a u32>,\n}\n\nlet mut x = Foo { x: None };\n\nlet y = 0;\nx.x = Some(&y);\n\nprintln!(\"{:?}\", x.x);\n```\n"},"level":"error","spans":[{"file_name":"src\\whatsapp\\service.rs","byte_start":11568,"byte_end":11580,"line_start":351,"line_end":351,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"            client_guard","highlight_start":13,"highlight_end":25}],"label":"borrowed value does not live long enough","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":11713,"byte_end":11714,"line_start":354,"line_end":354,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        };","highlight_start":9,"highlight_end":10}],"label":"`client_guard` dropped here while still borrowed","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":11510,"byte_end":11526,"line_start":350,"line_end":350,"column_start":17,"column_end":33,"is_primary":false,"text":[{"text":"            let mut client_guard = self.client.lock().await;","highlight_start":17,"highlight_end":33}],"label":"binding `client_guard` declared here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\whatsapp\\service.rs","byte_start":11807,"byte_end":11819,"line_start":357,"line_end":357,"column_start":48,"column_end":60,"is_primary":false,"text":[{"text":"        if let Some(mut event_stream) = client.event_stream() {","highlight_start":48,"highlight_end":60}],"label":"borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0597]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `client_guard` does not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\whatsapp\\service.rs:351:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut client_guard = self.client.lock().await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbinding `client_guard` declared here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            client_guard\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mborrowed value does not live long enough\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m354\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        };\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`client_guard` dropped here while still borrowed\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m357\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(mut event_stream) = client.event_stream() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mborrow later used by call\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 9 previous errors; 6 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 9 previous errors; 6 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0271, E0412, E0432, E0597, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0271, E0412, E0432, E0597, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0271`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0271`.\u001b[0m\n"}
