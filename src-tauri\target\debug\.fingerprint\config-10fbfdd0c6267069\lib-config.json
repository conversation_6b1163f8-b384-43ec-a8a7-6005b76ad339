{"rustc": 16591470773350601817, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 11679461194111479299, "path": 6772131305939330334, "deps": [[1213098572879462490, "json5_rs", false, 1566764842142267688], [2244620803250265856, "ron", false, 235593632345010263], [2353329962562453694, "yaml_rust2", false, 2021272547341316812], [2356429411733741858, "ini", false, 12803606053346390801], [6517602928339163454, "path<PERSON><PERSON>", false, 14703199461025199088], [9689903380558560274, "serde", false, 10996426815917291500], [11946729385090170470, "async_trait", false, 2389517705436373950], [12060164242600251039, "toml", false, 18092698204631450409], [13475460906694513802, "convert_case", false, 2606012789252860853], [14718834678227948963, "winnow", false, 7841660171975622661], [16362055519698394275, "serde_json", false, 7262301444845053042]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\config-10fbfdd0c6267069\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}