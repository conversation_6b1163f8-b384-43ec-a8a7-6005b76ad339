use crate::core::entities::MessageContent;
use crate::core::errors::{Result, WhatsAppError};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, RwLock};
use tokio::time::timeout;

fn default_instant() -> Option<Instant> {
    None
}

/// Priority levels for bulk messages
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum MessagePriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

impl Default for MessagePriority {
    fn default() -> Self {
        MessagePriority::Normal
    }
}

/// Status of a bulk message
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MessageStatus {
    Queued,
    Sending,
    Sent { message_id: String },
    Failed { error: String, retry_count: u32 },
    Cancelled,
}

/// A message in the bulk messaging queue
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BulkMessage {
    pub id: String,
    pub recipient: String,
    pub content: MessageContent,
    pub priority: MessagePriority,
    pub retry_count: u32,
    pub max_retries: u32,
    #[serde(skip, default = "default_instant")]
    pub scheduled_time: Option<Instant>,
    pub status: MessageStatus,
    #[serde(skip, default = "Instant::now")]
    pub created_at: Instant,
    #[serde(skip, default = "Instant::now")]
    pub updated_at: Instant,
}

impl BulkMessage {
    pub fn new(
        id: String,
        recipient: String,
        content: MessageContent,
        priority: MessagePriority,
        max_retries: u32,
    ) -> Self {
        let now = Instant::now();
        Self {
            id,
            recipient,
            content,
            priority,
            retry_count: 0,
            max_retries,
            scheduled_time: None,
            status: MessageStatus::Queued,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn schedule_for(&mut self, delay: Duration) {
        self.scheduled_time = Some(Instant::now() + delay);
        self.updated_at = Instant::now();
    }

    pub fn is_ready(&self) -> bool {
        match self.scheduled_time {
            Some(scheduled) => Instant::now() >= scheduled,
            None => true,
        }
    }

    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries
    }

    pub fn increment_retry(&mut self) {
        self.retry_count += 1;
        self.updated_at = Instant::now();
    }

    pub fn update_status(&mut self, status: MessageStatus) {
        self.status = status;
        self.updated_at = Instant::now();
    }
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub messages_per_minute: u32,
    pub messages_per_hour: u32,
    pub burst_limit: u32,
    pub window_size: Duration,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            messages_per_minute: 20,
            messages_per_hour: 1000,
            burst_limit: 5,
            window_size: Duration::from_secs(60),
        }
    }
}

/// Rate limiter implementation using token bucket algorithm
#[derive(Debug)]
pub struct RateLimiter {
    config: RateLimitConfig,
    tokens: Arc<Mutex<f64>>,
    last_refill: Arc<Mutex<Instant>>,
    message_timestamps: Arc<Mutex<VecDeque<Instant>>>,
}

impl RateLimiter {
    pub fn new(config: RateLimitConfig) -> Self {
        let burst_limit = config.burst_limit;
        Self {
            config,
            tokens: Arc::new(Mutex::new(burst_limit as f64)),
            last_refill: Arc::new(Mutex::new(Instant::now())),
            message_timestamps: Arc::new(Mutex::new(VecDeque::new())),
        }
    }

    pub async fn can_send(&self) -> bool {
        self.refill_tokens().await;
        let tokens = self.tokens.lock().await;
        *tokens >= 1.0 && self.check_hourly_limit().await
    }

    pub async fn consume_token(&self) -> Result<()> {
        self.refill_tokens().await;
        let mut tokens = self.tokens.lock().await;

        if *tokens >= 1.0 && self.check_hourly_limit().await {
            *tokens -= 1.0;

            // Record timestamp for hourly tracking
            let mut timestamps = self.message_timestamps.lock().await;
            timestamps.push_back(Instant::now());

            Ok(())
        } else {
            Err(WhatsAppError::Internal("Rate limit exceeded".to_string()))
        }
    }

    pub async fn time_until_next_token(&self) -> Duration {
        self.refill_tokens().await;
        let tokens = self.tokens.lock().await;

        if *tokens >= 1.0 {
            Duration::from_secs(0)
        } else {
            let refill_rate = self.config.messages_per_minute as f64 / 60.0;
            let time_for_one_token = 1.0 / refill_rate;
            Duration::from_secs_f64(time_for_one_token)
        }
    }

    async fn refill_tokens(&self) {
        let mut last_refill = self.last_refill.lock().await;
        let mut tokens = self.tokens.lock().await;

        let now = Instant::now();
        let elapsed = now.duration_since(*last_refill);

        let refill_rate = self.config.messages_per_minute as f64 / 60.0; // tokens per second
        let tokens_to_add = elapsed.as_secs_f64() * refill_rate;

        *tokens = (*tokens + tokens_to_add).min(self.config.burst_limit as f64);
        *last_refill = now;
    }

    async fn check_hourly_limit(&self) -> bool {
        let mut timestamps = self.message_timestamps.lock().await;
        let now = Instant::now();
        let one_hour_ago = now - Duration::from_secs(3600);

        // Remove timestamps older than 1 hour
        while let Some(&front) = timestamps.front() {
            if front < one_hour_ago {
                timestamps.pop_front();
            } else {
                break;
            }
        }

        timestamps.len() < self.config.messages_per_hour as usize
    }
}

/// Retry configuration with exponential backoff
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    pub initial_delay: Duration,
    pub max_delay: Duration,
    pub multiplier: f64,
    pub jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            initial_delay: Duration::from_secs(1),
            max_delay: Duration::from_secs(300), // 5 minutes
            multiplier: 2.0,
            jitter: true,
        }
    }
}

/// Retry handler with exponential backoff
#[derive(Debug)]
pub struct RetryHandler {
    config: RetryConfig,
}

impl RetryHandler {
    pub fn new(config: RetryConfig) -> Self {
        Self { config }
    }

    pub fn calculate_delay(&self, retry_count: u32) -> Duration {
        let base_delay = self.config.initial_delay.as_secs_f64();
        let delay = base_delay * self.config.multiplier.powi(retry_count as i32);
        let capped_delay = delay.min(self.config.max_delay.as_secs_f64());

        let final_delay = if self.config.jitter {
            // Add jitter: ±25% of the delay
            let jitter_factor = 0.75 + (rand::random::<f64>() * 0.5); // 0.75 to 1.25
            capped_delay * jitter_factor
        } else {
            capped_delay
        };

        Duration::from_secs_f64(final_delay.max(0.1)) // Minimum 100ms
    }

    pub fn should_retry(&self, error: &WhatsAppError, retry_count: u32, max_retries: u32) -> bool {
        retry_count < max_retries && error.is_recoverable()
    }
}

/// Progress tracking for bulk operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkProgress {
    pub operation_id: String,
    pub total_messages: usize,
    pub queued: usize,
    pub sending: usize,
    pub sent: usize,
    pub failed: usize,
    pub cancelled: usize,
    #[serde(skip, default = "Instant::now")]
    pub started_at: Instant,
    #[serde(skip, default = "default_instant")]
    pub estimated_completion: Option<Instant>,
}

impl PartialEq for BulkProgress {
    fn eq(&self, other: &Self) -> bool {
        self.operation_id == other.operation_id
            && self.total_messages == other.total_messages
            && self.queued == other.queued
            && self.sending == other.sending
            && self.sent == other.sent
            && self.failed == other.failed
            && self.cancelled == other.cancelled
        // Note: We skip comparing Instant fields as they don't implement PartialEq
    }
}

impl BulkProgress {
    pub fn new(operation_id: String, total_messages: usize) -> Self {
        Self {
            operation_id,
            total_messages,
            queued: total_messages,
            sending: 0,
            sent: 0,
            failed: 0,
            cancelled: 0,
            started_at: Instant::now(),
            estimated_completion: None,
        }
    }

    pub fn completed(&self) -> usize {
        self.sent + self.failed + self.cancelled
    }

    pub fn is_complete(&self) -> bool {
        self.completed() >= self.total_messages
    }

    pub fn success_rate(&self) -> f64 {
        if self.completed() == 0 {
            0.0
        } else {
            self.sent as f64 / self.completed() as f64
        }
    }

    pub fn update_estimates(&mut self) {
        if self.completed() > 0 {
            let elapsed = self.started_at.elapsed();
            let rate = self.completed() as f64 / elapsed.as_secs_f64();
            let remaining = self.total_messages - self.completed();

            if rate > 0.0 {
                let estimated_remaining_time = Duration::from_secs_f64(remaining as f64 / rate);
                self.estimated_completion = Some(Instant::now() + estimated_remaining_time);
            }
        }
    }
}

/// Callback trait for progress updates
#[async_trait::async_trait]
pub trait ProgressCallback: Send + Sync {
    async fn on_progress(&self, progress: &BulkProgress);
    async fn on_message_sent(&self, message: &BulkMessage, message_id: String);
    async fn on_message_failed(&self, message: &BulkMessage, error: &WhatsAppError);
    async fn on_operation_complete(&self, progress: &BulkProgress);
}

/// Statistics for bulk operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkStatistics {
    pub total_operations: usize,
    pub total_messages_processed: usize,
    pub total_messages_sent: usize,
    pub total_messages_failed: usize,
    pub average_success_rate: f64,
    pub average_processing_time: Duration,
    pub rate_limit_hits: usize,
    pub retry_attempts: usize,
}

impl Default for BulkStatistics {
    fn default() -> Self {
        Self {
            total_operations: 0,
            total_messages_processed: 0,
            total_messages_sent: 0,
            total_messages_failed: 0,
            average_success_rate: 0.0,
            average_processing_time: Duration::from_secs(0),
            rate_limit_hits: 0,
            retry_attempts: 0,
        }
    }
}

/// Configuration for the bulk message manager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkManagerConfig {
    pub rate_limit: RateLimitConfig,
    pub retry: RetryConfig,
    pub max_queue_size: usize,
    pub worker_count: usize,
    pub batch_size: usize,
    pub operation_timeout: Duration,
}

impl Default for BulkManagerConfig {
    fn default() -> Self {
        Self {
            rate_limit: RateLimitConfig::default(),
            retry: RetryConfig::default(),
            max_queue_size: 10000,
            worker_count: 3,
            batch_size: 10,
            operation_timeout: Duration::from_secs(300), // 5 minutes
        }
    }
}
/// Main bulk messaer
pub struct BulkMessageManager {
    config: BulkManagerConfig,
    rate_limiter: Arc<RateLimiter>,
    retry_handler: Arc<RetryHandler>,
    message_queue: Arc<Mutex<VecDeque<BulkMessage>>>,
    active_operations: Arc<RwLock<HashMap<String, BulkProgress>>>,
    statistics: Arc<RwLock<BulkStatistics>>,
    is_running: Arc<RwLock<bool>>,
    worker_handles: Arc<Mutex<Vec<tokio::task::JoinHandle<()>>>>,
}

impl BulkMessageManager {
    pub fn new(config: BulkManagerConfig) -> Self {
        let rate_limiter = Arc::new(RateLimiter::new(config.rate_limit.clone()));
        let retry_handler = Arc::new(RetryHandler::new(config.retry.clone()));

        Self {
            config,
            rate_limiter,
            retry_handler,
            message_queue: Arc::new(Mutex::new(VecDeque::new())),
            active_operations: Arc::new(RwLock::new(HashMap::new())),
            statistics: Arc::new(RwLock::new(BulkStatistics::default())),
            is_running: Arc::new(RwLock::new(false)),
            worker_handles: Arc::new(Mutex::new(Vec::new())),
        }
    }

    /// Start the bulk message manager workers
    pub async fn start(&self) -> Result<()> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            return Err(WhatsAppError::Internal(
                "Bulk manager already running".to_string(),
            ));
        }

        *is_running = true;
        drop(is_running);

        let mut handles = self.worker_handles.lock().await;

        // Start worker tasks
        for worker_id in 0..self.config.worker_count {
            let handle = self.spawn_worker(worker_id).await;
            handles.push(handle);
        }

        Ok(())
    }

    /// Stop the bulk message manager
    pub async fn stop(&self) -> Result<()> {
        let mut is_running = self.is_running.write().await;
        *is_running = false;
        drop(is_running);

        // Wait for all workers to finish
        let mut handles = self.worker_handles.lock().await;
        while let Some(handle) = handles.pop() {
            let _ = handle.await;
        }

        Ok(())
    }

    /// Add messages to the bulk queue
    pub async fn queue_messages(
        &self,
        operation_id: String,
        messages: Vec<(String, MessageContent, MessagePriority)>,
        max_retries: Option<u32>,
        callback: Option<Arc<dyn ProgressCallback>>,
    ) -> Result<String> {
        let max_retries = max_retries.unwrap_or(3);
        let mut queue = self.message_queue.lock().await;

        if queue.len() + messages.len() > self.config.max_queue_size {
            return Err(WhatsAppError::Internal(
                "Queue size limit exceeded".to_string(),
            ));
        }

        // Create progress tracker
        let progress = BulkProgress::new(operation_id.clone(), messages.len());
        {
            let mut operations = self.active_operations.write().await;
            operations.insert(operation_id.clone(), progress);
        }

        // Store message count before consuming the vector
        let message_count = messages.len();

        // Add messages to queue
        for (i, (recipient, content, priority)) in messages.into_iter().enumerate() {
            let message_id = format!("{}_{}", operation_id, i);
            let bulk_message = BulkMessage::new(
                message_id.clone(),
                recipient,
                content,
                priority,
                max_retries,
            );

            // Insert based on priority (higher priority first)
            let insert_pos = queue
                .iter()
                .position(|msg| msg.priority < bulk_message.priority)
                .unwrap_or(queue.len());

            queue.insert(insert_pos, bulk_message);
            tracing::debug!(
                "Queued message {} with priority {:?} at position {}",
                message_id,
                priority,
                insert_pos
            );
        }

        tracing::info!(
            "Queued {} messages for operation {}, total queue size: {}",
            message_count,
            operation_id,
            queue.len()
        );

        // Notify callback if provided
        if let Some(callback) = callback {
            let operations = self.active_operations.read().await;
            if let Some(progress) = operations.get(&operation_id) {
                callback.on_progress(progress).await;
            }
        }

        Ok(operation_id)
    }

    /// Get progress for a specific operation
    pub async fn get_progress(&self, operation_id: &str) -> Option<BulkProgress> {
        let operations = self.active_operations.read().await;
        operations.get(operation_id).cloned()
    }

    /// Get all active operations
    pub async fn get_active_operations(&self) -> HashMap<String, BulkProgress> {
        let operations = self.active_operations.read().await;
        operations.clone()
    }

    /// Cancel a bulk operation
    pub async fn cancel_operation(&self, operation_id: &str) -> Result<()> {
        let mut queue = self.message_queue.lock().await;
        let mut cancelled_count = 0;

        // Mark queued messages as cancelled
        for message in queue.iter_mut() {
            if message.id.starts_with(operation_id) && message.status == MessageStatus::Queued {
                message.update_status(MessageStatus::Cancelled);
                cancelled_count += 1;
            }
        }

        // Update progress
        {
            let mut operations = self.active_operations.write().await;
            if let Some(progress) = operations.get_mut(operation_id) {
                progress.cancelled += cancelled_count;
                progress.queued = progress.queued.saturating_sub(cancelled_count);
            }
        }

        Ok(())
    }

    /// Get current statistics
    pub async fn get_statistics(&self) -> BulkStatistics {
        let stats = self.statistics.read().await;
        stats.clone()
    }

    /// Get current queue size
    pub async fn queue_size(&self) -> usize {
        let queue = self.message_queue.lock().await;
        queue.len()
    }

    /// Check if rate limiter allows sending
    pub async fn can_send_now(&self) -> bool {
        self.rate_limiter.can_send().await
    }

    /// Get time until next message can be sent
    pub async fn time_until_next_send(&self) -> Duration {
        self.rate_limiter.time_until_next_token().await
    }

    /// Get the number of worker threads
    pub fn worker_count(&self) -> usize {
        self.config.worker_count
    }

    /// Spawn a worker task
    async fn spawn_worker(&self, worker_id: usize) -> tokio::task::JoinHandle<()> {
        let queue = Arc::clone(&self.message_queue);
        let rate_limiter = Arc::clone(&self.rate_limiter);
        let retry_handler = Arc::clone(&self.retry_handler);
        let active_operations = Arc::clone(&self.active_operations);
        let statistics = Arc::clone(&self.statistics);
        let is_running = Arc::clone(&self.is_running);

        tokio::spawn(async move {
            tracing::info!("Bulk message worker {} started", worker_id);

            while *is_running.read().await {
                // Try to get a message from the queue
                let message = {
                    let mut queue = queue.lock().await;
                    queue
                        .iter()
                        .position(|msg| msg.status == MessageStatus::Queued && msg.is_ready())
                        .and_then(|pos| queue.remove(pos))
                };

                if let Some(mut message) = message {
                    // Wait for rate limiter
                    while !rate_limiter.can_send().await {
                        let wait_time = rate_limiter.time_until_next_token().await;
                        tokio::time::sleep(wait_time).await;

                        // Update statistics
                        {
                            let mut stats = statistics.write().await;
                            stats.rate_limit_hits += 1;
                        }
                    }

                    // Consume rate limit token
                    if let Err(e) = rate_limiter.consume_token().await {
                        tracing::warn!("Failed to consume rate limit token: {}", e);
                        // Put message back in queue
                        let mut queue = queue.lock().await;
                        queue.push_front(message);
                        continue;
                    }

                    // Update message status to sending
                    message.update_status(MessageStatus::Sending);

                    // Update progress
                    let operation_id = message
                        .id
                        .rsplitn(2, '_')
                        .nth(1)
                        .unwrap_or("unknown")
                        .to_string();
                    {
                        let mut operations = active_operations.write().await;
                        if let Some(progress) = operations.get_mut(&operation_id) {
                            progress.queued = progress.queued.saturating_sub(1);
                            progress.sending += 1;
                            progress.update_estimates();
                            tracing::debug!(
                                "Updated progress for {}: queued={}, sending={}",
                                operation_id,
                                progress.queued,
                                progress.sending
                            );
                        }
                    }

                    // Simulate message sending (replace with actual FFI call)
                    let send_result = Self::send_message_impl(&message).await;

                    match send_result {
                        Ok(message_id) => {
                            // Message sent successfully
                            message.update_status(MessageStatus::Sent {
                                message_id: message_id.clone(),
                            });

                            // Update progress and statistics
                            {
                                let mut operations = active_operations.write().await;
                                if let Some(progress) = operations.get_mut(&operation_id) {
                                    progress.sending = progress.sending.saturating_sub(1);
                                    progress.sent += 1;
                                    progress.update_estimates();
                                    tracing::debug!(
                                        "Message sent successfully for {}: sent={}, total={}",
                                        operation_id,
                                        progress.sent,
                                        progress.total_messages
                                    );
                                }
                            }

                            {
                                let mut stats = statistics.write().await;
                                stats.total_messages_sent += 1;
                                stats.total_messages_processed += 1;
                            }

                            tracing::debug!("Message {} sent successfully", message.id);
                        }
                        Err(error) => {
                            // Message failed
                            let should_retry = retry_handler.should_retry(
                                &error,
                                message.retry_count,
                                message.max_retries,
                            );

                            if should_retry && message.can_retry() {
                                // Schedule retry
                                let message_id = message.id.clone();
                                let retry_count = message.retry_count;
                                message.increment_retry();
                                let delay = retry_handler.calculate_delay(message.retry_count);
                                message.schedule_for(delay);
                                message.update_status(MessageStatus::Failed {
                                    error: error.to_string(),
                                    retry_count: message.retry_count,
                                });

                                // Put back in queue for retry
                                let mut queue = queue.lock().await;
                                let insert_pos = queue
                                    .iter()
                                    .position(|msg| msg.priority < message.priority)
                                    .unwrap_or(queue.len());
                                queue.insert(insert_pos, message);

                                // Update statistics
                                {
                                    let mut stats = statistics.write().await;
                                    stats.retry_attempts += 1;
                                }

                                tracing::warn!(
                                    "Message {} failed, scheduled for retry {}: {}",
                                    message_id,
                                    retry_count + 1,
                                    error
                                );
                            } else {
                                // Final failure
                                message.update_status(MessageStatus::Failed {
                                    error: error.to_string(),
                                    retry_count: message.retry_count,
                                });

                                // Update progress and statistics
                                {
                                    let mut operations = active_operations.write().await;
                                    if let Some(progress) = operations.get_mut(&operation_id) {
                                        progress.sending = progress.sending.saturating_sub(1);
                                        progress.failed += 1;
                                        progress.update_estimates();
                                    }
                                }

                                {
                                    let mut stats = statistics.write().await;
                                    stats.total_messages_failed += 1;
                                    stats.total_messages_processed += 1;
                                }

                                tracing::error!(
                                    "Message {} failed permanently: {}",
                                    message.id,
                                    error
                                );
                            }

                            // Update progress for sending status
                            {
                                let mut operations = active_operations.write().await;
                                if let Some(progress) = operations.get_mut(&operation_id) {
                                    if progress.sending > 0 {
                                        progress.sending -= 1;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // No messages ready, sleep briefly
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }

            tracing::info!("Bulk message worker {} stopped", worker_id);
        })
    }

    /// Simulate message sending (replace with actual FFI implementation)
    async fn send_message_impl(message: &BulkMessage) -> Result<String> {
        // Add timeout for the operation
        let send_future = async {
            // Simulate network delay (reduced for faster example)
            tokio::time::sleep(Duration::from_millis(50 + rand::random::<u64>() % 200)).await;

            // Simulate occasional failures for testing (reduced failure rate)
            if rand::random::<f64>() < 0.05 {
                // 5% failure rate
                return Err(WhatsAppError::SendFailed {
                    jid: message.recipient.clone(),
                    reason: "Simulated network error".to_string(),
                });
            }

            // Return success with mock message ID
            Ok(format!("msg_{}", rand::random::<u64>()))
        };

        timeout(Duration::from_secs(30), send_future)
            .await
            .map_err(|_| WhatsAppError::Timeout {
                operation: "send_message".to_string(),
                duration_ms: 30000,
            })?
    }
}

impl Drop for BulkMessageManager {
    fn drop(&mut self) {
        // Ensure workers are stopped when manager is dropped
        let is_running = Arc::clone(&self.is_running);
        tokio::spawn(async move {
            let mut running = is_running.write().await;
            *running = false;
        });
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_rate_limiter_basic() {
        let config = RateLimitConfig {
            messages_per_minute: 60,
            messages_per_hour: 1000,
            burst_limit: 5,
            window_size: Duration::from_secs(60),
        };

        let limiter = RateLimiter::new(config);

        // Should be able to send burst_limit messages immediately
        for _ in 0..5 {
            assert!(limiter.can_send().await);
            limiter.consume_token().await.unwrap();
        }

        // Should be rate limited after burst
        assert!(!limiter.can_send().await);
    }

    #[tokio::test]
    async fn test_rate_limiter_refill() {
        let config = RateLimitConfig {
            messages_per_minute: 60, // 1 per second
            messages_per_hour: 1000,
            burst_limit: 1,
            window_size: Duration::from_secs(60),
        };

        let limiter = RateLimiter::new(config);

        // Consume the only token
        assert!(limiter.can_send().await);
        limiter.consume_token().await.unwrap();
        assert!(!limiter.can_send().await);

        // Wait for refill (slightly more than 1 second)
        tokio::time::sleep(Duration::from_millis(1100)).await;
        assert!(limiter.can_send().await);
    }

    #[tokio::test]
    async fn test_retry_handler_delay_calculation() {
        let config = RetryConfig {
            initial_delay: Duration::from_secs(1),
            max_delay: Duration::from_secs(60),
            multiplier: 2.0,
            jitter: false,
        };

        let handler = RetryHandler::new(config);

        // Test exponential backoff
        assert_eq!(handler.calculate_delay(0), Duration::from_secs(1));
        assert_eq!(handler.calculate_delay(1), Duration::from_secs(2));
        assert_eq!(handler.calculate_delay(2), Duration::from_secs(4));
        assert_eq!(handler.calculate_delay(3), Duration::from_secs(8));

        // Test max delay cap
        assert_eq!(handler.calculate_delay(10), Duration::from_secs(60));
    }

    #[tokio::test]
    async fn test_retry_handler_should_retry() {
        let config = RetryConfig::default();
        let handler = RetryHandler::new(config);

        let recoverable_error = WhatsAppError::NotConnected;
        let non_recoverable_error = WhatsAppError::InvalidHandle { handle: 0 };

        // Should retry recoverable errors within limit
        assert!(handler.should_retry(&recoverable_error, 0, 3));
        assert!(handler.should_retry(&recoverable_error, 2, 3));
        assert!(!handler.should_retry(&recoverable_error, 3, 3));

        // Should not retry non-recoverable errors
        assert!(!handler.should_retry(&non_recoverable_error, 0, 3));
    }

    #[tokio::test]
    async fn test_bulk_message_priority_ordering() {
        let msg1 = BulkMessage::new(
            "1".to_string(),
            "recipient1".to_string(),
            MessageContent::Text {
                text: "test1".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::Low,
            3,
        );

        let msg2 = BulkMessage::new(
            "2".to_string(),
            "recipient2".to_string(),
            MessageContent::Text {
                text: "test2".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::High,
            3,
        );

        let msg3 = BulkMessage::new(
            "3".to_string(),
            "recipient3".to_string(),
            MessageContent::Text {
                text: "test3".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::Normal,
            3,
        );

        let mut messages = vec![msg1, msg2, msg3];
        messages.sort_by(|a, b| b.priority.cmp(&a.priority));

        assert_eq!(messages[0].priority, MessagePriority::High);
        assert_eq!(messages[1].priority, MessagePriority::Normal);
        assert_eq!(messages[2].priority, MessagePriority::Low);
    }

    #[tokio::test]
    async fn test_bulk_message_scheduling() {
        let mut message = BulkMessage::new(
            "test".to_string(),
            "recipient".to_string(),
            MessageContent::Text {
                text: "test".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::Normal,
            3,
        );

        // Initially ready
        assert!(message.is_ready());

        // Schedule for future
        message.schedule_for(Duration::from_millis(100));
        assert!(!message.is_ready());

        // Wait and check again
        tokio::time::sleep(Duration::from_millis(150)).await;
        assert!(message.is_ready());
    }

    #[tokio::test]
    async fn test_bulk_progress_tracking() {
        let mut progress = BulkProgress::new("test_op".to_string(), 100);

        assert_eq!(progress.total_messages, 100);
        assert_eq!(progress.queued, 100);
        assert_eq!(progress.completed(), 0);
        assert!(!progress.is_complete());
        assert_eq!(progress.success_rate(), 0.0);

        // Simulate some progress
        progress.queued = 80;
        progress.sending = 10;
        progress.sent = 8;
        progress.failed = 2;

        assert_eq!(progress.completed(), 10);
        assert!(!progress.is_complete());
        assert_eq!(progress.success_rate(), 0.8);

        // Complete the operation
        progress.queued = 0;
        progress.sending = 0;
        progress.sent = 90;
        progress.failed = 10;

        assert_eq!(progress.completed(), 100);
        assert!(progress.is_complete());
        assert_eq!(progress.success_rate(), 0.9);
    }

    #[tokio::test]
    async fn test_bulk_manager_queue_messages() {
        let config = BulkManagerConfig::default();
        let manager = BulkMessageManager::new(config);

        let messages = vec![
            (
                "recipient1".to_string(),
                MessageContent::Text {
                    text: "test1".to_string(),
                    quoted_message_id: None,
                    mentioned_numbers: None,
                },
                MessagePriority::Normal,
            ),
            (
                "recipient2".to_string(),
                MessageContent::Text {
                    text: "test2".to_string(),
                    quoted_message_id: None,
                    mentioned_numbers: None,
                },
                MessagePriority::High,
            ),
        ];

        let operation_id = manager
            .queue_messages("test_op".to_string(), messages, Some(3), None)
            .await
            .unwrap();

        assert_eq!(operation_id, "test_op");
        assert_eq!(manager.queue_size().await, 2);

        let progress = manager.get_progress("test_op").await.unwrap();
        assert_eq!(progress.total_messages, 2);
        assert_eq!(progress.queued, 2);
    }

    #[tokio::test]
    async fn test_bulk_manager_cancel_operation() {
        let config = BulkManagerConfig::default();
        let manager = BulkMessageManager::new(config);

        let messages = vec![
            (
                "recipient1".to_string(),
                MessageContent::Text {
                    text: "test1".to_string(),
                    quoted_message_id: None,
                    mentioned_numbers: None,
                },
                MessagePriority::Normal,
            ),
            (
                "recipient2".to_string(),
                MessageContent::Text {
                    text: "test2".to_string(),
                    quoted_message_id: None,
                    mentioned_numbers: None,
                },
                MessagePriority::Normal,
            ),
        ];

        manager
            .queue_messages("test_op".to_string(), messages, Some(3), None)
            .await
            .unwrap();

        manager.cancel_operation("test_op").await.unwrap();

        let progress = manager.get_progress("test_op").await.unwrap();
        assert_eq!(progress.cancelled, 2);
        assert_eq!(progress.queued, 0);
    }
}
