use std::mem::ManuallyDrop;
use std::ops::Deref;
use std::{
    ffi::{CStr, CString},
    os::raw::{c_char, c_int, c_void},
    sync::Arc,
};

use libloading::{Library, Symbol};
use tokio::sync::mpsc;
use tracing::{debug, error, info, instrument, warn};

use crate::core::{
    entities::{
        ConnectionStatusEvent, ErrorEvent, LogLevel, MessageEvent, QRCodeEvent, WhatsAppEvent,
    },
    errors::{Result, WhatsAppError},
    ports::FfiPort,
};

/// Raw event data received from C callback before JSON parsing
#[derive(Debug, Clone)]
pub struct RawEvent {
    pub event_type: i32,
    pub data: String,
    pub timestamp: std::time::Instant,
}

/// Event processing context that handles both raw events and parsed events
pub struct EventProcessor {
    raw_sender: mpsc::UnboundedSender<RawEvent>,
    parsed_sender: Arc<mpsc::UnboundedSender<WhatsAppEvent>>,
}

impl EventProcessor {
    /// Create a new event processor with separate channels for raw and parsed events
    pub fn new(
        parsed_sender: Arc<mpsc::UnboundedSender<WhatsAppEvent>>,
    ) -> (Self, mpsc::UnboundedReceiver<RawEvent>) {
        let (raw_sender, raw_receiver) = mpsc::unbounded_channel();

        let processor = Self {
            raw_sender,
            parsed_sender,
        };

        (processor, raw_receiver)
    }

    /// Get the raw event sender for use in FFI callbacks
    pub fn raw_sender(&self) -> &mpsc::UnboundedSender<RawEvent> {
        &self.raw_sender
    }

    /// Process a raw event into a parsed event (called from background task)
    pub fn parse_raw_event(&self, raw_event: RawEvent) -> Result<WhatsAppEvent> {
        let event = match raw_event.event_type {
            1 => {
                // QR_CODE
                let qr_event: QRCodeEvent = serde_json::from_str(&raw_event.data).map_err(|e| {
                    WhatsAppError::event_parsing_failed(1, format!("QR code parsing failed: {}", e))
                })?;
                WhatsAppEvent::QRCode(qr_event.code)
            }
            2 => {
                // MESSAGE_RECEIVED
                let msg_event: MessageEvent =
                    serde_json::from_str(&raw_event.data).map_err(|e| {
                        WhatsAppError::event_parsing_failed(
                            2,
                            format!("Message parsing failed: {}", e),
                        )
                    })?;
                let message = crate::core::entities::Message {
                    from: msg_event.from,
                    message_type: msg_event.message_type,
                    timestamp: msg_event.timestamp,
                    message_id: msg_event.message_id,
                };
                WhatsAppEvent::MessageReceived(Box::new(message))
            }
            3 => {
                // CONNECTION_STATUS
                let status_event: ConnectionStatusEvent = serde_json::from_str(&raw_event.data)
                    .map_err(|e| {
                        WhatsAppError::event_parsing_failed(
                            3,
                            format!("Connection status parsing failed: {}", e),
                        )
                    })?;
                WhatsAppEvent::ConnectionStatusChanged {
                    status: status_event.status.into(),
                    reason: status_event.reason,
                }
            }
            4 => {
                // ERROR
                let error_event: ErrorEvent =
                    serde_json::from_str(&raw_event.data).map_err(|e| {
                        WhatsAppError::event_parsing_failed(
                            4,
                            format!("Error event parsing failed: {}", e),
                        )
                    })?;
                WhatsAppEvent::Error {
                    code: error_event.code,
                    message: error_event.message,
                }
            }
            _ => {
                return Err(WhatsAppError::invalid_event_type(raw_event.event_type));
            }
        };

        Ok(event)
    }

    /// Send a parsed event to the application
    pub fn send_parsed_event(&self, event: WhatsAppEvent) -> Result<()> {
        self.parsed_sender.send(event).map_err(|e| {
            WhatsAppError::channel_error(
                "send_parsed_event",
                format!("Failed to send parsed event: {}", e),
            )
        })
    }
}

use super::types::*;

/// FFI bindings for the WhatsApp shared library with safe memory management
pub struct FfiBindings {
    // Use ManuallyDrop to control when the library is dropped
    _library: ManuallyDrop<Library>,
    // Function pointers stored safely without transmute
    create_client: CreateClientFn,
    connect: ConnectFn,
    disconnect: DisconnectFn,
    send_message: SendMessageFn,
    is_connected: IsConnectedFn,
    destroy_client: DestroyClientFn,
    get_error_message: GetErrorMessageFn,
    free_string: FreeStringFn,
    set_log_level: SetLogLevelFn,
    get_log_level: GetLogLevelFn,
    set_client_log_level: SetClientLogLevelFn,
    get_client_log_level: GetClientLogLevelFn,
}

impl FfiBindings {
    /// Load the FFI library from the specified path with safe memory management
    #[instrument(level = "debug")]
    pub fn load(library_path: &str) -> Result<Self> {
        info!("Loading FFI library from: {}", library_path);

        unsafe {
            let library = Library::new(library_path).map_err(|e| {
                error!("Failed to load library '{}': {}", library_path, e);
                e
            })?;

            debug!("Successfully loaded library: {}", library_path);

            // Load all function symbols and convert to function pointers safely
            debug!("Loading FFI function symbols...");
            let create_client: Symbol<'_, _> = library
                .get::<CreateClientFn>(b"whatsapp_create_client")
                .map_err(|e| {
                    error!("Failed to load symbol 'whatsapp_create_client': {}", e);
                    WhatsAppError::symbol_not_found("whatsapp_create_client")
                })?;
            let connect = library
                .get::<ConnectFn>(b"whatsapp_connect")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_connect"))?;
            let disconnect = library
                .get::<DisconnectFn>(b"whatsapp_disconnect")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_disconnect"))?;
            let send_message = library
                .get::<SendMessageFn>(b"whatsapp_send_message")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_send_message"))?;
            let is_connected = library
                .get::<IsConnectedFn>(b"whatsapp_is_connected")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_is_connected"))?;
            let destroy_client = library
                .get::<DestroyClientFn>(b"whatsapp_destroy_client")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_destroy_client"))?;
            let get_error_message = library
                .get::<GetErrorMessageFn>(b"whatsapp_get_error_message")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_get_error_message"))?;
            let free_string = library
                .get::<FreeStringFn>(b"whatsapp_free_string")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_free_string"))?;

            // Load logging functions
            let set_log_level = library
                .get::<SetLogLevelFn>(b"whatsapp_set_log_level")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_set_log_level"))?;
            let get_log_level = library
                .get::<GetLogLevelFn>(b"whatsapp_get_log_level")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_get_log_level"))?;
            let set_client_log_level = library
                .get::<SetClientLogLevelFn>(b"whatsapp_set_client_log_level")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_set_client_log_level"))?;
            let get_client_log_level = library
                .get::<GetClientLogLevelFn>(b"whatsapp_get_client_log_level")
                .map_err(|_| WhatsAppError::symbol_not_found("whatsapp_get_client_log_level"))?;

            // Convert symbols to function pointers safely
            // This is safe because we keep the library alive with ManuallyDrop
            let create_client_fn = *create_client.deref();
            let connect_fn = *connect.deref();
            let disconnect_fn = *disconnect.deref();
            let send_message_fn = *send_message.deref();
            let is_connected_fn = *is_connected.deref();
            let destroy_client_fn = *destroy_client.deref();
            let get_error_message_fn = *get_error_message.deref();
            let free_string_fn = *free_string.deref();
            let set_log_level_fn = *set_log_level.deref();
            let get_log_level_fn = *get_log_level.deref();
            let set_client_log_level_fn = *set_client_log_level.deref();
            let get_client_log_level_fn = *get_client_log_level.deref();

            info!("Successfully loaded all FFI symbols from: {}", library_path);
            debug!("FFI bindings initialized successfully");

            Ok(Self {
                // Use ManuallyDrop to prevent automatic cleanup
                _library: ManuallyDrop::new(library),
                // Store function pointers directly without transmute
                create_client: create_client_fn,
                connect: connect_fn,
                disconnect: disconnect_fn,
                send_message: send_message_fn,
                is_connected: is_connected_fn,
                destroy_client: destroy_client_fn,
                get_error_message: get_error_message_fn,
                free_string: free_string_fn,
                set_log_level: set_log_level_fn,
                get_log_level: get_log_level_fn,
                set_client_log_level: set_client_log_level_fn,
                get_client_log_level: get_client_log_level_fn,
            })
        }
    }

    /// Create a new client with optimized event callback processing
    #[instrument(level = "debug", skip(self, event_sender))]
    pub fn create_client_with_callback(
        &self,
        db_path: &str,
        event_sender: Arc<mpsc::UnboundedSender<WhatsAppEvent>>,
    ) -> Result<(usize, mpsc::UnboundedReceiver<RawEvent>)> {
        info!("Creating WhatsApp client with optimized event processing");
        debug!("Database path: {}", db_path);

        let db_path_cstr = CString::new(db_path).map_err(|e| {
            error!("Invalid db_path string '{}': contains null bytes", db_path);
            WhatsAppError::Internal(format!("Invalid db_path string: {}", e))
        })?;

        // Create event processor for optimized event handling
        debug!("Creating event processor for optimized callback handling");
        let (event_processor, raw_receiver) = EventProcessor::new(event_sender);

        // Box the event processor to pass as user data
        let user_data = Box::into_raw(Box::new(event_processor)) as *mut c_void;
        debug!(
            "Event processor boxed for FFI callback, user_data: {:p}",
            user_data
        );

        unsafe {
            let handle =
                (self.create_client)(db_path_cstr.as_ptr(), optimized_event_callback, user_data);

            if handle == 0 {
                error!("FFI create_client returned null handle");
                // Clean up user data on failure
                let _ = Box::from_raw(user_data as *mut EventProcessor);
                return Err(WhatsAppError::Internal(
                    "Failed to create client - FFI returned null handle".to_string(),
                ));
            }

            info!(
                "Successfully created WhatsApp client with handle: {}",
                handle
            );
            debug!("Optimized event processing enabled for handle: {}", handle);
            Ok((handle, raw_receiver))
        }
    }

    /// Legacy method for backward compatibility - creates client with old callback system
    pub fn create_client_with_callback_legacy(
        &self,
        db_path: &str,
        event_sender: Arc<mpsc::UnboundedSender<WhatsAppEvent>>,
    ) -> Result<usize> {
        let db_path_cstr = CString::new(db_path)
            .map_err(|_| WhatsAppError::Internal("Invalid db_path string".to_string()))?;

        // Box the sender to pass as user data (legacy approach)
        let user_data = Box::into_raw(Box::new(event_sender)) as *mut c_void;

        unsafe {
            let handle = (self.create_client)(db_path_cstr.as_ptr(), event_callback, user_data);

            if handle == 0 {
                // Clean up user data on failure
                let _ = Box::from_raw(user_data as *mut Arc<mpsc::UnboundedSender<WhatsAppEvent>>);
                return Err(WhatsAppError::Internal(
                    "Failed to create client".to_string(),
                ));
            }

            Ok(handle)
        }
    }

    /// Get error message for an error code
    pub fn get_error_message(&self, error_code: i32) -> String {
        unsafe {
            let msg_ptr = (self.get_error_message)(error_code);
            if msg_ptr.is_null() {
                return "Unknown error".to_string();
            }

            let cstr = CStr::from_ptr(msg_ptr);
            let result = cstr.to_string_lossy().to_string();
            (self.free_string)(msg_ptr);
            result
        }
    }
}

impl FfiPort for FfiBindings {
    #[instrument(level = "debug", skip(self))]
    fn connect(&self, handle: usize) -> Result<()> {
        debug!("Attempting to connect client with handle: {}", handle);

        unsafe {
            let result = (self.connect)(handle);
            if result != 0 {
                let error_msg = self.get_error_message(result);
                error!(
                    "Connection failed for handle {}: {} (code: {})",
                    handle, error_msg, result
                );
                return Err(WhatsAppError::from(result));
            }
            info!("Successfully connected client with handle: {}", handle);
            Ok(())
        }
    }

    #[instrument(level = "debug", skip(self))]
    fn disconnect(&self, handle: usize) -> Result<()> {
        debug!("Attempting to disconnect client with handle: {}", handle);

        unsafe {
            let result = (self.disconnect)(handle);
            if result != 0 {
                let error_msg = self.get_error_message(result);
                error!(
                    "Disconnection failed for handle {}: {} (code: {})",
                    handle, error_msg, result
                );
                return Err(WhatsAppError::from(result));
            }
            info!("Successfully disconnected client with handle: {}", handle);
            Ok(())
        }
    }

    #[instrument(level = "debug", skip(self, text), fields(jid = %jid, text_len = text.len()))]
    fn send_message(&self, handle: usize, jid: &str, text: &str) -> Result<()> {
        debug!(
            "Attempting to send message to {} from handle {}",
            jid, handle
        );

        let jid_cstr = CString::new(jid).map_err(|e| {
            error!("Invalid JID '{}': contains null bytes", jid);
            WhatsAppError::invalid_jid(jid, format!("Contains null bytes: {}", e))
        })?;
        let text_cstr = CString::new(text).map_err(|e| {
            error!("Invalid message text: contains null bytes");
            WhatsAppError::Internal(format!("Invalid text string - contains null bytes: {}", e))
        })?;

        unsafe {
            let result = (self.send_message)(handle, jid_cstr.as_ptr(), text_cstr.as_ptr());
            if result != 0 {
                let error_msg = self.get_error_message(result);
                error!(
                    "Failed to send message to {} from handle {}: {} (code: {})",
                    jid, handle, error_msg, result
                );
                return Err(WhatsAppError::send_failed(jid, error_msg));
            }
            info!(
                "Successfully sent message to {} from handle {}",
                jid, handle
            );
            Ok(())
        }
    }

    #[instrument(level = "debug", skip(self))]
    fn is_connected(&self, handle: usize) -> Result<bool> {
        let connected = unsafe {
            let result = (self.is_connected)(handle);
            result != 0
        };
        debug!("Connection status for handle {}: {}", handle, connected);
        Ok(connected)
    }

    #[instrument(level = "debug", skip(self))]
    fn destroy_client(&self, handle: usize) -> Result<()> {
        debug!("Attempting to destroy client with handle: {}", handle);

        unsafe {
            let result = (self.destroy_client)(handle);
            if result != 0 {
                let error_msg = self.get_error_message(result);
                error!(
                    "Failed to destroy client with handle {}: {} (code: {})",
                    handle, error_msg, result
                );
                return Err(WhatsAppError::from(result));
            }
            info!("Successfully destroyed client with handle: {}", handle);
            Ok(())
        }
    }
}

/// Optimized event callback that only queues raw events for background processing
unsafe extern "C" fn optimized_event_callback(
    event_type: c_int,
    data: *const c_char,
    user_data: *mut c_void,
) {
    // Add panic boundary to prevent panics from propagating to C
    let result = std::panic::catch_unwind(|| {
        // Validate inputs first - minimal validation for performance
        if user_data.is_null() || data.is_null() {
            return;
        }

        // Bounds check event_type - only accept valid event types (1-4)
        if !(1..=4).contains(&event_type) {
            return;
        }

        // Fast path: just copy data and queue for background processing
        queue_raw_event_fast(event_type, data, user_data);
    });

    if result.is_err() {
        // Minimal error handling in callback - just log and continue
        tracing::error!("Panic in optimized_event_callback");
    }
}

/// Fast raw event queuing - minimal processing in C callback thread
fn queue_raw_event_fast(event_type: c_int, data: *const c_char, user_data: *mut c_void) {
    // Get the event processor from user data
    let event_processor = unsafe {
        // Quick alignment check
        if (user_data as usize) % std::mem::align_of::<EventProcessor>() != 0 {
            tracing::error!("Misaligned user_data pointer in optimized callback");
            return;
        }
        &*(user_data as *const EventProcessor)
    };

    // Copy C string data immediately - this is the only potentially expensive operation
    let data_copy = match unsafe { CStr::from_ptr(data).to_string_lossy() } {
        std::borrow::Cow::Borrowed(s) => s.to_string(),
        std::borrow::Cow::Owned(s) => s,
    };

    // Create raw event with timestamp
    let raw_event = RawEvent {
        event_type,
        data: data_copy,
        timestamp: std::time::Instant::now(),
    };

    // Send to background processing queue - non-blocking
    if event_processor.raw_sender().send(raw_event).is_err() {
        // Channel closed - this is expected during shutdown, so don't log at error level
        tracing::debug!("Raw event channel closed - likely during shutdown");
    } else {
        // Only log at trace level to avoid spam
        tracing::trace!(
            "Queued raw event type {} for background processing",
            event_type
        );
    }
}

/// Legacy event callback function with full processing in callback thread
/// Kept for backward compatibility
unsafe extern "C" fn event_callback(
    event_type: c_int,
    data: *const c_char,
    user_data: *mut c_void,
) {
    // Add panic boundary to prevent panics from propagating to C
    let result = std::panic::catch_unwind(|| {
        // Validate inputs first
        if user_data.is_null() {
            tracing::warn!("event_callback: Null user_data pointer received");
            return;
        }

        if data.is_null() {
            tracing::warn!("event_callback: Null data pointer received");
            return;
        }

        // Bounds check event_type - only accept valid event types (1-4)
        if !(1..=4).contains(&event_type) {
            tracing::warn!("event_callback: Invalid event_type: {}", event_type);
            return;
        }

        // Safe processing with proper error handling
        process_event_safely(event_type, data, user_data);
    });

    if let Err(panic_info) = result {
        // Log panic but don't propagate to C - this is critical for FFI safety
        let panic_details = if let Some(s) = panic_info.downcast_ref::<&str>() {
            s.to_string()
        } else if let Some(s) = panic_info.downcast_ref::<String>() {
            s.clone()
        } else {
            "Unknown panic".to_string()
        };

        tracing::error!("Panic caught in event_callback: {}", panic_details);
        // In production, you might want to report this to a monitoring system
        // For now, we just log it and continue
    }
}

/// Internal function to safely process events with proper error handling
fn process_event_safely(event_type: c_int, data: *const c_char, user_data: *mut c_void) {
    // Reconstruct the sender from user data with additional safety checks
    let sender = unsafe {
        // Additional validation: ensure the pointer is properly aligned
        if (user_data as usize) % std::mem::align_of::<Arc<mpsc::UnboundedSender<WhatsAppEvent>>>()
            != 0
        {
            tracing::error!("event_callback: Misaligned user_data pointer");
            return;
        }
        &*(user_data as *const Arc<mpsc::UnboundedSender<WhatsAppEvent>>)
    };

    // Convert C string to Rust string with better error handling
    let data_str = match unsafe { CStr::from_ptr(data).to_str() } {
        Ok(s) => s,
        Err(e) => {
            tracing::warn!("event_callback: Failed to convert C string to UTF-8: {}", e);
            return;
        }
    };

    // Parse event based on type with improved error reporting
    let event = match event_type {
        1 => {
            // QR_CODE
            match serde_json::from_str::<QRCodeEvent>(data_str) {
                Ok(qr_event) => WhatsAppEvent::QRCode(qr_event.code),
                Err(e) => {
                    tracing::warn!("event_callback: Failed to parse QR code event: {}", e);
                    return;
                }
            }
        }
        2 => {
            // MESSAGE_RECEIVED
            match serde_json::from_str::<MessageEvent>(data_str) {
                Ok(msg_event) => {
                    let message = crate::core::entities::Message {
                        from: msg_event.from,
                        message_type: msg_event.message_type,
                        timestamp: msg_event.timestamp,
                        message_id: msg_event.message_id,
                    };
                    WhatsAppEvent::MessageReceived(Box::new(message))
                }
                Err(e) => {
                    tracing::warn!("event_callback: Failed to parse message event: {}", e);
                    return;
                }
            }
        }
        3 => {
            // CONNECTION_STATUS
            match serde_json::from_str::<ConnectionStatusEvent>(data_str) {
                Ok(status_event) => WhatsAppEvent::ConnectionStatusChanged {
                    status: status_event.status.into(),
                    reason: status_event.reason,
                },
                Err(e) => {
                    tracing::warn!(
                        "event_callback: Failed to parse connection status event: {}",
                        e
                    );
                    return;
                }
            }
        }
        4 => {
            // ERROR
            match serde_json::from_str::<ErrorEvent>(data_str) {
                Ok(error_event) => WhatsAppEvent::Error {
                    code: error_event.code,
                    message: error_event.message,
                },
                Err(e) => {
                    tracing::warn!("event_callback: Failed to parse error event: {}", e);
                    return;
                }
            }
        }
        _ => {
            // This should never happen due to bounds checking above, but keep for safety
            tracing::error!(
                "event_callback: Unexpected event_type after bounds check: {}",
                event_type
            );
            return;
        }
    };

    // Send event through channel with better error handling
    if let Err(e) = sender.send(event) {
        tracing::warn!(
            "event_callback: Failed to send event through channel: {}",
            e
        );
        // Channel is likely closed, but this is not a critical error for the callback
    }
}

/// Background event processor that handles raw events from the optimized callback
pub async fn process_events_background(
    mut raw_receiver: mpsc::UnboundedReceiver<RawEvent>,
    event_processor: Arc<EventProcessor>,
) {
    tracing::info!("Starting background event processor");

    let mut processed_count = 0u64;
    let mut error_count = 0u64;
    let start_time = std::time::Instant::now();

    while let Some(raw_event) = raw_receiver.recv().await {
        // Calculate processing latency
        let processing_latency = raw_event.timestamp.elapsed();

        // Parse the raw event
        match event_processor.parse_raw_event(raw_event) {
            Ok(parsed_event) => {
                // Send parsed event to application
                if let Err(e) = event_processor.send_parsed_event(parsed_event) {
                    error_count += 1;
                    tracing::warn!("Failed to send parsed event: {}", e);
                } else {
                    processed_count += 1;

                    // Log performance metrics periodically
                    if processed_count % 100 == 0 {
                        let elapsed = start_time.elapsed();
                        let rate = processed_count as f64 / elapsed.as_secs_f64();
                        tracing::debug!(
                            "Event processing stats: {} processed, {} errors, {:.2} events/sec, last latency: {:?}",
                            processed_count, error_count, rate, processing_latency
                        );
                    }
                }
            }
            Err(e) => {
                error_count += 1;
                tracing::warn!("Failed to parse raw event: {}", e);

                // Log error details for debugging
                if tracing::enabled!(tracing::Level::DEBUG) {
                    tracing::debug!("Raw event that failed to parse: {:?}", e);
                }
            }
        }

        // Warn if processing latency is high
        if processing_latency > std::time::Duration::from_millis(100) {
            tracing::warn!("High event processing latency: {:?}", processing_latency);
        }
    }

    tracing::info!(
        "Background event processor stopped. Final stats: {} processed, {} errors",
        processed_count,
        error_count
    );
}

/// Utility function to spawn the background event processor
pub fn spawn_event_processor(
    raw_receiver: mpsc::UnboundedReceiver<RawEvent>,
    event_processor: Arc<EventProcessor>,
) -> tokio::task::JoinHandle<()> {
    tokio::spawn(async move {
        process_events_background(raw_receiver, event_processor).await;
    })
}

impl FfiBindings {
    /// Set global log level
    #[instrument(level = "debug", skip(self))]
    pub fn set_global_log_level(&self, level: LogLevel) -> Result<()> {
        debug!("Setting global log level to: {:?}", level);

        let result = unsafe { (self.set_log_level)(level as c_int) };
        if result == 0 {
            info!("Successfully set global log level to: {:?}", level);
            Ok(())
        } else {
            error!(
                "Failed to set global log level to {:?}: FFI returned code {}",
                level, result
            );
            Err(WhatsAppError::ffi_call_failed(
                result,
                format!("Failed to set global log level to {:?}", level),
            ))
        }
    }

    /// Get global log level
    #[instrument(level = "debug", skip(self))]
    pub fn get_global_log_level(&self) -> Result<LogLevel> {
        let result = unsafe { (self.get_log_level)() };
        match LogLevel::from_i32(result) {
            Some(level) => {
                debug!("Current global log level: {:?}", level);
                Ok(level)
            }
            None => {
                error!("Invalid log level returned from FFI: {}", result);
                Err(WhatsAppError::InvalidLogLevel { level: result })
            }
        }
    }

    /// Set client-specific log level
    #[instrument(level = "debug", skip(self))]
    pub fn set_client_log_level(&self, handle: usize, level: LogLevel) -> Result<()> {
        debug!(
            "Setting log level to {:?} for client handle: {}",
            level, handle
        );

        let result = unsafe { (self.set_client_log_level)(handle, level as c_int) };
        if result == 0 {
            info!(
                "Successfully set log level to {:?} for client handle: {}",
                level, handle
            );
            Ok(())
        } else {
            error!(
                "Failed to set log level to {:?} for client handle {}: FFI returned code {}",
                level, handle, result
            );
            Err(WhatsAppError::ffi_call_failed(
                result,
                format!(
                    "Failed to set client log level to {:?} for handle {}",
                    level, handle
                ),
            ))
        }
    }

    /// Get client-specific log level
    #[instrument(level = "debug", skip(self))]
    pub fn get_client_log_level(&self, handle: usize) -> Result<LogLevel> {
        let result = unsafe { (self.get_client_log_level)(handle) };
        if result < 0 {
            error!(
                "Failed to get log level for client handle {}: FFI returned code {}",
                handle, result
            );
            return Err(WhatsAppError::ffi_call_failed(
                result,
                format!("Failed to get client log level for handle {}", handle),
            ));
        }
        match LogLevel::from_i32(result) {
            Some(level) => {
                debug!(
                    "Current log level for client handle {}: {:?}",
                    handle, level
                );
                Ok(level)
            }
            None => {
                error!(
                    "Invalid log level returned for client handle {}: {}",
                    handle, result
                );
                Err(WhatsAppError::InvalidLogLevel { level: result })
            }
        }
    }
}

/// Implement Drop for safe cleanup of the library
impl Drop for FfiBindings {
    fn drop(&mut self) {
        info!("Dropping FFI bindings - cleaning up library resources");

        // Manually drop the library to ensure proper cleanup
        // This is safe because we control the lifetime
        unsafe {
            ManuallyDrop::drop(&mut self._library);
        }

        info!("FFI bindings dropped successfully - library resources cleaned up");
    }
}
