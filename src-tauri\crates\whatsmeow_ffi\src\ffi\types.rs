use std::os::raw::{c_char, c_int, c_void};

/// Event types matching the C enum
#[repr(C)]
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum EventType {
    QRCode = 1,
    MessageReceived = 2,
    ConnectionStatus = 3,
    Error = 4,
}

/// Connection status matching the C enum
#[repr(C)]
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum ConnectionStatus {
    Disconnected = 0,
    Connecting = 1,
    Connected = 2,
    LoggedOut = 3,
}

/// Error codes matching the C enum
#[repr(C)]
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum ErrorCode {
    None = 0,
    InvalidHandle = 1,
    NotConnected = 2,
    InvalidJid = 3,
    SendFailed = 4,
    Internal = 5,
}

/// Log levels matching the C enum
#[repr(C)]
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum LogLevel {
    Off = 0,
    Error = 1,
    Warn = 2,
    Info = 3,
    Debug = 4,
}

/// Event callback function pointer type
pub type EventCallback =
    unsafe extern "C" fn(event_type: c_int, data: *const c_char, user_data: *mut c_void);

/// FFI function signatures
pub type CreateClientFn = unsafe extern "C" fn(
    db_path: *const c_char,
    callback: EventCallback,
    user_data: *mut c_void,
) -> usize;

pub type ConnectFn = unsafe extern "C" fn(handle: usize) -> c_int;
pub type DisconnectFn = unsafe extern "C" fn(handle: usize) -> c_int;
pub type SendMessageFn =
    unsafe extern "C" fn(handle: usize, jid: *const c_char, text: *const c_char) -> c_int;
pub type IsConnectedFn = unsafe extern "C" fn(handle: usize) -> c_int;
pub type DestroyClientFn = unsafe extern "C" fn(handle: usize) -> c_int;
pub type GetErrorMessageFn = unsafe extern "C" fn(error_code: c_int) -> *mut c_char;
pub type FreeStringFn = unsafe extern "C" fn(str: *mut c_char);

// Logging function signatures
pub type SetLogLevelFn = unsafe extern "C" fn(level: c_int) -> c_int;
pub type GetLogLevelFn = unsafe extern "C" fn() -> c_int;
pub type SetClientLogLevelFn = unsafe extern "C" fn(handle: usize, level: c_int) -> c_int;
pub type GetClientLogLevelFn = unsafe extern "C" fn(handle: usize) -> c_int;
