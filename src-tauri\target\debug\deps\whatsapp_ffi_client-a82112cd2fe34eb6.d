D:\programming\desktop-apps\whatsapp-sender-pro\src-tauri\target\debug\deps\whatsapp_ffi_client-a82112cd2fe34eb6.exe: crates\whatsmeow_ffi\src\lib.rs crates\whatsmeow_ffi\src\core\mod.rs crates\whatsmeow_ffi\src\core\connection_manager.rs crates\whatsmeow_ffi\src\core\entities.rs crates\whatsmeow_ffi\src\core\errors.rs crates\whatsmeow_ffi\src\core\ports.rs crates\whatsmeow_ffi\src\core\event_aggregator.rs crates\whatsmeow_ffi\src\ffi\mod.rs crates\whatsmeow_ffi\src\ffi\bindings.rs crates\whatsmeow_ffi\src\ffi\types.rs crates\whatsmeow_ffi\src\infra\mod.rs crates\whatsmeow_ffi\src\infra\whatsapp_adapter.rs crates\whatsmeow_ffi\src\interface\mod.rs

D:\programming\desktop-apps\whatsapp-sender-pro\src-tauri\target\debug\deps\whatsapp_ffi_client-a82112cd2fe34eb6.d: crates\whatsmeow_ffi\src\lib.rs crates\whatsmeow_ffi\src\core\mod.rs crates\whatsmeow_ffi\src\core\connection_manager.rs crates\whatsmeow_ffi\src\core\entities.rs crates\whatsmeow_ffi\src\core\errors.rs crates\whatsmeow_ffi\src\core\ports.rs crates\whatsmeow_ffi\src\core\event_aggregator.rs crates\whatsmeow_ffi\src\ffi\mod.rs crates\whatsmeow_ffi\src\ffi\bindings.rs crates\whatsmeow_ffi\src\ffi\types.rs crates\whatsmeow_ffi\src\infra\mod.rs crates\whatsmeow_ffi\src\infra\whatsapp_adapter.rs crates\whatsmeow_ffi\src\interface\mod.rs

crates\whatsmeow_ffi\src\lib.rs:
crates\whatsmeow_ffi\src\core\mod.rs:
crates\whatsmeow_ffi\src\core\connection_manager.rs:
crates\whatsmeow_ffi\src\core\entities.rs:
crates\whatsmeow_ffi\src\core\errors.rs:
crates\whatsmeow_ffi\src\core\ports.rs:
crates\whatsmeow_ffi\src\core\event_aggregator.rs:
crates\whatsmeow_ffi\src\ffi\mod.rs:
crates\whatsmeow_ffi\src\ffi\bindings.rs:
crates\whatsmeow_ffi\src\ffi\types.rs:
crates\whatsmeow_ffi\src\infra\mod.rs:
crates\whatsmeow_ffi\src\infra\whatsapp_adapter.rs:
crates\whatsmeow_ffi\src\interface\mod.rs:
