//! Database migration utilities for the WhatsApp Sender Pro application.
//!
//! This crate provides database migration functionality using SeaORM,
//! allowing for schema changes and database setup operations.

#![deny(missing_docs)]

use sea_orm_migration::{DbErr, MigrationTrait, MigratorTrait, sea_orm::DatabaseConnection};

/// Database migrator struct that implements SeaORM's MigratorTrait.
///
/// This struct is responsible for managing database schema migrations
/// and ensuring the database is up to date with the latest schema changes.
pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![]
    }
}

/// Runs all pending database migrations.
///
/// This function applies all pending migrations to bring the database
/// schema up to the latest version. It should be called during application
/// startup to ensure the database is properly initialized.
///
/// # Arguments
///
/// * `db` - A reference to the database connection
///
/// # Returns
///
/// Returns `Ok(())` if all migrations were applied successfully,
/// or a `DbErr` if any migration failed.
///
/// # Errors
///
/// This function will return an error if:
/// - The database connection is invalid
/// - A migration fails to apply
/// - There are database permission issues
pub async fn run_migrations(db: &DatabaseConnection) -> Result<(), DbErr> {
    Migrator::up(db, None).await
}
