use std::sync::Arc;

use async_trait::async_trait;
use tokio::sync::mpsc;

use crate::{
    core::{
        entities::{LogLevel, WhatsAppEvent},
        errors::{Result, WhatsAppError},
        ports::{FfiPort, WhatsAppClientPort},
    },
    ffi::bindings::{spawn_event_processor, EventProcessor, FfiBindings},
};

/// Adapter that implements WhatsAppClientPort using FFI bindings
pub struct WhatsAppAdapter {
    ffi: Arc<FfiBindings>,
    handle: usize,
    event_receiver: Option<mpsc::UnboundedReceiver<WhatsAppEvent>>,
    destroyed: std::sync::atomic::AtomicBool,
    _background_processor: Option<tokio::task::Join<PERSON>andle<()>>,
}

impl WhatsAppAdapter {
    /// Create a new WhatsApp adapter with optimized event processing
    pub fn new(library_path: &str, db_path: &str) -> Result<Self> {
        let ffi = Arc::new(FfiBindings::load(library_path)?);

        // Create event channel for parsed events
        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        let event_sender = Arc::new(event_sender);

        // Create client with optimized callback that returns raw event receiver
        let (handle, raw_receiver) =
            ffi.create_client_with_callback(db_path, event_sender.clone())?;

        // Create event processor and spawn background processing task
        let (event_processor, _) = EventProcessor::new(event_sender);
        let event_processor = Arc::new(event_processor);
        let background_processor = spawn_event_processor(raw_receiver, event_processor);

        Ok(Self {
            ffi,
            handle,
            event_receiver: Some(event_receiver),
            destroyed: std::sync::atomic::AtomicBool::new(false),
            _background_processor: Some(background_processor),
        })
    }

    /// Get the client handle
    pub fn handle(&self) -> usize {
        self.handle
    }

    /// Take the event receiver (can only be called once)
    pub fn event_receiver(&mut self) -> Result<mpsc::UnboundedReceiver<WhatsAppEvent>> {
        self.event_receiver
            .take()
            .ok_or_else(|| WhatsAppError::Internal("Event receiver already taken".to_string()))
    }

    /// Async-compatible destroy method that doesn't block the runtime
    pub async fn destroy_async(&self) -> Result<()> {
        if !self
            .destroyed
            .swap(true, std::sync::atomic::Ordering::SeqCst)
        {
            tracing::info!("Destroying client handle asynchronously: {}", self.handle);

            let ffi = Arc::clone(&self.ffi);
            let handle = self.handle;

            // Use spawn_blocking for async-compatible FFI cleanup
            tokio::task::spawn_blocking(move || {
                tracing::info!("Calling FFI destroy_client for handle {}", handle);
                let result = ffi.destroy_client(handle);
                match &result {
                    Ok(_) => tracing::info!("Successfully destroyed client handle {}", handle),
                    Err(e) => tracing::error!("Failed to destroy client handle {}: {}", handle, e),
                }
                result
            })
            .await
            .map_err(|e| {
                tracing::error!("Task join error during client destruction: {}", e);
                WhatsAppError::Internal(format!("Task join error during destruction: {}", e))
            })??;

            tracing::info!(
                "Client handle {} destruction completed asynchronously",
                self.handle
            );
        }
        Ok(())
    }

    /// Check if the client has been destroyed
    pub fn is_destroyed(&self) -> bool {
        self.destroyed.load(std::sync::atomic::Ordering::SeqCst)
    }
}

#[async_trait]
impl WhatsAppClientPort for WhatsAppAdapter {
    async fn connect(&self) -> Result<()> {
        // Run FFI call in blocking task to avoid blocking async runtime
        let ffi = Arc::clone(&self.ffi);
        let handle = self.handle;

        tokio::task::spawn_blocking(move || ffi.connect(handle))
            .await
            .map_err(|e| WhatsAppError::Internal(format!("Task join error: {}", e)))?
    }

    async fn disconnect(&self) -> Result<()> {
        tracing::info!("Disconnecting WhatsApp client handle {}", self.handle);

        let ffi = Arc::clone(&self.ffi);
        let handle = self.handle;

        let result = tokio::task::spawn_blocking(move || {
            tracing::info!("Calling FFI disconnect for handle {}", handle);
            ffi.disconnect(handle)
        })
        .await
        .map_err(|e| WhatsAppError::Internal(format!("Task join error: {}", e)))?;

        match &result {
            Ok(_) => tracing::info!("Successfully disconnected client handle {}", self.handle),
            Err(e) => tracing::warn!("Error disconnecting client handle {}: {}", self.handle, e),
        }

        // After disconnect attempt, destroy the client handle
        // This ensures cleanup even if disconnect failed
        self.destroy_async().await?;

        result
    }

    async fn send_message(&self, jid: &str, text: &str) -> Result<()> {
        let ffi = Arc::clone(&self.ffi);
        let handle = self.handle;
        let jid = jid.to_string();
        let text = text.to_string();

        tokio::task::spawn_blocking(move || ffi.send_message(handle, &jid, &text))
            .await
            .map_err(|e| WhatsAppError::Internal(format!("Task join error: {}", e)))?
    }

    async fn is_connected(&self) -> Result<bool> {
        let ffi = Arc::clone(&self.ffi);
        let handle = self.handle;

        tokio::task::spawn_blocking(move || ffi.is_connected(handle))
            .await
            .map_err(|e| WhatsAppError::Internal(format!("Task join error: {}", e)))?
    }

    fn has_events(&self) -> bool {
        self.event_receiver.is_some()
    }

    fn set_global_log_level(&self, level: LogLevel) -> Result<()> {
        self.ffi.set_global_log_level(level)
    }

    fn get_global_log_level(&self) -> Result<LogLevel> {
        self.ffi.get_global_log_level()
    }

    fn set_client_log_level(&self, level: LogLevel) -> Result<()> {
        self.ffi.set_client_log_level(self.handle, level)
    }

    fn get_client_log_level(&self) -> Result<LogLevel> {
        self.ffi.get_client_log_level(self.handle)
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

impl Drop for WhatsAppAdapter {
    fn drop(&mut self) {
        tracing::info!("Dropping WhatsAppAdapter with handle {}", self.handle);

        // Since we can't use async in Drop, we need to spawn a task for cleanup in the background
        // This is a fallback for cases where destroy_async wasn't called explicitly
        if !self.destroyed.load(std::sync::atomic::Ordering::SeqCst) {
            tracing::warn!("WhatsAppAdapter dropped without explicit destroy_async call - using fallback cleanup");

            // Create a clone of self for the async task
            let adapter_clone = WhatsAppAdapter {
                ffi: Arc::clone(&self.ffi),
                handle: self.handle,
                event_receiver: None, // Already taken or not needed for cleanup
                destroyed: std::sync::atomic::AtomicBool::new(false),
                _background_processor: None,
            };

            // Use tokio::task::spawn to call destroy_async
            tokio::task::spawn(async move {
                tracing::info!(
                    "Fallback: Calling destroy_async for handle {}",
                    adapter_clone.handle
                );
                if let Err(e) = adapter_clone.destroy_async().await {
                    tracing::error!(
                        "Fallback cleanup failed for handle {}: {}",
                        adapter_clone.handle,
                        e
                    );
                } else {
                    tracing::info!(
                        "Fallback cleanup completed for handle {}",
                        adapter_clone.handle
                    );
                }
            });

            // Note: We don't await the task in Drop since Drop must be synchronous
            // The cleanup will happen in the background
        }

        tracing::info!("WhatsAppAdapter drop completed for handle {}", self.handle);
    }
}
