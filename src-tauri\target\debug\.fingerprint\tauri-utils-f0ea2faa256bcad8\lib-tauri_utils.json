{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 7612950396392890533, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 15495546638958747663], [2995469292676432503, "uuid", false, 6769240453884269147], [3129130049864710036, "memchr", false, 2482764352869297290], [3150220818285335163, "url", false, 16586463868209902736], [3191507132440681679, "serde_untagged", false, 3352916838612899264], [4899080583175475170, "semver", false, 818495799722532413], [5986029879202738730, "log", false, 3210362874149672375], [6213549728662707793, "serde_with", false, 17294102683623283372], [6606131838865521726, "ctor", false, 10319392224565566374], [7170110829644101142, "json_patch", false, 7291882358991802797], [8786711029710048183, "toml", false, 7835229229973180693], [9010263965687315507, "http", false, 3354175764241350141], [9451456094439810778, "regex", false, 1995452313642458860], [9556762810601084293, "brotli", false, 16181042358721137845], [9689903380558560274, "serde", false, 10996426815917291500], [10806645703491011684, "thiserror", false, 44651847147331727], [11989259058781683633, "dunce", false, 13340844955090052542], [13625485746686963219, "anyhow", false, 8308486080552771400], [15622660310229662834, "walkdir", false, 3015811392471448980], [16362055519698394275, "serde_json", false, 7262301444845053042], [17146114186171651583, "infer", false, 13173404494087578228], [17155886227862585100, "glob", false, 16192797392537921819], [17186037756130803222, "phf", false, 3978057940454508716]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-f0ea2faa256bcad8\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}