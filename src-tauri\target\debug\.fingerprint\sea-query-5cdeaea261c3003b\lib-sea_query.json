{"rustc": 16591470773350601817, "features": "[\"backend-mysql\", \"backend-postgres\", \"backend-sqlite\", \"bigdecimal\", \"chrono\", \"derive\", \"hashable-value\", \"ordered-float\", \"postgres-array\", \"rust_decimal\", \"sea-query-derive\", \"serde_json\", \"thread-safe\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"all-features\", \"all-types\", \"attr\", \"backend-mysql\", \"backend-postgres\", \"backend-sqlite\", \"bigdecimal\", \"chrono\", \"default\", \"derive\", \"hashable-value\", \"ipnetwork\", \"mac_address\", \"option-more-parentheses\", \"option-sqlite-exact-column-type\", \"ordered-float\", \"pgvector\", \"postgres-array\", \"postgres-interval\", \"postgres-types\", \"postgres-vector\", \"rust_decimal\", \"sea-query-derive\", \"serde_json\", \"tests-cfg\", \"thread-safe\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 17362542534165460642, "profile": 15657897354478470176, "path": 1105030420448245761, "deps": [[1117455883785908841, "inherent", false, 2399215757878492937], [2995469292676432503, "uuid", false, 6769240453884269147], [9897246384292347999, "chrono", false, 541124106390407368], [11074247395802926746, "ordered_float", false, 6376604423478772819], [12409575957772518135, "time", false, 16913109520808322110], [14647456484942590313, "bigdecimal", false, 16054569543174291623], [14806645394729624434, "sea_query_derive", false, 15963054597833485243], [16119793329258425851, "rust_decimal", false, 15692620249086610884], [16362055519698394275, "serde_json", false, 7262301444845053042]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-query-5cdeaea261c3003b\\dep-lib-sea_query", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}