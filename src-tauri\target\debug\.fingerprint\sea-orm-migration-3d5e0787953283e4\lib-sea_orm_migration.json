{"rustc": 16591470773350601817, "features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlx-postgres\"]", "declared_features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlite-use-returning-for-3_35\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 4990465507743545796, "profile": 15657897354478470176, "path": 17667846755963464982, "deps": [[3405707034081185165, "dotenvy", false, 9497081734834712602], [6991425963735389556, "sea_orm", false, 8979077302387389665], [8606274917505247608, "tracing", false, 13752143806742742294], [10686516203231854806, "sea_schema", false, 14277225735214557359], [11946729385090170470, "async_trait", false, 2389517705436373950], [16179757479889401155, "sea_orm_cli", false, 16058178225214214291], [16230660778393187092, "tracing_subscriber", false, 10624335275707051079], [17612818546626403359, "clap", false, 16621575627164328192]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-migration-3d5e0787953283e4\\dep-lib-sea_orm_migration", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}