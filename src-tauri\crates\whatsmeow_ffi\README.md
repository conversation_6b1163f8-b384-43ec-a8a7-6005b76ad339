# WhatsApp FFI Integration

A comprehensive FFI solution that bridges Go's [Whatsmeow](https://github.com/tulir/whatsmeow) library with Rust, providing a clean, async, and type-safe interface for WhatsApp messaging functionality.

## Architecture

This project follows **Clean Architecture** principles with clear separation of concerns:

```
whatsmeow-ffi/
├── go-lib/              # Go shared library (FFI provider)
│   ├── main.go          # C exports and FFI interface
│   ├── whatsapp.go      # Whatsmeow wrapper and business logic
│   ├── types.go         # Shared types and event handling
└── rust-client/         # Rust client (FFI consumer)
    ├── src/
    │   ├── core/        # Domain logic (entities, errors, ports)
    │   ├── ffi/         # Low-level unsafe FFI bindings
    │   ├── infra/       # Safe FFI wrappers and adapters
    │   └── interface/   # High-level user API
    └── examples/        # Usage examples
```

## Features

### Go Shared Library

- ✅ **cgo exports** for C-compatible interface
- ✅ **Whatsmeow integration** with full lifecycle management
- ✅ **Event-driven architecture** with JSON-serialized callbacks
- ✅ **Connection management** (connect, disconnect, cleanup)
- ✅ **QR code generation** for WhatsApp Web login
- ✅ **Comprehensive message handling** (text, image, video, audio, document, sticker)
- ✅ **Rich media support** with detailed metadata and thumbnails
- ✅ **Comprehensive error handling** with typed error codes and panic recovery
- ✅ **Memory safety** with proper cleanup and resource management
- ✅ **Multi-client support** with handle-based registry
- ✅ **Thread-safe operations** with mutex protection and callback safety
- ✅ **Configurable logging system** with 5 levels (Off, Error, Warn, Info, Debug)
- ✅ **Per-client and global log level control** via FFI interface
- ✅ **WhatsApp library logging integration** with custom adapters

### Rust Client

- ✅ **Clean Architecture** with 4-layer design (core/ffi/infra/interface)
- ✅ **Async/await support** with full Tokio integration
- ✅ **Type-safe FFI** with safe wrappers over unsafe bindings
- ✅ **Event streams** for async notifications via channels
- ✅ **RAII cleanup** with automatic resource management
- ✅ **Builder pattern** for ergonomic client configuration
- ✅ **Rich error types** with `thiserror` and detailed error context
- ✅ **Beautiful examples** with terminal UI and QR code rendering
- ✅ **Signal handling** for graceful shutdown (Ctrl+C support)
- ✅ **Cross-platform support** (Windows/Linux/macOS)
- ✅ **Logging control interface** with dynamic level adjustment
- ✅ **Log suppression capabilities** for production environments
- ✅ **Multiple convenience methods** for event handling patterns

## Quick Start

### 1. Build the Go Shared Library

```bash
cd go-lib

# Build shared library (cross-platform)
go build -buildmode=c-shared -o whatsapp_ffi.dll .     # Windows
go build -buildmode=c-shared -o libwhatsapp_ffi.so .  # Linux
go build -buildmode=c-shared -o libwhatsapp_ffi.dylib . # macOS
```

This generates:

- `libwhatsapp_ffi.so` (Linux)
- `libwhatsapp_ffi.dylib` (macOS)
- `whatsapp_ffi.dll` (Windows)
- `whatsapp_ffi.h` (C header)

### 2. Use the Rust Client

Add to your `Cargo.toml`:

```toml
[dependencies]
whatsapp-ffi-client = { path = "./rust-client" }
tokio = { version = "1.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = "0.3"
```

Basic usage:

```rust
use whatsapp_ffi_client::{WhatsAppClient, WhatsAppEvent, ConnectionStatus};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Determine library path based on platform
    let library_path = if cfg!(target_os = "windows") {
        "./whatsapp_ffi.dll"
    } else if cfg!(target_os = "macos") {
        "./libwhatsapp_ffi.dylib"
    } else {
        "./libwhatsapp_ffi.so"
    };

    // Create client
    let mut client = WhatsAppClient::new(library_path, "./whatsapp.db")?;

    // Connect
    client.connect().await?;

    // Option 1: Handle events with custom logic
    client.run_event_loop(|event| {
        match event {
            WhatsAppEvent::QRCode(code) => {
                println!("🔗 Scan QR code with WhatsApp:");
                println!("{}", code);
            }
            WhatsAppEvent::MessageReceived(message) => {
                println!("📨 Message from {}: {}", message.from, message.text);
                println!("   Timestamp: {}", message.timestamp);
                println!("   Message ID: {}", message.message_id);
            }
            WhatsAppEvent::ConnectionStatusChanged { status, reason } => {
                match status {
                    ConnectionStatus::Connected => {
                        println!("✅ Connected! Ready to send messages.");
                    }
                    ConnectionStatus::Disconnected => {
                        println!("❌ Disconnected: {:?}", reason);
                    }
                    ConnectionStatus::Connecting => {
                        println!("🔄 Connecting to WhatsApp...");
                    }
                    ConnectionStatus::LoggedOut => {
                        println!("🚪 Logged out: {:?}", reason);
                    }
                }
            }
            WhatsAppEvent::Error { code, message } => {
                eprintln!("❌ Error {}: {}", code, message);
            }
        }
    }).await?;

    // Option 2: Use built-in beautiful event logging (simpler)
    // client.run_with_default_logging().await?;

    Ok(())
}
```

### 3. Send Messages

```rust
// Send a text message (phone number will be formatted as JID automatically)
client.send_message("1234567890", "Hello from Rust!").await?;

// Or use direct JID format
client.send_message("<EMAIL>", "Direct JID message").await?;

// Send message once connected (example from event handler)
WhatsAppEvent::ConnectionStatusChanged { status, .. } => {
    if status == ConnectionStatus::Connected {
        // Spawn async task to send welcome message
        tokio::spawn(async move {
            if let Err(e) = client.send_message("1234567890", "Bot is now online!").await {
                eprintln!("Failed to send message: {}", e);
            }
        });
    }
}
```

## Event System

The library provides async event notifications through Rust channels:

```rust
// Get event stream for manual handling
let mut events = client.event_stream().unwrap();

while let Some(event) = events.recv().await {
    match event {
        WhatsAppEvent::QRCode(code) => {
            // Display QR code for user to scan
            display_qr_code(&code);
        }
        WhatsAppEvent::MessageReceived(message) => {
            // Process incoming message
            process_message(message).await;
        }
        WhatsAppEvent::ConnectionStatusChanged { status, reason } => {
            // Handle connection state changes
            handle_connection_change(status, reason).await;
        }
        WhatsAppEvent::Error { code, message } => {
            // Handle errors
            handle_error(code, message).await;
        }
    }
}
```

## Error Handling

Comprehensive error handling with typed errors:

```rust
use whatsapp_ffi_client::WhatsAppError;

match client.send_message("invalid", "test").await {
    Ok(()) => println!("Message sent!"),
    Err(WhatsAppError::InvalidJid(jid)) => {
        eprintln!("Invalid phone number: {}", jid);
    }
    Err(WhatsAppError::NotConnected) => {
        eprintln!("Not connected to WhatsApp");
    }
    Err(e) => eprintln!("Other error: {}", e),
}
```

## SOLID Principles Implementation

### Single Responsibility Principle (SRP)

- **Go**: Separate modules for FFI exports, WhatsApp logic, and event handling
- **Rust**: Distinct layers for FFI bindings, business logic, and user interface

### Open/Closed Principle (OCP)

- **Extensible event system**: Easy to add new event types
- **Pluggable adapters**: Can swap FFI implementation without changing core logic
- **Trait-based design**: New WhatsApp features can be added via trait extensions

### Interface Segregation Principle (ISP)

- **Focused traits**: `WhatsAppClientPort` only exposes necessary operations
- **Minimal FFI interface**: C exports only essential functions
- **Event-specific handlers**: Separate handling for different event types

### Dependency Inversion Principle (DIP)

- **Rust layers depend on abstractions**: Core logic uses traits, not concrete FFI
- **Dependency injection**: FFI bindings injected into adapters
- **Testable design**: Easy to mock FFI layer for testing

## Examples

The project includes several comprehensive examples:

- **`basic_usage.rs`** - Beautiful terminal UI with QR code rendering and colorful output
- **`advanced_usage.rs`** - Manual event handling with interactive command input
- **`simple_with_ctrl_c.rs`** - Graceful shutdown handling with signal processing
- **`example_integration.rs`** - Integration testing and advanced patterns
- **`logging_demo.rs`** - Comprehensive logging interface demonstration with all log levels

Run any example:

```bash
cd rust-client
cargo run --example basic_usage
cargo run --example logging_demo  # NEW: Test logging features
```

## Logging Control

The library provides comprehensive logging control at both global and per-client levels:

### Log Levels

- **`Off`** - No logging output
- **`Error`** - Only error messages
- **`Warn`** - Warnings and errors
- **`Info`** - Informational messages, warnings, and errors (default)
- **`Debug`** - All messages including detailed debug information

### Basic Logging Control

```rust
use whatsapp_ffi_client::{WhatsAppClient, core::entities::LogLevel};

let client = WhatsAppClient::new("./whatsapp_ffi.dll", "./whatsapp.db")?;

// Set log level for this specific client
client.set_log_level(LogLevel::Debug)?;

// Suppress all Go-side logging for production
client.suppress_go_logs()?;

// Set global log level (affects all clients)
client.set_global_log_level(LogLevel::Error)?;

// Get current log levels
let client_level = client.get_log_level()?;
let global_level = client.get_global_log_level()?;
```

### Builder Pattern with Logging

```rust
use whatsapp_ffi_client::{WhatsAppClientBuilder, core::entities::LogLevel};

// Client with custom log level
let client = WhatsAppClientBuilder::new()
    .library_path("./libwhatsapp_ffi.so")
    .db_path("./my_whatsapp.db")
    .with_log_level(LogLevel::Debug)
    .build()?;

// Client with suppressed Go logging
let quiet_client = WhatsAppClientBuilder::new()
    .library_path("./libwhatsapp_ffi.so")
    .db_path("./production.db")
    .suppress_go_logs()
    .build()?;
```

### Dynamic Log Level Changes

```rust
// Start with default logging
let client = WhatsAppClient::new("./whatsapp_ffi.dll", "./whatsapp.db")?;

// Enable debug logging for troubleshooting
client.set_log_level(LogLevel::Debug)?;
client.connect().await?; // Will show detailed debug output

// Reduce logging for normal operation
client.set_log_level(LogLevel::Warn)?;

// Completely suppress Go logs if needed
client.suppress_go_logs()?;
```

## Advanced Usage

### Builder Pattern

```rust
use whatsapp_ffi_client::WhatsAppClientBuilder;

let client = WhatsAppClientBuilder::new()
    .library_path("./libwhatsapp_ffi.so")
    .db_path("./my_whatsapp.db")
    .build()?;
```

### Event Handling Patterns

The library provides multiple convenient ways to handle events:

#### 1. Custom Event Loop with Ctrl+C Handling

```rust
client.run_event_loop(|event| {
    match event {
        WhatsAppEvent::QRCode(code) => {
            println!("🔗 Scan QR code: {}", code);
        }
        WhatsAppEvent::MessageReceived(message) => {
            println!("📨 Message from {}: {}", message.from, message.text);
        }
        WhatsAppEvent::ConnectionStatusChanged { status, reason } => {
            println!("🔄 Status changed: {:?} - {:?}", status, reason);
        }
        WhatsAppEvent::Error { code, message } => {
            eprintln!("❌ Error {}: {}", code, message);
        }
    }
}).await?;
```

#### 2. Default Logging Event Handler

```rust
// Simple event handling with built-in beautiful output
client.run_with_default_logging().await?;
```

#### 3. Manual Event Stream Processing

```rust
// Spawn background task for event processing
let mut events = client.event_stream().unwrap();
tokio::spawn(async move {
    while let Some(event) = events.recv().await {
        // Custom async event processing
        process_event_async(event).await;
    }
});

// Continue with other operations
client.connect().await?;
```

#### 4. Alternative Handler Method

```rust
// Alias for run_event_loop with different naming
client.run_with_handler(|event| {
    // Handle events
}).await?;
```

## Development

### Prerequisites

- **Go 1.24.2+** with cgo support
- **Rust 1.70+** with Cargo
- **Platform-specific tools**:
  - Linux: `gcc`, `pkg-config`
  - macOS: Xcode command line tools
  - Windows: MSVC or MinGW

### Building

```bash
# Build Go library
cd go-lib
go build -buildmode=c-shared -o whatsapp_ffi.dll .  # Windows
# go build -buildmode=c-shared -o libwhatsapp_ffi.so .  # Linux
# go build -buildmode=c-shared -o libwhatsapp_ffi.dylib .  # macOS

# Build Rust client
cd rust-client && cargo build

# Run examples
cargo run --example basic_usage
cargo run --example advanced_usage
cargo run --example simple_with_ctrl_c
cargo run --example example_integration
cargo run --example logging_demo  # Test logging features
```

### Testing

```bash
# Test Rust client
cd rust-client && cargo test

# Run with logging
RUST_LOG=debug cargo run --example basic_usage
```

## Implementation Status

### ✅ Completed Features

- **FFI Bridge**: Complete C-compatible interface between Go and Rust
- **WhatsApp Integration**: Full Whatsmeow library integration with session management
- **Authentication**: QR code-based login with beautiful terminal rendering
- **Messaging**: Send and receive comprehensive message types (text, image, video, audio, document, sticker) with rich metadata
- **Event System**: Comprehensive async event handling with JSON serialization
- **Error Handling**: Typed errors with detailed error codes and messages
- **Resource Management**: Proper cleanup and memory safety
- **Logging System**: Configurable logging with 5 levels (Off, Error, Warn, Info, Debug)
- **Log Control**: Global and per-client log level management via FFI
- **Examples**: Multiple working examples with different usage patterns including logging demo
- **Documentation**: Complete API documentation and usage guides

### 🚧 Current Limitations

- **Media Download**: Media messages are received with metadata but downloading actual files not yet implemented
- **Media Upload**: Sending media messages (images, videos, documents) not yet implemented
- **Group Management**: Group operations not yet implemented
- **Contact Management**: Contact list operations not available
- **Message History**: No message history retrieval functionality

### 🔮 Future Enhancements & Roadmap

#### 📱 **Core Messaging Features**

- **🖼️ Rich Media Support**

  - Image messages with compression and metadata
  - Document sharing (PDF, Office files, archives)
  - Audio messages and voice notes
  - Video messages with thumbnail generation
  - Sticker support with custom sticker packs
  - Location sharing with interactive maps

- **📊 Message Status & Delivery**
  - Real-time delivery confirmations (sent ✓, delivered ✓✓, read 💙)
  - Message reactions and emoji responses
  - Message editing and deletion with sync
  - Reply-to-message threading
  - Message forwarding with attribution
  - Starred/bookmarked messages

#### 👥 **Group & Contact Management**

- **🏢 Advanced Group Operations**

  - Create groups with custom settings and descriptions
  - Invite management with approval workflows
  - Admin controls (promote/demote, kick/ban members)
  - Group settings (who can send messages, edit info, add members)
  - Group announcements and broadcast lists
  - Disappearing messages configuration

- **📇 Contact & Profile Management**
  - Contact list synchronization and management
  - Profile picture updates and retrieval
  - Status message management
  - Block/unblock contacts with privacy controls
  - Contact verification and trust indicators
  - Business profile integration

#### 🚀 **Enterprise & Integration Features**

- **🔗 Webhook & API Gateway**

  - RESTful webhook endpoints for message events
  - GraphQL API for advanced querying
  - Real-time WebSocket connections
  - Rate limiting and quota management
  - Multi-tenant support with isolated sessions
  - Custom authentication and authorization

- **📈 Analytics & Monitoring**
  - Message delivery analytics and reporting
  - Connection health monitoring and alerts
  - Performance metrics and optimization insights
  - Usage statistics and billing integration
  - Custom dashboards with real-time data
  - Audit logs for compliance and security

#### 🛡️ **Security & Privacy Enhancements**

- **🔐 Advanced Security**

  - End-to-end encryption verification
  - Session management with device fingerprinting
  - Two-factor authentication integration
  - Secure key storage and rotation
  - Privacy mode with message auto-deletion
  - Compliance tools (GDPR, CCPA, HIPAA)

- **🔒 Access Control**
  - Role-based permissions system
  - API key management with scoped access
  - IP whitelisting and geographic restrictions
  - Session timeout and automatic logout
  - Audit trails for all operations
  - Data retention policies

#### 🌐 **Platform & Language Bindings**

- **🔧 Additional Language Support**

  - Python bindings with asyncio integration
  - Node.js/JavaScript FFI wrapper
  - C# .NET bindings for Windows applications
  - Java JNI bindings for Android/server applications
  - Swift bindings for iOS and macOS
  - WebAssembly (WASM) for browser integration

- **☁️ Cloud & Deployment**
  - Docker containers with multi-stage builds
  - Kubernetes operators and Helm charts
  - AWS Lambda serverless functions
  - Google Cloud Functions integration
  - Azure Functions support
  - Terraform modules for infrastructure

#### 🎨 **Developer Experience**

- **🛠️ Advanced Tooling**

  - Interactive CLI tool with rich TUI
  - VS Code extension with syntax highlighting
  - Postman/Insomnia collections for API testing
  - OpenAPI/Swagger specifications
  - SDK generators for multiple languages
  - Live documentation with interactive examples

- **🧪 Testing & Quality**
  - Comprehensive integration test suite
  - Load testing tools and benchmarks
  - Mock WhatsApp server for development
  - Chaos engineering tools for resilience testing
  - Property-based testing with QuickCheck
  - Continuous integration with multiple platforms

#### 🌟 **Nice-to-Have Features**

- **🤖 AI & Automation**

  - Chatbot framework with natural language processing
  - Auto-reply systems with smart responses
  - Message translation with multiple language support
  - Sentiment analysis and content moderation
  - Smart message scheduling and reminders
  - Voice-to-text and text-to-voice conversion

- **📱 Mobile & Desktop Apps**

  - Cross-platform mobile app (Flutter/React Native)
  - Desktop applications (Tauri/Electron)
  - Browser extension for web integration
  - System tray integration with notifications
  - Offline message queuing and sync
  - Dark/light theme with customization

- **🔄 Advanced Integrations**
  - CRM system integrations (Salesforce, HubSpot)
  - E-commerce platform connectors (Shopify, WooCommerce)
  - Social media cross-posting (Twitter, LinkedIn)
  - Email gateway for WhatsApp-to-email bridging
  - Calendar integration for appointment scheduling
  - Payment processing for business transactions

#### 🎯 **Performance & Scalability**

- **⚡ Optimization**

  - Connection pooling and multiplexing
  - Message batching and bulk operations
  - Caching layers with Redis integration
  - Database sharding and read replicas
  - CDN integration for media files
  - Edge computing with global distribution

- **📊 Monitoring & Observability**
  - Distributed tracing with OpenTelemetry
  - Metrics collection with Prometheus
  - Log aggregation with ELK stack
  - Health checks and service discovery
  - Auto-scaling based on load patterns
  - Disaster recovery and backup strategies

#### 🌈 **Advanced User Experience**

- **🎨 Rich UI Components**

  - Interactive web dashboard with real-time updates
  - Drag-and-drop message composer with rich formatting
  - Live typing indicators and presence status
  - Message search with advanced filters and indexing
  - Custom themes and branding options
  - Accessibility compliance (WCAG 2.1 AA)

- **🔔 Smart Notifications**
  - Intelligent notification routing and prioritization
  - Custom notification sounds and vibration patterns
  - Do-not-disturb modes with smart scheduling
  - Notification grouping and conversation threading
  - Cross-device notification synchronization
  - Emergency override for critical messages

#### 🧠 **Machine Learning & Intelligence**

- **🤖 Advanced AI Features**

  - Smart message categorization and tagging
  - Automated spam and phishing detection
  - Predictive text and smart compose suggestions
  - Language detection and auto-translation
  - Emotion recognition and sentiment tracking
  - Conversation summarization and insights

- **📊 Behavioral Analytics**
  - User engagement pattern analysis
  - Message frequency and timing optimization
  - Conversation flow analysis and optimization
  - A/B testing framework for message templates
  - Churn prediction and retention strategies
  - Personalized message recommendations

#### 🔧 **Advanced Configuration & Customization**

- **⚙️ Dynamic Configuration**

  - Hot-reloadable configuration without restarts
  - Environment-specific configuration management
  - Feature flags and gradual rollout capabilities
  - Custom plugin architecture with sandboxing
  - Rule-based message routing and processing
  - Dynamic rate limiting and throttling

- **🎛️ Administrative Controls**
  - Multi-level administrative hierarchy
  - Granular permission management system
  - Resource usage monitoring and quotas
  - Automated policy enforcement
  - Compliance reporting and audit trails
  - Emergency shutdown and maintenance modes

#### 🌍 **Internationalization & Localization**

- **🗺️ Global Support**

  - Multi-language interface with RTL support
  - Regional phone number formatting and validation
  - Timezone-aware message scheduling and delivery
  - Currency and payment method localization
  - Cultural adaptation for different markets
  - Local compliance and regulatory adherence

- **🔤 Advanced Text Processing**
  - Unicode normalization and emoji handling
  - Text-to-speech with multiple voice options
  - Optical character recognition (OCR) for images
  - Handwriting recognition and conversion
  - Advanced text formatting and markdown support
  - Custom font and typography options

#### 🔐 **Enterprise Security & Compliance**

- **🛡️ Advanced Threat Protection**

  - Real-time malware scanning for media files
  - Advanced persistent threat (APT) detection
  - Behavioral anomaly detection and alerting
  - Zero-trust security model implementation
  - Secure multi-party computation for privacy
  - Homomorphic encryption for data processing

- **📋 Regulatory Compliance**
  - GDPR, CCPA, PIPEDA compliance frameworks
  - HIPAA compliance for healthcare applications
  - SOX compliance for financial institutions
  - ISO 27001 security management standards
  - PCI DSS compliance for payment processing
  - Industry-specific compliance modules

#### 🚀 **Next-Generation Technologies**

- **🔮 Emerging Tech Integration**

  - Blockchain integration for message verification
  - NFT support for digital collectibles and media
  - Metaverse integration with VR/AR experiences
  - IoT device integration and control
  - Edge AI processing for real-time insights
  - Quantum-resistant cryptography preparation

- **🌐 Web3 & Decentralization**
  - Decentralized identity (DID) integration
  - IPFS integration for distributed media storage
  - Smart contract integration for automated workflows
  - Cryptocurrency payment integration
  - Decentralized autonomous organization (DAO) features
  - Cross-chain interoperability protocols

#### 📱 **Mobile & Wearable Integration**

- **⌚ Wearable Device Support**

  - Apple Watch and WearOS integration
  - Voice-only interaction for hands-free operation
  - Gesture-based controls and navigation
  - Health data integration for wellness bots
  - Location-based automated responses
  - Biometric authentication integration

- **📲 Advanced Mobile Features**
  - Augmented reality (AR) message overlays
  - Camera integration with real-time filters
  - NFC-based contact sharing and pairing
  - Offline-first architecture with sync
  - Battery optimization and power management
  - Adaptive UI based on device capabilities

#### 🎯 **Business Intelligence & Analytics**

- **📈 Advanced Reporting**

  - Real-time business intelligence dashboards
  - Custom report builder with drag-and-drop interface
  - Automated report generation and distribution
  - Predictive analytics and forecasting
  - Customer journey mapping and analysis
  - ROI tracking and attribution modeling

- **🔍 Data Mining & Insights**
  - Advanced text analytics and topic modeling
  - Social network analysis and influence mapping
  - Conversation pattern recognition
  - Customer satisfaction scoring and tracking
  - Competitive analysis and benchmarking
  - Market trend analysis and predictions

#### 🌟 **Innovation Lab Features**

- **🧪 Experimental Capabilities**

  - Voice cloning and synthesis for personalization
  - Real-time language translation with context awareness
  - Holographic message projection (future AR/VR)
  - Brain-computer interface integration (research)
  - Quantum communication protocols (experimental)
  - AI-generated content and media creation

- **🔬 Research & Development**
  - Academic collaboration and research partnerships
  - Open-source contribution and community building
  - Patent portfolio development and protection
  - Technology transfer and licensing opportunities
  - Innovation challenges and hackathon support
  - Future technology scouting and evaluation

## Platform Support

| Platform  | Library File            | Status          | Notes                    |
| --------- | ----------------------- | --------------- | ------------------------ |
| Linux x64 | `libwhatsapp_ffi.so`    | ✅ Fully Tested | Requires gcc, pkg-config |
| macOS     | `libwhatsapp_ffi.dylib` | ✅ Supported    | Requires Xcode CLI tools |
| Windows   | `whatsapp_ffi.dll`      | ✅ Fully Tested | Requires MSVC or MinGW   |

## FFI Interface Details

### C API Functions

The Go shared library exports these C-compatible functions:

```c
// Client lifecycle
uintptr_t whatsapp_create_client(const char* db_path, EventCallback callback, void* user_data);
int whatsapp_connect(uintptr_t handle);
int whatsapp_disconnect(uintptr_t handle);
int whatsapp_destroy_client(uintptr_t handle);

// Messaging
int whatsapp_send_message(uintptr_t handle, const char* jid, const char* text);
int whatsapp_is_connected(uintptr_t handle);

// Error handling
const char* whatsapp_get_error_message(int error_code);
void whatsapp_free_string(char* str);

// Logging control (NEW)
int whatsapp_set_log_level(int level);
int whatsapp_get_log_level();
int whatsapp_set_client_log_level(uintptr_t handle, int level);
int whatsapp_get_client_log_level(uintptr_t handle);
```

### Event System

Events are delivered via callback with JSON-serialized data:

```c
typedef void (*EventCallback)(int event_type, const char* json_data, void* user_data);
```

Event types:

- `EVENT_QR_CODE` (1) - QR code for authentication
- `EVENT_MESSAGE_RECEIVED` (2) - Incoming text message
- `EVENT_CONNECTION_STATUS` (3) - Connection state changes
- `EVENT_ERROR` (4) - Error notifications

### Error Codes

- `ERROR_NONE` (0) - Success
- `ERROR_INVALID_HANDLE` (1) - Invalid client handle
- `ERROR_NOT_CONNECTED` (2) - Client not connected
- `ERROR_INVALID_JID` (3) - Invalid phone number/JID format
- `ERROR_SEND_FAILED` (4) - Message sending failed
- `ERROR_INTERNAL` (5) - Internal library error

### Log Levels

- `LOG_LEVEL_OFF` (0) - No logging output
- `LOG_LEVEL_ERROR` (1) - Only error messages
- `LOG_LEVEL_WARN` (2) - Warnings and errors
- `LOG_LEVEL_INFO` (3) - Informational messages (default)
- `LOG_LEVEL_DEBUG` (4) - All messages including debug information

## Security Considerations

- **Database encryption**: WhatsApp session data is stored in SQLite with proper isolation
- **Memory safety**: Rust wrapper provides memory-safe interface over unsafe FFI
- **Resource cleanup**: Automatic cleanup prevents memory leaks and handle exhaustion
- **Error isolation**: FFI errors don't crash the Rust application
- **Thread safety**: Go library uses mutexes to protect shared state
- **Callback safety**: Event callbacks include panic recovery to prevent crashes

## Troubleshooting

### Common Issues

**Library not found errors:**

```bash
# Ensure the shared library is built and in the correct location
cd go-lib
go build -buildmode=c-shared -o whatsapp_ffi.dll .  # Windows

# Check library path in your Rust code matches the actual file location
```

**Connection failures:**

```bash
# Enable debug logging to see detailed connection info
RUST_LOG=debug cargo run --example basic_usage

# Check database permissions and path
ls -la ./whatsapp.db  # Should be writable by your user
```

**QR code not displaying:**

```bash
# Install required dependencies for QR code rendering
# The basic_usage example includes beautiful QR code display
cargo run --example basic_usage
```

**Build errors on Windows:**

```bash
# Ensure you have MSVC or MinGW installed
# For MSVC: Install Visual Studio Build Tools
# For MinGW: Install via MSYS2 or similar
```

**cgo compilation issues:**

```bash
# Ensure Go has cgo support enabled
go env CGO_ENABLED  # Should output "1"

# Install required C compiler for your platform
# Linux: sudo apt install build-essential
# macOS: xcode-select --install
# Windows: Install Visual Studio Build Tools
```

### Debug Mode

Enable detailed logging:

```bash
# Rust side logging
RUST_LOG=debug cargo run --example basic_usage

# Go side logging via FFI interface
cargo run --example logging_demo  # Demonstrates all log levels

# Test different log levels programmatically
let client = WhatsAppClient::new("./whatsapp_ffi.dll", "./debug.db")?;
client.set_log_level(LogLevel::Debug)?;  // Enable verbose Go logging
client.connect().await?;  // Will show detailed debug output
```

**Logging-related troubleshooting:**

```bash
# Too much logging output?
client.set_log_level(LogLevel::Error)?;  # Only show errors
# or
client.suppress_go_logs()?;  # Turn off all Go logging

# Not enough logging information?
client.set_log_level(LogLevel::Debug)?;  # Show everything

# Different log levels per client
let debug_client = WhatsAppClientBuilder::new()
    .library_path("./whatsapp_ffi.dll")
    .db_path("./debug.db")
    .with_log_level(LogLevel::Debug)
    .build()?;

let production_client = WhatsAppClientBuilder::new()
    .library_path("./whatsapp_ffi.dll")
    .db_path("./production.db")
    .suppress_go_logs()
    .build()?;
```

### Performance Tips

- Use `cargo build --release` for production builds
- The Go library handles multiple clients efficiently via handle registry
- Event callbacks are non-blocking and use JSON serialization
- Database operations are optimized with SQLite WAL mode

## Contributing

1. Follow the established Clean Architecture patterns
2. Add comprehensive tests for new functionality
3. Update documentation for API changes
4. Ensure cross-platform compatibility (Windows/Linux/macOS)
5. Use proper error handling with typed errors
6. Include examples for new features
7. Follow Rust and Go best practices

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Whatsmeow](https://github.com/tulir/whatsmeow) - Excellent Go WhatsApp library
- [libloading](https://github.com/nagisa/rust_libloading) - Safe Rust FFI loading
- [Tokio](https://tokio.rs/) - Async runtime for Rust
