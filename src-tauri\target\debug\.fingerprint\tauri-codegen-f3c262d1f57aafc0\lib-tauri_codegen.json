{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 576489812661279835, "deps": [[654232091421095663, "tauri_utils", false, 15712907068407191118], [2995469292676432503, "uuid", false, 6998241069174797990], [3060637413840920116, "proc_macro2", false, 12441885387897080324], [3150220818285335163, "url", false, 14195290360083940607], [4899080583175475170, "semver", false, 5742074698723764310], [7170110829644101142, "json_patch", false, 7408263245480546190], [7392050791754369441, "ico", false, 12700539494278120901], [9556762810601084293, "brotli", false, 16181042358721137845], [9689903380558560274, "serde", false, 16503059047051001516], [9857275760291862238, "sha2", false, 15451757385390307161], [10806645703491011684, "thiserror", false, 44651847147331727], [12687914511023397207, "png", false, 12255934945692980923], [13077212702700853852, "base64", false, 8431100555923844722], [15622660310229662834, "walkdir", false, 14109848318917712597], [16362055519698394275, "serde_json", false, 14102427500185749966], [17990358020177143287, "quote", false, 9492086704218893313], [18149961000318489080, "syn", false, 6062637982592736054]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-f3c262d1f57aafc0\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}