use colored::*;
use qrcode::render::unicode;
use qrcode::QrCode;
use std::sync::Arc;
use tokio::signal;
use tokio::sync::{mpsc, Mutex};
use tokio::time::{timeout, Duration};
use whatsapp_ffi_client::{ConnectionStatus, WhatsAppClientBuilder, WhatsAppEvent};

/// Message sending command for channel communication
#[derive(Debu<PERSON>, <PERSON>lone)]
struct SendMessageCommand {
    phone_number: String,
    message: String,
}

/// Renders a stunning QR code in the terminal with beautiful styling
pub fn render_qr_code(qr_data: &str) {
    match QrCode::new(qr_data.as_bytes()) {
        Ok(code) => {
            // Render the QR code with high-density Unicode characters for better clarity
            let image = code
                .render::<unicode::Dense1x2>()
                .dark_color(unicode::Dense1x2::Light)
                .light_color(unicode::Dense1x2::Dark)
                .build();

            let lines: Vec<&str> = image.lines().collect();
            let width = lines[0].chars().count();

            // Create a beautiful header with gradient-like effect
            println!();
            println!(
                "    {}",
                "✨ WhatsApp Authentication QR Code ✨".bright_cyan().bold()
            );
            println!();

            // Create decorative top border with double lines
            print!("    {}", "╔".bright_magenta().bold());
            for i in 0..width + 4 {
                if i % 4 == 0 {
                    print!("{}", "═".bright_cyan().bold());
                } else {
                    print!("{}", "═".bright_blue());
                }
            }
            println!("{}", "╗".bright_magenta().bold());

            // Add padding line with side decorations
            println!(
                "    {}{}{}",
                "║".bright_magenta().bold(),
                " ".repeat(width + 4),
                "║".bright_magenta().bold()
            );

            // Print QR code with beautiful side borders and padding
            for (i, line) in lines.iter().enumerate() {
                let left_border = if i % 3 == 0 {
                    "║".bright_magenta().bold()
                } else {
                    "║".bright_blue()
                };
                let right_border = if i % 3 == 0 {
                    "║".bright_magenta().bold()
                } else {
                    "║".bright_blue()
                };

                println!("    {}  {}  {}", left_border, line, right_border);
            }

            // Add padding line
            println!(
                "    {}{}{}",
                "║".bright_magenta().bold(),
                " ".repeat(width + 4),
                "║".bright_magenta().bold()
            );

            // Create decorative bottom border
            print!("    {}", "╚".bright_magenta().bold());
            for i in 0..width + 4 {
                if i % 4 == 0 {
                    print!("{}", "═".bright_cyan().bold());
                } else {
                    print!("{}", "═".bright_blue());
                }
            }
            println!("{}", "╝".bright_magenta().bold());

            // Add beautiful footer with instructions
            println!();
            println!(
                "    {}",
                "📱 Open WhatsApp → Settings → Linked Devices → Link a Device".bright_yellow()
            );
            println!(
                "    {}",
                "⚡ Scan the QR code above to connect instantly!"
                    .bright_green()
                    .bold()
            );
            println!();
        }
        Err(e) => {
            println!(
                "    {}",
                "❌ Failed to generate QR code".bright_red().bold()
            );
            println!("    {}", format!("Error: {}", e).bright_red());
            println!("    {}", "📝 Raw QR data:".bright_yellow());
            println!("    {}", qr_data.bright_white());
        }
    }
}

/// Displays a beautifully formatted QR code with enhanced styling
fn display_qr_code(qr_data: &str) {
    // Create a stunning header with animated-like border
    println!();
    println!("{}", "╔".repeat(80).bright_magenta().bold());
    println!(
        "{}",
        "    � WhatsApp FFI Authentication Portal 🚀"
            .bright_cyan()
            .bold()
    );
    println!("{}", "╚".repeat(80).bright_magenta().bold());
    println!();

    // Display step-by-step instructions with beautiful formatting
    println!("{}", "📋 Quick Setup Guide:".bright_yellow().bold());
    println!(
        "   {} Open WhatsApp on your mobile device",
        "1️⃣".bright_green()
    );
    println!(
        "   {} Navigate to Settings → Linked Devices",
        "2️⃣".bright_green()
    );
    println!("   {} Tap 'Link a Device' button", "3️⃣".bright_green());
    println!(
        "   {} Scan the beautiful QR code below",
        "4️⃣".bright_green()
    );
    println!();

    // Render the beautiful QR code
    render_qr_code(qr_data);

    // Add a beautiful waiting message with animation-like effect
    println!(
        "{}",
        "⏳ Waiting for authentication...".bright_yellow().bold()
    );
    println!(
        "{}",
        "✨ Your connection will be established automatically once scanned!".bright_green()
    );
    println!();
    println!("{}", "═".repeat(80).bright_blue());
    println!();
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Starting Beautiful WhatsApp FFI Client");
    println!("📝 Press Ctrl+C at any time for graceful shutdown");
    println!("✨ Built-in signal handling will disconnect cleanly");
    println!();

    // Determine library path based on platform
    let library_path = if cfg!(target_os = "windows") {
        "../go-lib/whatsapp_ffi.dll"
    } else if cfg!(target_os = "macos") {
        "../go-lib/libwhatsapp_ffi.dylib"
    } else {
        "../go-lib/libwhatsapp_ffi.so"
    };

    let mut client = WhatsAppClientBuilder::new()
        .library_path(library_path)
        .db_path("./whatsapp.db")
        .suppress_go_logs()
        .build()?;

    // Connect to WhatsApp
    client.connect().await?;

    // Get event stream for manual handling BEFORE wrapping in Arc
    let mut event_stream = client.event_stream().ok_or("Failed to get event stream")?;

    // Create channel for message sending commands
    let (message_tx, mut message_rx) = mpsc::unbounded_channel::<SendMessageCommand>();

    // Wrap client in Arc<Mutex> for sharing between tasks
    let client_arc = Arc::new(Mutex::new(client));
    let client_for_sender = Arc::clone(&client_arc);

    // Spawn dedicated message sending task
    tokio::spawn(async move {
        while let Some(cmd) = message_rx.recv().await {
            println!(
                "{}",
                "📤 Processing message send command...".bright_yellow()
            );

            let client = client_for_sender.lock().await;
            match timeout(
                Duration::from_secs(10),
                client.send_message(&cmd.phone_number, &cmd.message),
            )
            .await
            {
                Ok(Ok(())) => {
                    println!("{}", "✅ Message sent successfully!".bright_green());
                }
                Ok(Err(e)) => {
                    eprintln!(
                        "{}",
                        format!("❌ Failed to send message: {}", e).bright_red()
                    );
                }
                Err(_) => {
                    eprintln!(
                        "{}",
                        "⏰ Message sending timed out after 10 seconds".bright_red()
                    );
                }
            }
        }
    });

    // Set up Ctrl+C handler
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("Failed to install Ctrl+C handler");
    };

    println!(
        "{}",
        "🎯 Event loop started. Press Ctrl+C to gracefully shutdown".bright_blue()
    );

    // Handle events with Ctrl+C support using tokio::select!
    tokio::select! {
        // Handle Ctrl+C signal
        _ = ctrl_c => {
            println!();
            println!("{}", "🛑 Graceful Shutdown Initiated".bright_yellow().bold());
            println!("{}", "═".repeat(50).bright_blue());
            println!("{}", "📱 Disconnecting from WhatsApp...".bright_cyan());

            // Attempt graceful disconnect with timeout
            let client = client_arc.lock().await;
            let disconnect_result = tokio::time::timeout(
                std::time::Duration::from_secs(5),
                client.disconnect()
            ).await;

            match disconnect_result {
                Ok(Ok(_)) => {
                    println!("{}", "✅ Successfully disconnected from WhatsApp".bright_green());
                }
                Ok(Err(e)) => {
                    println!("{}", format!("⚠️  Disconnect warning: {}", e).bright_yellow());
                }
                Err(_) => {
                    println!("{}", "⚠️  Disconnect timeout, forcing cleanup".bright_yellow());
                }
            }

            println!("{}", "🔄 Cleaning up resources...".bright_cyan());
            tokio::time::sleep(std::time::Duration::from_millis(100)).await;
            println!("{}", "✨ Shutdown complete. Thank you for using WhatsApp FFI!".bright_green().bold());
            println!("{}", "═".repeat(50).bright_blue());
            println!();

            return Ok(());
        }

        // Handle WhatsApp events
        _result = async {
            while let Some(event) = event_stream.recv().await {
                match event {
                    WhatsAppEvent::QRCode(code) => {
                        display_qr_code(&code);
                    }
                    WhatsAppEvent::MessageReceived(_message) => {
                        // Handle message received event here
                    }
                    WhatsAppEvent::ConnectionStatusChanged { status, reason } => {
                        match status {
                            ConnectionStatus::Connected => {
                        // Spectacular connection success display
                        println!();
                        println!("{}", "🌟".repeat(25).bright_yellow().bold());
                        println!(
                            "{}",
                            "    🎉 CONNECTION ESTABLISHED! 🎉".bright_green().bold()
                        );
                        println!("{}", "🌟".repeat(25).bright_yellow().bold());
                        println!();
                        println!(
                            "{}",
                            "✨ Your WhatsApp device is now successfully linked!"
                                .bright_cyan()
                                .bold()
                        );
                        println!(
                            "{}",
                            "� Ready to send and receive messages instantly!".bright_green()
                        );
                        println!(
                            "{}",
                            "💫 Enjoy seamless messaging through the FFI bridge!".bright_magenta()
                        );
                        println!();
                        println!("{}", "═".repeat(80).bright_blue().bold());
                        println!();

                        // Example: Send a message once connected using channel
                        // Wait a bit for connection to stabilize
                        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

                        println!(
                            "{}",
                            "📤 Sending message command via channel...".bright_yellow()
                        );

                        // Send message command via channel (non-blocking)
                        let cmd = SendMessageCommand {
                            phone_number: "201021347532".to_string(),
                            message: "Hello from Rust!".to_string(),
                        };

                        if let Err(e) = message_tx.send(cmd) {
                            eprintln!(
                                "{}",
                                format!("❌ Failed to send message command: {}", e).bright_red()
                            );
                        } else {
                            println!(
                                "{}",
                                "✅ Message command sent to processing task!".bright_green()
                            );
                        }
                    }
                            ConnectionStatus::Disconnected => {
                        println!();
                        println!("{}", "⚠️  CONNECTION LOST".bright_red().bold());
                        println!("{}", "─".repeat(50).bright_red());
                        if let Some(reason) = reason {
                            println!("{} {}", "📝 Reason:".bright_yellow(), reason.bright_white());
                        }
                        println!(
                            "{}",
                            "🔄 Attempting automatic reconnection...".bright_cyan()
                        );
                        println!(
                            "{}",
                            "⏳ Please wait while we restore your connection".bright_blue()
                        );
                        println!();
                    }
                            ConnectionStatus::Connecting => {
                        println!(
                            "{}",
                            "🔄 Establishing connection to WhatsApp servers..."
                                .bright_blue()
                                .bold()
                        );
                        println!(
                            "{}",
                            "⏳ Initializing secure communication channel...".bright_cyan()
                        );
                        println!("{}", "✨ Almost ready!".bright_green());
                    }
                            ConnectionStatus::LoggedOut => {
                        println!();
                        println!("{}", "🚪 SESSION ENDED".bright_yellow().bold());
                        println!("{}", "─".repeat(40).bright_yellow());
                        if let Some(reason) = reason {
                            println!(
                                "{} {}",
                                "📝 Logout reason:".bright_cyan(),
                                reason.bright_white()
                            );
                        }
                        println!(
                            "{}",
                            "📱 Please scan a new QR code to reconnect".bright_green()
                        );
                        println!("{}", "🔄 Waiting for new authentication...".bright_blue());
                        println!();
                    }
                        }
                    }
                    WhatsAppEvent::Error { code, message } => {
                println!();
                println!("{}", "🚨 ERROR DETECTED".bright_red().bold());
                println!("{}", "═".repeat(50).bright_red());
                println!(
                    "{} {}",
                    "🔢 Error Code:".bright_yellow().bold(),
                    code.to_string().bright_white()
                );
                println!(
                    "{} {}",
                    "💬 Message:".bright_yellow().bold(),
                    message.bright_red()
                );
                println!("{}", "═".repeat(50).bright_red());
                        println!();
                    }
                }
            }
            Ok::<(), Box<dyn std::error::Error>>(())
        } => {
            // Event loop ended
        }
    }

    Ok(())
}
