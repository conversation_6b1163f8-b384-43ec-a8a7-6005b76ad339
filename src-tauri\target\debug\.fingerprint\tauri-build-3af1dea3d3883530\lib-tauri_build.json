{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 10652724864738995638, "deps": [[654232091421095663, "tauri_utils", false, 15712907068407191118], [4899080583175475170, "semver", false, 5742074698723764310], [6913375703034175521, "schemars", false, 2371191611272397952], [7170110829644101142, "json_patch", false, 7408263245480546190], [8786711029710048183, "toml", false, 10048794690708226692], [9689903380558560274, "serde", false, 16503059047051001516], [12714016054753183456, "tauri_winres", false, 992761749311515204], [13077543566650298139, "heck", false, 1670613786372742531], [13475171727366188400, "cargo_toml", false, 10247127045834834018], [13625485746686963219, "anyhow", false, 8308486080552771400], [15622660310229662834, "walkdir", false, 14109848318917712597], [16362055519698394275, "serde_json", false, 14102427500185749966], [16928111194414003569, "dirs", false, 2783601655958361085], [17155886227862585100, "glob", false, 16192797392537921819]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-3af1dea3d3883530\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}