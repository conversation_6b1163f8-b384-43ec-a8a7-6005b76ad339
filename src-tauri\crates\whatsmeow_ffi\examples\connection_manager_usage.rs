use std::sync::Arc;
use tokio::sync::mpsc;
use whatsapp_ffi_client::core::{
    connection_manager::{
        ConnectionManager, DeviceInfo, HealthCheckConfig, InMemorySessionStore, ReconnectConfig,
        SessionData,
    },
    entities::WhatsAppEvent,
    errors::Result,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing for logging
    tracing_subscriber::fmt::init();

    println!("Connection Manager Usage Example");
    println!("================================");

    // Configure reconnection behavior
    let reconnect_config = ReconnectConfig {
        max_attempts: 5,
        initial_delay_ms: 1000,
        max_delay_ms: 30_000,
        backoff_multiplier: 2.0,
        jitter_factor: 0.1,
        connection_timeout_ms: 10_000,
    };

    // Configure health monitoring
    let health_config = HealthCheckConfig {
        enabled: true,
        check_interval_ms: 30_000,
        check_timeout_ms: 5_000,
        failure_threshold: 3,
    };

    // Create session store
    let session_store = Arc::new(InMemorySessionStore::default());

    // Create connection manager
    let mut manager = ConnectionManager::new(reconnect_config, health_config, session_store);

    // Set up event handling
    let (event_tx, mut event_rx) = mpsc::unbounded_channel();
    manager.set_event_sender(event_tx);

    // Spawn event handler
    let event_handler = tokio::spawn(async move {
        while let Some(event) = event_rx.recv().await {
            match event {
                WhatsAppEvent::ConnectionStatusChanged { status, reason } => {
                    println!("Connection status changed: {:?}", status);
                    if let Some(reason) = reason {
                        println!("Reason: {}", reason);
                    }
                }
                WhatsAppEvent::Error { code, message } => {
                    println!("Error occurred: {} - {}", code, message);
                }
                _ => {
                    println!("Other event: {:?}", event);
                }
            }
        }
    });

    // Example session data
    let session = SessionData {
        session_id: "example_session".to_string(),
        user_jid: Some("<EMAIL>".to_string()),
        created_at: chrono::Utc::now(),
        last_used: chrono::Utc::now(),
        device_info: Some(DeviceInfo {
            device_id: "example_device".to_string(),
            platform: "desktop".to_string(),
            app_version: "1.0.0".to_string(),
        }),
        auth_token: Some("example_token".to_string()),
    };

    // Save session
    println!("Saving session...");
    manager.save_session(session).await?;

    // Mock connection function that succeeds after 2 attempts
    let attempt_counter = Arc::new(std::sync::atomic::AtomicU32::new(0));
    let connect_fn = {
        let counter = attempt_counter.clone();
        move || {
            let counter = counter.clone();
            async move {
                let attempt = counter.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
                println!("Connection attempt #{}", attempt + 1);

                if attempt < 1 {
                    // Fail first attempt
                    Err(whatsapp_ffi_client::core::errors::WhatsAppError::NotConnected)
                } else {
                    // Succeed on second attempt
                    println!("Connection successful!");
                    Ok(())
                }
            }
        }
    };

    // Attempt connection with auto-retry
    println!("Starting connection with auto-retry...");
    match manager.connect(connect_fn).await {
        Ok(()) => {
            println!("Successfully connected!");

            // Show connection status
            let status = manager.get_status();
            println!("Current status: {:?}", status);

            // Show health status
            let health = manager.get_health_status();
            println!(
                "Health status: healthy={}, uptime={:?}",
                health.is_healthy, health.uptime
            );

            // Wait a bit to see health monitoring in action
            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

            // Load session
            println!("Loading session...");
            if let Some(loaded_session) = manager.load_session("example_session").await? {
                println!("Loaded session: {}", loaded_session.session_id);
                println!("User JID: {:?}", loaded_session.user_jid);
            }

            // List all sessions
            let sessions = manager.list_sessions().await?;
            println!("All sessions: {:?}", sessions);

            // Disconnect gracefully
            println!("Disconnecting...");
            manager.disconnect().await?;

            println!("Final status: {:?}", manager.get_status());
        }
        Err(e) => {
            println!("Connection failed: {}", e);
        }
    }

    // Clean up
    event_handler.abort();

    println!("Example completed!");
    Ok(())
}
