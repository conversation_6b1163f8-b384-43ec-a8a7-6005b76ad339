{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 5546582051241965818, "deps": [[654232091421095663, "tauri_utils", false, 15355600758100773933], [3150220818285335163, "url", false, 16586463868209902736], [4143744114649553716, "raw_window_handle", false, 3850038477068984947], [7606335748176206944, "dpi", false, 14102429171621091192], [9010263965687315507, "http", false, 3354175764241350141], [9689903380558560274, "serde", false, 10996426815917291500], [10806645703491011684, "thiserror", false, 44651847147331727], [12943761728066819757, "build_script_build", false, 12534829795320157039], [13116089016666501665, "windows", false, 9661768058793115304], [16362055519698394275, "serde_json", false, 7262301444845053042], [16727543399706004146, "cookie", false, 6193473348559863361]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-4846be73edbebc0a\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}