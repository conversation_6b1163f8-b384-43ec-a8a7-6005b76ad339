{"$message_type":"diagnostic","message":"this `impl` can be derived","code":{"code":"clippy::derivable_impls","explanation":null},"level":"warning","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs","byte_start":581,"byte_end":686,"line_start":23,"line_end":27,"column_start":1,"column_end":2,"is_primary":true,"text":[{"text":"impl Default for MessagePriority {","highlight_start":1,"highlight_end":35},{"text":"    fn default() -> Self {","highlight_start":1,"highlight_end":27},{"text":"        MessagePriority::Normal","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::derivable_impls)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"replace the manual implementation with a derive attribute and mark the default variant","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs","byte_start":581,"byte_end":688,"line_start":23,"line_end":28,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"impl Default for MessagePriority {","highlight_start":1,"highlight_end":35},{"text":"    fn default() -> Self {","highlight_start":1,"highlight_end":27},{"text":"        MessagePriority::Normal","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs","byte_start":483,"byte_end":483,"line_start":16,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub enum MessagePriority {","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"#[derive(Default)]\n","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs","byte_start":529,"byte_end":529,"line_start":18,"line_end":18,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    Normal = 1,","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"#[default]\n    ","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this `impl` can be derived\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs:23:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Default for MessagePriority {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn default() -> Self {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        MessagePriority::Normal\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|_^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::derivable_impls)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: replace the manual implementation with a derive attribute and mark the default variant\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ #[derive(Default)]\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0mpub enum MessagePriority {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     Low = 0,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10m#[default]\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~     \u001b[0m\u001b[0mNormal = 1,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"manual implementation of `rsplit_once`","code":{"code":"clippy::manual_split_once","explanation":null},"level":"warning","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs","byte_start":21082,"byte_end":21193,"line_start":651,"line_end":654,"column_start":40,"column_end":32,"is_primary":true,"text":[{"text":"                    let operation_id = message","highlight_start":40,"highlight_end":47},{"text":"                        .id","highlight_start":1,"highlight_end":28},{"text":"                        .rsplitn(2, '_')","highlight_start":1,"highlight_end":41},{"text":"                        .nth(1)","highlight_start":1,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_split_once","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::manual_split_once)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs","byte_start":21082,"byte_end":21193,"line_start":651,"line_end":654,"column_start":40,"column_end":32,"is_primary":true,"text":[{"text":"                    let operation_id = message","highlight_start":40,"highlight_end":47},{"text":"                        .id","highlight_start":1,"highlight_end":28},{"text":"                        .rsplitn(2, '_')","highlight_start":1,"highlight_end":41},{"text":"                        .nth(1)","highlight_start":1,"highlight_end":32}],"label":null,"suggested_replacement":"message\n                        .id.rsplit_once('_').map(|x| x.0)","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: manual implementation of `rsplit_once`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\bulk_manager.rs:651:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m651\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                    let operation_id = message\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m ________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m652\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m653\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .rsplitn(2, '_')\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m654\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .nth(1)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|_______________________________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_split_once\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::manual_split_once)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m651\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    let operation_id = \u001b[0m\u001b[0m\u001b[38;5;10mmessage\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m652\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+                         .id.rsplit_once('_').map(|x| x.0)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"2 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 2 warnings emitted\u001b[0m\n\n"}
