{"rustc": 16591470773350601817, "features": "[\"def\", \"discovery\", \"futures\", \"parser\", \"postgres\", \"probe\", \"query\", \"runtime-tokio-rustls\", \"sea-query-binder\", \"sqlx\", \"sqlx-dep\", \"sqlx-postgres\", \"writer\"]", "declared_features": "[\"debug-print\", \"def\", \"default\", \"discovery\", \"futures\", \"log\", \"mysql\", \"parser\", \"planetscale\", \"postgres\", \"postgres-vector\", \"probe\", \"query\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-query-binder\", \"serde\", \"sqlite\", \"sqlx\", \"sqlx-all\", \"sqlx-dep\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"with-serde\", \"writer\"]", "target": 7760212573621923541, "profile": 15657897354478470176, "path": 4811511947006269738, "deps": [[2706460456408817945, "futures", false, 6695622145210466527], [7161281228672193341, "sea_query", false, 3205212379452710621], [7817431159498251116, "sea_query_binder", false, 8738364181399071367], [13942520304170068984, "sea_schema_derive", false, 16992722671639991179], [17982831385697850842, "sqlx", false, 8997539274098623822]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-schema-1cf94d4d212c0053\\dep-lib-sea_schema", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}