{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 8284550627669777295, "deps": [[2995469292676432503, "uuid1", false, 6998241069174797990], [3150220818285335163, "url", false, 14195290360083940607], [6913375703034175521, "build_script_build", false, 14866715180653449587], [9122563107207267705, "dyn_clone", false, 3836249447545788828], [9689903380558560274, "serde", false, 16503059047051001516], [14923790796823607459, "indexmap", false, 8279403583340669594], [16071897500792579091, "schemars_derive", false, 14786122419714904619], [16362055519698394275, "serde_json", false, 14102427500185749966]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-c5df95aa3fa11654\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}