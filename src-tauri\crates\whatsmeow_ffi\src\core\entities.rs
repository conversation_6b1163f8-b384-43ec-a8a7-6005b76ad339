use base64::{engine::general_purpose::STANDARD as BASE64, Engine as _};
use serde::{Deserialize, Deserializer, Serialize};

// Re-export enhanced media types
pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};

/// Represents logging levels for the WhatsApp FFI library
#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(i32)]
pub enum LogLevel {
    Off = 0,
    Error = 1,
    Warn = 2,
    #[default]
    Info = 3,
    Debug = 4,
}

impl LogLevel {
    /// Convert from i32 to LogLevel
    pub fn from_i32(value: i32) -> Option<Self> {
        match value {
            0 => Some(LogLevel::Off),
            1 => Some(LogLevel::Error),
            2 => Some(LogLevel::Warn),
            3 => Some(LogLevel::Info),
            4 => Some(LogLevel::Debug),
            _ => None,
        }
    }
}

/// Represents different types of WhatsApp messages
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum MessageType {
    Text {
        text: String,
    },
    Image {
        caption: Option<String>,
        media_info: MediaInfo,
    },
    Video {
        caption: Option<String>,
        media_info: MediaInfo,
    },
    Audio {
        media_info: MediaInfo,
        is_voice: bool,
    },
    Document {
        caption: Option<String>,
        media_info: MediaInfo,
        filename: Option<String>,
    },
    Sticker {
        media_info: MediaInfo,
    },
}

/// Media information for non-text messages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MediaInfo {
    pub mime_type: String,
    pub file_size: Option<u64>,
    pub file_sha256: Option<String>,
    pub file_enc_sha256: Option<String>,
    pub media_key: Option<String>,
    pub direct_path: Option<String>,
    pub url: Option<String>,
    #[serde(deserialize_with = "deserialize_thumbnail", default)]
    pub thumbnail: Option<Vec<u8>>,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub duration: Option<u32>, // For audio/video in seconds
}

/// Represents a WhatsApp message with enhanced media support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub from: String,
    pub message_type: MessageType,
    pub timestamp: i64,
    pub message_id: String,
}

impl Message {
    /// Create a new text message (backward compatibility)
    pub fn new_text(from: String, text: String, timestamp: i64, message_id: String) -> Self {
        Self {
            from,
            message_type: MessageType::Text { text },
            timestamp,
            message_id,
        }
    }

    /// Get text content if this is a text message (backward compatibility)
    pub fn text(&self) -> String {
        match &self.message_type {
            MessageType::Text { text } => text.clone(),
            MessageType::Image { caption, .. }
            | MessageType::Video { caption, .. }
            | MessageType::Document { caption, .. } => {
                caption.clone().unwrap_or_else(|| "[Media]".to_string())
            }
            MessageType::Audio { is_voice, .. } => {
                if *is_voice {
                    "[Voice Message]".to_string()
                } else {
                    "[Audio]".to_string()
                }
            }
            MessageType::Sticker { .. } => "[Sticker]".to_string(),
        }
    }

    /// Get caption if this message type supports captions
    pub fn caption(&self) -> Option<&str> {
        match &self.message_type {
            MessageType::Image { caption, .. }
            | MessageType::Video { caption, .. }
            | MessageType::Document { caption, .. } => caption.as_deref(),
            _ => None,
        }
    }

    /// Check if this is a media message
    pub fn is_media(&self) -> bool {
        !matches!(self.message_type, MessageType::Text { .. })
    }

    /// Get media info if this is a media message
    pub fn media_info(&self) -> Option<&MediaInfo> {
        match &self.message_type {
            MessageType::Image { media_info, .. }
            | MessageType::Video { media_info, .. }
            | MessageType::Audio { media_info, .. }
            | MessageType::Document { media_info, .. }
            | MessageType::Sticker { media_info, .. } => Some(media_info),
            MessageType::Text { .. } => None,
        }
    }
}

/// Connection status of the WhatsApp client
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConnectionStatus {
    Disconnected,
    Connecting,
    Connected,
    LoggedOut,
}

impl From<i32> for ConnectionStatus {
    fn from(value: i32) -> Self {
        match value {
            0 => ConnectionStatus::Disconnected,
            1 => ConnectionStatus::Connecting,
            2 => ConnectionStatus::Connected,
            3 => ConnectionStatus::LoggedOut,
            _ => ConnectionStatus::Disconnected,
        }
    }
}

/// Events that can be received from the WhatsApp client
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WhatsAppEvent {
    QRCode(String),
    MessageReceived(Box<Message>),
    ConnectionStatusChanged {
        status: ConnectionStatus,
        reason: Option<String>,
    },
    Error {
        code: i32,
        message: String,
    },
}

/// QR code event data
#[derive(Debug, Deserialize)]
pub struct QRCodeEvent {
    pub code: String,
}

/// Message event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageEvent {
    pub from: String,
    pub message_type: crate::core::entities::MessageType,
    pub timestamp: i64,
    pub message_id: String,
}

/// Connection status event data
#[derive(Debug, Deserialize)]
pub struct ConnectionStatusEvent {
    pub status: i32,
    pub reason: Option<String>,
}

/// Error event data
#[derive(Debug, Deserialize)]
pub struct ErrorEvent {
    pub code: i32,
    pub message: String,
}



/// Custom deserializer for thumbnail field that handles both base64 strings and byte arrays
fn deserialize_thumbnail<'de, D>(deserializer: D) -> Result<Option<Vec<u8>>, D::Error>
where
    D: Deserializer<'de>,
{
    #[derive(Deserialize)]
    #[serde(untagged)]
    enum ThumbnailData {
        String(String),
        Bytes(Vec<u8>),
        Null,
    }

    match Option::<ThumbnailData>::deserialize(deserializer)? {
        Some(ThumbnailData::String(s)) => {
            // Try to decode base64 string
            match BASE64.decode(&s) {
                Ok(bytes) => Ok(Some(bytes)),
                Err(e) => {
                    // If base64 decoding fails, log the error but don't fail the entire deserialization
                    eprintln!("Warning: Failed to decode base64 thumbnail: {}", e);
                    Ok(None)
                }
            }
        }
        Some(ThumbnailData::Bytes(bytes)) => Ok(Some(bytes)),
        Some(ThumbnailData::Null) | None => Ok(None),
    }
}
