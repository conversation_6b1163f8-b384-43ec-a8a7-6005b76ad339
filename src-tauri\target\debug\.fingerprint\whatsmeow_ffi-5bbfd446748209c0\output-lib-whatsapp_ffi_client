{"$message_type":"diagnostic","message":"the name `MediaSource` is defined multiple times","code":{"code":"E0255","explanation":"You can't import a value whose name is the same as another value defined in the\nmodule.\n\nErroneous code example:\n\n```compile_fail,E0255\nuse bar::foo; // error: an item named `foo` is already in scope\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse bar::foo as bar_foo; // ok!\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nOr you can reference the item with its parent:\n\n```\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {\n    bar::foo(); // we get the item by referring to its parent\n}\n```\n"},"level":"error","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":6150,"byte_end":6170,"line_start":218,"line_end":218,"column_start":1,"column_end":21,"is_primary":true,"text":[{"text":"pub enum MediaSource {","highlight_start":1,"highlight_end":21}],"label":"`MediaSource` redefined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":214,"byte_end":225,"line_start":6,"line_end":6,"column_start":30,"column_end":41,"is_primary":false,"text":[{"text":"pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};","highlight_start":30,"highlight_end":41}],"label":"previous import of the type `MediaSource` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MediaSource` must be defined only once in the type namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you can use `as` to change the binding name of the import","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":225,"byte_end":225,"line_start":6,"line_end":6,"column_start":41,"column_end":41,"is_primary":true,"text":[{"text":"pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};","highlight_start":41,"highlight_end":41}],"label":null,"suggested_replacement":" as OtherMediaSource","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0255]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `MediaSource` is defined multiple times\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\entities.rs:218:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious import of the type `MediaSource` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m218\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum MediaSource {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`MediaSource` redefined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `MediaSource` must be defined only once in the type namespace of this module\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can use `as` to change the binding name of the import\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0mpub use crate::core::media::{MediaSource\u001b[0m\u001b[0m\u001b[38;5;10m as OtherMediaSource\u001b[0m\u001b[0m, MessageContent, MediaError, MediaMetadata, MediaType};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the name `MessageContent` is defined multiple times","code":{"code":"E0255","explanation":"You can't import a value whose name is the same as another value defined in the\nmodule.\n\nErroneous code example:\n\n```compile_fail,E0255\nuse bar::foo; // error: an item named `foo` is already in scope\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse bar::foo as bar_foo; // ok!\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nOr you can reference the item with its parent:\n\n```\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {\n    bar::foo(); // we get the item by referring to its parent\n}\n```\n"},"level":"error","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":12606,"byte_end":12629,"line_start":393,"line_end":393,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"pub enum MessageContent {","highlight_start":1,"highlight_end":24}],"label":"`MessageContent` redefined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":227,"byte_end":241,"line_start":6,"line_end":6,"column_start":43,"column_end":57,"is_primary":false,"text":[{"text":"pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};","highlight_start":43,"highlight_end":57}],"label":"previous import of the type `MessageContent` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MessageContent` must be defined only once in the type namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you can use `as` to change the binding name of the import","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":241,"byte_end":241,"line_start":6,"line_end":6,"column_start":57,"column_end":57,"is_primary":true,"text":[{"text":"pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};","highlight_start":57,"highlight_end":57}],"label":null,"suggested_replacement":" as OtherMessageContent","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0255]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `MessageContent` is defined multiple times\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\entities.rs:393:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious import of the type `MessageContent` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m393\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum MessageContent {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`MessageContent` redefined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `MessageContent` must be defined only once in the type namespace of this module\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can use `as` to change the binding name of the import\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0mpub use crate::core::media::{MediaSource, MessageContent\u001b[0m\u001b[0m\u001b[38;5;10m as OtherMessageContent\u001b[0m\u001b[0m, MediaError, MediaMetadata, MediaType};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `MediaSource` and `MessageContent`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":214,"byte_end":225,"line_start":6,"line_end":6,"column_start":30,"column_end":41,"is_primary":true,"text":[{"text":"pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};","highlight_start":30,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":227,"byte_end":241,"line_start":6,"line_end":6,"column_start":43,"column_end":57,"is_primary":true,"text":[{"text":"pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};","highlight_start":43,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\entities.rs","byte_start":214,"byte_end":243,"line_start":6,"line_end":6,"column_start":30,"column_end":59,"is_primary":true,"text":[{"text":"pub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};","highlight_start":30,"highlight_end":59}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `MediaSource` and `MessageContent`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\entities.rs:6:30\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::core::media::{MediaSource, MessageContent, MediaError, MediaMetadata, MediaType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this `if` statement can be collapsed","code":{"code":"clippy::collapsible_if","explanation":null},"level":"warning","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\media.rs","byte_start":18369,"byte_end":18811,"line_start":541,"line_end":547,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"            if (0xC0..=0xC3).contains(&marker) || (0xC5..=0xC7).contains(&marker) || (0xC9..=0xCB).contains(&marker) || (0xCD..=0xCF).contains(&marker) {","highlight_start":13,"highlight_end":154},{"text":"                if i + 9 < data.len() {","highlight_start":1,"highlight_end":40},{"text":"                    let height = u16::from_be_bytes([data[i + 5], data[i + 6]]) as u32;","highlight_start":1,"highlight_end":88},{"text":"                    let width = u16::from_be_bytes([data[i + 7], data[i + 8]]) as u32;","highlight_start":1,"highlight_end":87},{"text":"                    return Ok((width, height));","highlight_start":1,"highlight_end":48},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::collapsible_if)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"collapse nested if block","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\media.rs","byte_start":18369,"byte_end":18811,"line_start":541,"line_end":547,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"            if (0xC0..=0xC3).contains(&marker) || (0xC5..=0xC7).contains(&marker) || (0xC9..=0xCB).contains(&marker) || (0xCD..=0xCF).contains(&marker) {","highlight_start":13,"highlight_end":154},{"text":"                if i + 9 < data.len() {","highlight_start":1,"highlight_end":40},{"text":"                    let height = u16::from_be_bytes([data[i + 5], data[i + 6]]) as u32;","highlight_start":1,"highlight_end":88},{"text":"                    let width = u16::from_be_bytes([data[i + 7], data[i + 8]]) as u32;","highlight_start":1,"highlight_end":87},{"text":"                    return Ok((width, height));","highlight_start":1,"highlight_end":48},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":"if ((0xC0..=0xC3).contains(&marker) || (0xC5..=0xC7).contains(&marker) || (0xC9..=0xCB).contains(&marker) || (0xCD..=0xCF).contains(&marker)) && i + 9 < data.len() {\n                let height = u16::from_be_bytes([data[i + 5], data[i + 6]]) as u32;\n                let width = u16::from_be_bytes([data[i + 7], data[i + 8]]) as u32;\n                return Ok((width, height));\n            }","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this `if` statement can be collapsed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\media.rs:541:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m541\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   if (0xC0..=0xC3).contains(&marker) || (0xC5..=0xC7).contains(&marker) || (0xC9..=0xCB).contains(&marker) || (0xCD..=0xCF).contains(\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       if i + 9 < data.len() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m543\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m           let height = u16::from_be_bytes([data[i + 5], data[i + 6]]) as u32;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m544\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m           let width = u16::from_be_bytes([data[i + 7], data[i + 8]]) as u32;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m547\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|_______^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::collapsible_if)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: collapse nested if block\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m541\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[38;5;10mif ((0xC0..=0xC3).contains(&marker) || (0xC5..=0xC7).contains(&marker) || (0xC9..=0xCB).contains(&marker) || (0xCD..=0xCF).contains(&marker)) && i + 9 < data.len() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+                 let height = u16::from_be_bytes([data[i + 5], data[i + 6]]) as u32;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m543\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+                 let width = u16::from_be_bytes([data[i + 7], data[i + 8]]) as u32;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m544\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+                 return Ok((width, height));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m545\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 2 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors; 2 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0255`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0255`.\u001b[0m\n"}
