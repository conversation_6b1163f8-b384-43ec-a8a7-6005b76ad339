{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 12154226576749303749, "deps": [[376837177317575824, "softbuffer", false, 16396877924218411020], [654232091421095663, "tauri_utils", false, 15355600758100773933], [2013030631243296465, "webview2_com", false, 5504809740823490624], [3150220818285335163, "url", false, 16586463868209902736], [3722963349756955755, "once_cell", false, 16821080632637768006], [4143744114649553716, "raw_window_handle", false, 3850038477068984947], [5986029879202738730, "log", false, 3210362874149672375], [8826339825490770380, "tao", false, 13609474022847578049], [9010263965687315507, "http", false, 3354175764241350141], [9141053277961803901, "wry", false, 137988699580944099], [12304025191202589669, "build_script_build", false, 12990290497152856402], [12943761728066819757, "tauri_runtime", false, 5922118006371360892], [13116089016666501665, "windows", false, 9661768058793115304]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-2a267af7fd593014\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}