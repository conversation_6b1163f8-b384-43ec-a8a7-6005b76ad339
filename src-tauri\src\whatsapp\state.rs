//! Service state management for WhatsApp service

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use whatsapp_ffi_client::{ConnectionStatus, LogLevel};

/// Service status enumeration
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceStatus {
    /// Service is not initialized
    NotInitialized,
    /// Service is initializing
    Initializing,
    /// Service is initialized but not connected
    Initialized,
    /// Service is connecting to WhatsApp
    Connecting,
    /// Service is connected to WhatsApp
    Connected,
    /// Service is disconnecting from WhatsApp
    Disconnecting,
    /// Service is disconnected from WhatsApp
    Disconnected,
    /// Service is in error state
    Error,
    /// Service is shutting down
    ShuttingDown,
    /// Service has been shut down
    Shutdown,
}

impl Default for ServiceStatus {
    fn default() -> Self {
        Self::NotInitialized
    }
}

/// Service configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServiceConfig {
    /// Path to the WhatsApp FFI library
    pub library_path: String,
    /// Path to the database file
    pub db_path: String,
    /// Log level for the WhatsApp client
    pub log_level: LogLevel,
    /// Auto-reconnect on connection loss
    pub auto_reconnect: bool,
    /// Maximum reconnection attempts
    pub max_reconnect_attempts: u32,
    /// Reconnection delay in seconds
    pub reconnect_delay_seconds: u64,
}

impl ServiceConfig {
    /// Create a new service configuration with default values
    pub fn new(library_path: String, db_path: String) -> Self {
        Self {
            library_path,
            db_path,
            log_level: LogLevel::Info,
            auto_reconnect: true,
            max_reconnect_attempts: 5,
            reconnect_delay_seconds: 5,
        }
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<(), String> {
        // Validate library path
        if self.library_path.is_empty() {
            return Err("Library path cannot be empty".to_string());
        }

        let library_path = PathBuf::from(&self.library_path);
        if !library_path.exists() {
            return Err(format!(
                "Library path does not exist: {}",
                self.library_path
            ));
        }

        // Validate database path directory
        if self.db_path.is_empty() {
            return Err("Database path cannot be empty".to_string());
        }

        let db_path = PathBuf::from(&self.db_path);
        if let Some(parent) = db_path.parent() {
            if !parent.exists() {
                return Err(format!(
                    "Database directory does not exist: {}",
                    parent.display()
                ));
            }
        }

        // Validate reconnection settings
        if self.max_reconnect_attempts == 0 {
            return Err("Max reconnect attempts must be greater than 0".to_string());
        }

        if self.reconnect_delay_seconds == 0 {
            return Err("Reconnect delay must be greater than 0".to_string());
        }

        Ok(())
    }

    /// Set log level
    pub fn with_log_level(mut self, log_level: LogLevel) -> Self {
        self.log_level = log_level;
        self
    }

    /// Set auto-reconnect
    pub fn with_auto_reconnect(mut self, auto_reconnect: bool) -> Self {
        self.auto_reconnect = auto_reconnect;
        self
    }

    /// Set max reconnect attempts
    pub fn with_max_reconnect_attempts(mut self, max_attempts: u32) -> Self {
        self.max_reconnect_attempts = max_attempts;
        self
    }

    /// Set reconnect delay
    pub fn with_reconnect_delay(mut self, delay_seconds: u64) -> Self {
        self.reconnect_delay_seconds = delay_seconds;
        self
    }
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self::new(
            "whatsmeow_ffi.dll".to_string(), // Default Windows library name
            "whatsapp.db".to_string(),
        )
    }
}

/// Service state tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceState {
    /// Current service status
    status: ServiceStatus,
    /// Current WhatsApp connection status
    connection_status: ConnectionStatus,
    /// Last error message (if any)
    last_error: Option<String>,
    /// Service start time
    started_at: DateTime<Utc>,
    /// Last status change time
    last_status_change: DateTime<Utc>,
    /// Connection attempt count
    connection_attempts: u32,
    /// Last successful connection time
    last_connected_at: Option<DateTime<Utc>>,
    /// Total messages sent
    messages_sent: u64,
    /// Total messages received
    messages_received: u64,
}

impl ServiceState {
    /// Create a new service state
    pub fn new() -> Self {
        let now = Utc::now();
        Self {
            status: ServiceStatus::NotInitialized,
            connection_status: ConnectionStatus::Disconnected,
            last_error: None,
            started_at: now,
            last_status_change: now,
            connection_attempts: 0,
            last_connected_at: None,
            messages_sent: 0,
            messages_received: 0,
        }
    }

    /// Get current status
    pub fn status(&self) -> ServiceStatus {
        self.status
    }

    /// Set status and update timestamp
    pub fn set_status(&mut self, status: ServiceStatus) {
        if self.status != status {
            self.status = status;
            self.last_status_change = Utc::now();

            // Clear error when status changes to non-error state
            if !matches!(status, ServiceStatus::Error) {
                self.last_error = None;
            }

            // Update connection timestamp when connected
            if matches!(status, ServiceStatus::Connected) {
                self.last_connected_at = Some(Utc::now());
            }
        }
    }

    /// Get connection status
    pub fn connection_status(&self) -> ConnectionStatus {
        self.connection_status
    }

    /// Set connection status
    pub fn set_connection_status(&mut self, status: ConnectionStatus) {
        self.connection_status = status;

        // Increment connection attempts when connecting
        if matches!(status, ConnectionStatus::Connecting) {
            self.connection_attempts += 1;
        }
    }

    /// Get last error
    pub fn last_error(&self) -> Option<&str> {
        self.last_error.as_deref()
    }

    /// Set last error
    pub fn set_last_error(&mut self, error: Option<String>) {
        self.last_error = error;
    }

    /// Get service uptime
    pub fn uptime(&self) -> chrono::Duration {
        Utc::now() - self.started_at
    }

    /// Get time since last status change
    pub fn time_since_last_change(&self) -> chrono::Duration {
        Utc::now() - self.last_status_change
    }

    /// Get connection attempts
    pub fn connection_attempts(&self) -> u32 {
        self.connection_attempts
    }

    /// Reset connection attempts
    pub fn reset_connection_attempts(&mut self) {
        self.connection_attempts = 0;
    }

    /// Get last connected time
    pub fn last_connected_at(&self) -> Option<DateTime<Utc>> {
        self.last_connected_at
    }

    /// Get messages sent count
    pub fn messages_sent(&self) -> u64 {
        self.messages_sent
    }

    /// Increment messages sent
    pub fn increment_messages_sent(&mut self) {
        self.messages_sent += 1;
    }

    /// Get messages received count
    pub fn messages_received(&self) -> u64 {
        self.messages_received
    }

    /// Increment messages received
    pub fn increment_messages_received(&mut self) {
        self.messages_received += 1;
    }

    /// Check if service is in a healthy state
    pub fn is_healthy(&self) -> bool {
        matches!(
            self.status,
            ServiceStatus::Initialized | ServiceStatus::Connected | ServiceStatus::Disconnected
        ) && self.last_error.is_none()
    }

    /// Check if service can accept operations
    pub fn can_operate(&self) -> bool {
        matches!(self.status, ServiceStatus::Connected)
    }

    /// Get service statistics
    pub fn get_stats(&self) -> ServiceStats {
        ServiceStats {
            uptime_seconds: self.uptime().num_seconds() as u64,
            connection_attempts: self.connection_attempts,
            messages_sent: self.messages_sent,
            messages_received: self.messages_received,
            last_connected_at: self.last_connected_at,
            is_healthy: self.is_healthy(),
        }
    }
}

impl Default for ServiceState {
    fn default() -> Self {
        Self::new()
    }
}

/// Service statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceStats {
    /// Service uptime in seconds
    pub uptime_seconds: u64,
    /// Total connection attempts
    pub connection_attempts: u32,
    /// Total messages sent
    pub messages_sent: u64,
    /// Total messages received
    pub messages_received: u64,
    /// Last successful connection time
    pub last_connected_at: Option<DateTime<Utc>>,
    /// Whether service is in healthy state
    pub is_healthy: bool,
}
