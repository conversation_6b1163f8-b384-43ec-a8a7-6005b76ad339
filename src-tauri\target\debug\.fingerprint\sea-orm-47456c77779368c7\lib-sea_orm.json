{"rustc": 16591470773350601817, "features": "[\"bigdecimal\", \"chrono\", \"default\", \"macros\", \"mock\", \"postgres-array\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sea-query-binder\", \"serde_json\", \"sqlx\", \"sqlx-dep\", \"sqlx-postgres\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"debug-print\", \"default\", \"ipnetwork\", \"json-array\", \"macros\", \"mock\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"proxy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sea-orm-internal\", \"sea-query-binder\", \"seaography\", \"serde_json\", \"sqlite-use-returning-for-3_35\", \"sqlx\", \"sqlx-all\", \"sqlx-dep\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"tests-cfg\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 5911612443439219948, "profile": 2241668132362809309, "path": 15205570603458590620, "deps": [[1188017320647144970, "async_stream", false, 8384637549452119572], [2995469292676432503, "uuid", false, 13329635685914659949], [3150220818285335163, "url", false, 5161306808710849531], [3302295501534065768, "strum", false, 13313147422330952887], [3525657182790186941, "ouroboros", false, 8940244016122669730], [5986029879202738730, "log", false, 14504179726639124161], [7161281228672193341, "sea_query", false, 6679749599639037576], [7817431159498251116, "sea_query_binder", false, 7922396527558910547], [8606274917505247608, "tracing", false, 702195515399835879], [9689903380558560274, "serde", false, 1190822165864209379], [9897246384292347999, "chrono", false, 8330261631647650351], [10629569228670356391, "futures_util", false, 8935210692092416383], [10806645703491011684, "thiserror", false, 3797447290076662653], [11946729385090170470, "async_trait", false, 2389517705436373950], [12409575957772518135, "time", false, 6202611441483771785], [14647456484942590313, "bigdecimal", false, 1477368331990517333], [16119793329258425851, "rust_decimal", false, 13035123693571945880], [16362055519698394275, "serde_json", false, 10974075219193167317], [17625815326946657219, "sea_orm_macros", false, 9619219286990109587], [17982831385697850842, "sqlx", false, 1919210647607020814]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-47456c77779368c7\\dep-lib-sea_orm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}