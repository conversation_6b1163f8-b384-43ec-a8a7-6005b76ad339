{"rustc": 16591470773350601817, "features": "[\"clap\", \"cli\", \"dotenvy\", \"runtime-tokio-rustls\", \"sqlx-postgres\"]", "declared_features": "[\"async-std\", \"clap\", \"cli\", \"codegen\", \"default\", \"dotenvy\", \"postgres-vector\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-orm-codegen\", \"sea-schema\", \"sqlx\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\"]", "target": 5960219669809230312, "profile": 15657897354478470176, "path": 6955836321804170307, "deps": [[3150220818285335163, "url", false, 16586463868209902736], [3405707034081185165, "dotenvy", false, 9497081734834712602], [8606274917505247608, "tracing", false, 13752143806742742294], [9451456094439810778, "regex", false, 1995452313642458860], [9897246384292347999, "chrono", false, 541124106390407368], [16230660778393187092, "tracing_subscriber", false, 10624335275707051079], [17155886227862585100, "glob", false, 16192797392537921819], [17612818546626403359, "clap", false, 16621575627164328192]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-cli-6fc58156e9159c59\\dep-lib-sea_orm_cli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}