package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"
	"unsafe"

	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/proto/waE2E"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"

	_ "github.com/mattn/go-sqlite3"
)

/*
#cgo CFLAGS: -I.
#include "cdefs.h"
*/
import "C"

// WhatsAppClient wraps the whatsmeow client with FFI-friendly interface
type WhatsAppClient struct {
	client          *whatsmeow.Client
	container       *sqlstore.Container
	callback        C.EventCallback
	userData        unsafe.Pointer
	ctx             context.Context
	cancel          context.CancelFunc
	mu              sync.RWMutex
	connected       bool
	shutdown        bool            // Flag to prevent callbacks after shutdown
	shutdownDone    chan struct{}   // Channel to signal shutdown completion
	activeCallbacks sync.WaitGroup  // Track active callbacks
	logger          LoggerInterface // Configurable logger instance
}

// Global client registry to manage multiple instances
var (
	clientRegistry = make(map[uintptr]*WhatsAppClient)
	registryMu     sync.RWMutex
	nextHandle     uintptr = 1
)

// registerClient adds a client to the registry and returns a handle
func registerClient(client *WhatsAppClient) uintptr {
	registryMu.Lock()
	defer registryMu.Unlock()

	handle := nextHandle
	nextHandle++
	clientRegistry[handle] = client
	return handle
}

// getClient retrieves a client from the registry
func getClient(handle uintptr) *WhatsAppClient {
	registryMu.RLock()
	defer registryMu.RUnlock()
	return clientRegistry[handle]
}

// unregisterClient removes a client from the registry
func unregisterClient(handle uintptr) {
	registryMu.Lock()
	defer registryMu.Unlock()
	delete(clientRegistry, handle)
}

// NewWhatsAppClient creates a new WhatsApp client instance
func NewWhatsAppClient(dbPath string, callback C.EventCallback, userData unsafe.Pointer) (*WhatsAppClient, error) {
	return NewWhatsAppClientWithLogger(dbPath, callback, userData, GetGlobalLogger())
}

// NewWhatsAppClientWithLogger creates a new WhatsApp client instance with custom logger
func NewWhatsAppClientWithLogger(dbPath string, callback C.EventCallback, userData unsafe.Pointer, logger LoggerInterface) (*WhatsAppClient, error) {
	// Create context for the client
	ctx, cancel := context.WithCancel(context.Background())

	// Create database container with configurable logging
	dbLogger := NewWhatsAppLoggerAdapter(logger, "Database")
	container, err := sqlstore.New(ctx, "sqlite3", "file:"+dbPath+"?_foreign_keys=on", dbLogger)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create database container: %w", err)
	}

	// Get the first device store
	deviceStore, err := container.GetFirstDevice(ctx)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to get device store: %w", err)
	}

	// Create client logger with configurable logging
	clientLogger := NewWhatsAppLoggerAdapter(logger, "Client")

	// Create whatsmeow client
	client := whatsmeow.NewClient(deviceStore, clientLogger)

	waClient := &WhatsAppClient{
		client:          client,
		container:       container,
		callback:        callback,
		userData:        userData,
		ctx:             ctx,
		cancel:          cancel,
		connected:       false,
		shutdown:        false,
		shutdownDone:    make(chan struct{}),
		activeCallbacks: sync.WaitGroup{},
		logger:          logger,
	}

	// Set up event handlers
	waClient.setupEventHandlers()

	return waClient, nil
}

// safeSendEvent sends an event only if the client hasn't been shutdown
func (w *WhatsAppClient) safeSendEvent(eventType C.int, data interface{}) {
	w.mu.RLock()
	shutdown := w.shutdown
	callback := w.callback
	userData := w.userData
	w.mu.RUnlock()

	if shutdown || callback == nil {
		return
	}

	// Track active callback
	w.activeCallbacks.Add(1)
	defer w.activeCallbacks.Done()

	// Double-check shutdown status after acquiring callback tracking
	w.mu.RLock()
	if w.shutdown {
		w.mu.RUnlock()
		return
	}
	w.mu.RUnlock()

	sendEvent(callback, userData, eventType, data)
}

// setupEventHandlers configures event handlers for the WhatsApp client
func (w *WhatsAppClient) setupEventHandlers() {
	w.client.AddEventHandler(func(evt interface{}) {
		switch v := evt.(type) {
		case *events.Message:
			w.handleMessage(v)
		case *events.QR:
			w.handleQRCode(v)
		case *events.Connected:
			w.handleConnected()
		case *events.Disconnected:
			w.handleDisconnected()
		case *events.LoggedOut:
			w.handleLoggedOut(v)
		}
	})
}

// handleMessage processes incoming messages with comprehensive media support
func (w *WhatsAppClient) handleMessage(evt *events.Message) {
	if evt.Message == nil {
		return
	}

	var messageType MessageType

	// Handle different message types
	if conversation := evt.Message.GetConversation(); conversation != "" {
		// Simple text message
		messageType = MessageType{
			Type: "Text",
			Data: TextMessageData{
				Text: conversation,
			},
		}
	} else if extText := evt.Message.GetExtendedTextMessage(); extText != nil {
		// Extended text message
		messageType = MessageType{
			Type: "Text",
			Data: TextMessageData{
				Text: extText.GetText(),
			},
		}
	} else if imageMsg := evt.Message.GetImageMessage(); imageMsg != nil {
		// Image message
		mediaInfo := w.extractMediaInfo(imageMsg.GetURL(), imageMsg.GetMimetype(), imageMsg.GetDirectPath(),
			imageMsg.GetFileLength(), imageMsg.GetFileSHA256(), imageMsg.GetFileEncSHA256(),
			imageMsg.GetMediaKey(), imageMsg.GetJPEGThumbnail(),
			imageMsg.Width, imageMsg.Height, nil)

		messageType = MessageType{
			Type: "Image",
			Data: MediaMessageData{
				Caption:   stringPtr(imageMsg.GetCaption()),
				MediaInfo: mediaInfo,
			},
		}
	} else if videoMsg := evt.Message.GetVideoMessage(); videoMsg != nil {
		// Video message
		mediaInfo := w.extractMediaInfo(videoMsg.GetURL(), videoMsg.GetMimetype(), videoMsg.GetDirectPath(),
			videoMsg.GetFileLength(), videoMsg.GetFileSHA256(), videoMsg.GetFileEncSHA256(),
			videoMsg.GetMediaKey(), videoMsg.GetJPEGThumbnail(),
			videoMsg.Width, videoMsg.Height, videoMsg.Seconds)

		messageType = MessageType{
			Type: "Video",
			Data: MediaMessageData{
				Caption:   stringPtr(videoMsg.GetCaption()),
				MediaInfo: mediaInfo,
			},
		}
	} else if audioMsg := evt.Message.GetAudioMessage(); audioMsg != nil {
		// Audio message
		mediaInfo := w.extractMediaInfo(audioMsg.GetURL(), audioMsg.GetMimetype(), audioMsg.GetDirectPath(),
			audioMsg.GetFileLength(), audioMsg.GetFileSHA256(), audioMsg.GetFileEncSHA256(),
			audioMsg.GetMediaKey(), nil,
			nil, nil, audioMsg.Seconds)

		messageType = MessageType{
			Type: "Audio",
			Data: AudioMessageData{
				MediaInfo: mediaInfo,
				IsVoice:   audioMsg.GetPTT(), // Push-to-talk indicates voice message
			},
		}
	} else if docMsg := evt.Message.GetDocumentMessage(); docMsg != nil {
		// Document message
		mediaInfo := w.extractMediaInfo(docMsg.GetURL(), docMsg.GetMimetype(), docMsg.GetDirectPath(),
			docMsg.GetFileLength(), docMsg.GetFileSHA256(), docMsg.GetFileEncSHA256(),
			docMsg.GetMediaKey(), docMsg.GetJPEGThumbnail(),
			nil, nil, nil)

		messageType = MessageType{
			Type: "Document",
			Data: DocumentMessageData{
				Caption:   stringPtr(docMsg.GetCaption()),
				MediaInfo: mediaInfo,
				Filename:  stringPtr(docMsg.GetFileName()),
			},
		}
	} else if stickerMsg := evt.Message.GetStickerMessage(); stickerMsg != nil {
		// Sticker message
		mediaInfo := w.extractMediaInfo(stickerMsg.GetURL(), stickerMsg.GetMimetype(), stickerMsg.GetDirectPath(),
			stickerMsg.GetFileLength(), stickerMsg.GetFileSHA256(), stickerMsg.GetFileEncSHA256(),
			stickerMsg.GetMediaKey(), nil,
			stickerMsg.Width, stickerMsg.Height, nil)

		messageType = MessageType{
			Type: "Sticker",
			Data: MediaMessageData{
				Caption:   nil, // Stickers don't have captions
				MediaInfo: mediaInfo,
			},
		}
	} else {
		// Unknown message type - log details for debugging
		w.logger.Info(fmt.Sprintf("Unsupported message type received from %s", evt.Info.Sender.String()))

		// Log the raw message structure for debugging
		w.logUnsupportedMessageDetails(evt.Message)

		// Log all available message fields for debugging
		if evt.Message.GetContactMessage() != nil {
			w.logger.Info("Message contains: ContactMessage")
		}
		if evt.Message.GetLocationMessage() != nil {
			w.logger.Info("Message contains: LocationMessage")
		}
		if evt.Message.GetLiveLocationMessage() != nil {
			w.logger.Info("Message contains: LiveLocationMessage")
		}
		if evt.Message.GetGroupInviteMessage() != nil {
			w.logger.Info("Message contains: GroupInviteMessage")
		}
		if evt.Message.GetListMessage() != nil {
			w.logger.Info("Message contains: ListMessage")
		}
		if evt.Message.GetListResponseMessage() != nil {
			w.logger.Info("Message contains: ListResponseMessage")
		}
		if evt.Message.GetButtonsMessage() != nil {
			w.logger.Info("Message contains: ButtonsMessage")
		}
		if evt.Message.GetButtonsResponseMessage() != nil {
			w.logger.Info("Message contains: ButtonsResponseMessage")
		}
		if evt.Message.GetTemplateMessage() != nil {
			w.logger.Info("Message contains: TemplateMessage")
		}
		if evt.Message.GetTemplateButtonReplyMessage() != nil {
			w.logger.Info("Message contains: TemplateButtonReplyMessage")
		}
		if evt.Message.GetInteractiveMessage() != nil {
			w.logger.Info("Message contains: InteractiveMessage")
		}
		if evt.Message.GetReactionMessage() != nil {
			w.logger.Info("Message contains: ReactionMessage")
		}
		if evt.Message.GetPollCreationMessage() != nil {
			w.logger.Info("Message contains: PollCreationMessage")
		}
		if evt.Message.GetPollUpdateMessage() != nil {
			w.logger.Info("Message contains: PollUpdateMessage")
		}
		if evt.Message.GetProtocolMessage() != nil {
			w.logger.Info("Message contains: ProtocolMessage")
		}
		if evt.Message.GetEphemeralMessage() != nil {
			w.logger.Info("Message contains: EphemeralMessage")
		}
		if evt.Message.GetViewOnceMessage() != nil {
			w.logger.Info("Message contains: ViewOnceMessage")
		}

		// Create a detailed unsupported message with type information
		messageType = MessageType{
			Type: "Text",
			Data: TextMessageData{
				Text: "[Unsupported message type - check logs for details]",
			},
		}
	}

	messageEvent := MessageEvent{
		From:        evt.Info.Sender.String(),
		MessageType: messageType,
		Timestamp:   evt.Info.Timestamp.Unix(),
		MessageID:   evt.Info.ID,
	}

	w.safeSendEvent(EventMessageReceived, messageEvent)
}

// logUnsupportedMessageDetails logs detailed information about unsupported message types
func (w *WhatsAppClient) logUnsupportedMessageDetails(msg *waE2E.Message) {
	w.logger.Info("=== UNSUPPORTED MESSAGE DETAILS ===")

	// Try to marshal the message to JSON for detailed inspection
	if jsonData, err := json.MarshalIndent(msg, "", "  "); err == nil {
		// Truncate if too long to avoid log spam
		jsonStr := string(jsonData)
		if len(jsonStr) > 2000 {
			jsonStr = jsonStr[:2000] + "... (truncated)"
		}
		w.logger.Info(fmt.Sprintf("Raw message JSON: %s", jsonStr))
	} else {
		w.logger.Info(fmt.Sprintf("Failed to marshal message to JSON: %v", err))
	}

	// Log specific message type information
	w.logger.Info("Message type analysis:")
	if msg.GetConversation() != "" {
		w.logger.Info(fmt.Sprintf("  - Conversation: %s", msg.GetConversation()))
	}
	if msg.GetExtendedTextMessage() != nil {
		w.logger.Info("  - ExtendedTextMessage: present")
	}
	if msg.GetImageMessage() != nil {
		w.logger.Info("  - ImageMessage: present")
	}
	if msg.GetVideoMessage() != nil {
		w.logger.Info("  - VideoMessage: present")
	}
	if msg.GetAudioMessage() != nil {
		w.logger.Info("  - AudioMessage: present")
	}
	if msg.GetDocumentMessage() != nil {
		w.logger.Info("  - DocumentMessage: present")
	}
	if msg.GetStickerMessage() != nil {
		w.logger.Info("  - StickerMessage: present")
	}

	w.logger.Info("=== END UNSUPPORTED MESSAGE DETAILS ===")
}

// extractMediaInfo creates MediaInfo from whatsmeow message fields
func (w *WhatsAppClient) extractMediaInfo(url, mimeType, directPath string, fileLength uint64,
	fileSha256, fileEncSha256, mediaKey, thumbnail []byte,
	width, height, duration *uint32) MediaInfo {

	var mediaInfo MediaInfo
	mediaInfo.MimeType = mimeType

	if fileLength > 0 {
		mediaInfo.FileSize = &fileLength
	}

	if len(fileSha256) > 0 {
		sha256Str := fmt.Sprintf("%x", fileSha256)
		mediaInfo.FileSha256 = &sha256Str
	}

	if len(fileEncSha256) > 0 {
		encSha256Str := fmt.Sprintf("%x", fileEncSha256)
		mediaInfo.FileEncSha256 = &encSha256Str
	}

	if len(mediaKey) > 0 {
		mediaKeyStr := fmt.Sprintf("%x", mediaKey)
		mediaInfo.MediaKey = &mediaKeyStr
	}

	if directPath != "" {
		mediaInfo.DirectPath = &directPath
	}

	if url != "" {
		mediaInfo.URL = &url
	}

	if len(thumbnail) > 0 {
		mediaInfo.Thumbnail = thumbnail
	}

	if width != nil {
		mediaInfo.Width = width
	}

	if height != nil {
		mediaInfo.Height = height
	}

	if duration != nil {
		mediaInfo.Duration = duration
	}

	return mediaInfo
}

// stringPtr returns a pointer to a string if it's not empty, nil otherwise
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// handleQRCode processes QR code events
func (w *WhatsAppClient) handleQRCode(evt *events.QR) {
	qrEvent := QRCodeEvent{
		Code: evt.Codes[0],
	}
	w.safeSendEvent(EventQRCode, qrEvent)
}

// handleConnected processes connection events
func (w *WhatsAppClient) handleConnected() {
	w.mu.Lock()
	w.connected = true
	w.mu.Unlock()

	statusEvent := ConnectionStatusEvent{
		Status: int(StatusConnected),
		Reason: "Successfully connected to WhatsApp",
	}
	w.safeSendEvent(EventConnectionStatus, statusEvent)
}

// handleDisconnected processes disconnection events
func (w *WhatsAppClient) handleDisconnected() {
	w.mu.Lock()
	w.connected = false
	w.mu.Unlock()

	statusEvent := ConnectionStatusEvent{
		Status: int(StatusDisconnected),
		Reason: "Disconnected from WhatsApp",
	}
	w.safeSendEvent(EventConnectionStatus, statusEvent)
}

// handleLoggedOut processes logout events
func (w *WhatsAppClient) handleLoggedOut(evt *events.LoggedOut) {
	w.mu.Lock()
	w.connected = false
	w.mu.Unlock()

	reason := fmt.Sprintf("Logged out: %v", evt.Reason)

	statusEvent := ConnectionStatusEvent{
		Status: int(StatusLoggedOut),
		Reason: reason,
	}
	w.safeSendEvent(EventConnectionStatus, statusEvent)
}

// Connect starts the WhatsApp connection
func (w *WhatsAppClient) Connect() error {
	if w.client.Store.ID == nil {
		// Not logged in, need QR code
		qrChan, err := w.client.GetQRChannel(w.ctx)
		if err != nil {
			return fmt.Errorf("failed to get QR channel: %w", err)
		}

		// Connect in background
		go func() {
			err := w.client.Connect()
			if err != nil {
				errorEvent := ErrorEvent{
					Code:    int(ErrorInternal),
					Message: fmt.Sprintf("Connection failed: %v", err),
				}
				w.safeSendEvent(EventError, errorEvent)
			}
		}()

		// Handle QR codes
		go func() {
			for evt := range qrChan {
				if evt.Event == "code" {
					w.handleQRCode(&events.QR{Codes: []string{evt.Code}})
				}
			}
		}()
	} else {
		// Already logged in, just connect
		return w.client.Connect()
	}

	return nil
}

// Disconnect closes the WhatsApp connection
func (w *WhatsAppClient) Disconnect() {
	w.logger.Debug("Disconnect: Starting disconnect process")

	w.mu.Lock()
	if w.shutdown {
		w.mu.Unlock()
		w.logger.Debug("Disconnect: Already shutdown, returning")
		return
	}
	w.shutdown = true
	w.connected = false
	w.mu.Unlock()

	w.logger.Debug("Disconnect: Set shutdown flag, disconnecting client")

	// Give a small delay to allow any pending callbacks to complete
	// This prevents race conditions during shutdown
	if w.client != nil {
		w.logger.Debug("Disconnect: Calling client.Disconnect()")
		w.client.Disconnect()
		w.logger.Debug("Disconnect: Client disconnected")
	}

	if w.cancel != nil {
		w.logger.Debug("Disconnect: Cancelling context")
		w.cancel()
		w.logger.Debug("Disconnect: Context cancelled")
	}

	w.logger.Debug("Disconnect: Disconnect process completed")
}

// SendMessage sends a message with enhanced content (JSON format)
func (w *WhatsAppClient) SendMessage(jid, jsonContent string) error {
	w.logger.Info(fmt.Sprintf("SendMessage called: jid=%s, content=%s", jid, jsonContent))

	w.mu.RLock()
	connected := w.connected
	w.mu.RUnlock()

	if !connected {
		w.logger.Error("SendMessage failed: client not connected")
		return fmt.Errorf("client not connected")
	}

	// Parse JID
	targetJID, err := types.ParseJID(jid)
	if err != nil {
		return fmt.Errorf("invalid JID: %w", err)
	}

	// Try to parse as enhanced message content
	var messageContent struct {
		Type string `json:"type"`
		// For Text messages, fields are at the top level
		Text             string   `json:"text,omitempty"`
		QuotedMessageID  *string  `json:"quoted_message_id,omitempty"`
		MentionedNumbers []string `json:"mentioned_numbers,omitempty"`
		Image            struct {
			SourceType string `json:"source_type"`
			Source     struct {
				Path string `json:"path,omitempty"`
				URL  string `json:"url,omitempty"`
				Data string `json:"data,omitempty"`
			} `json:"source"`
			Caption         *string `json:"caption,omitempty"`
			QuotedMessageID *string `json:"quoted_message_id,omitempty"`
			MimeType        *string `json:"mime_type,omitempty"`
		} `json:",omitempty"`
		Video struct {
			SourceType string `json:"source_type"`
			Source     struct {
				Path string `json:"path,omitempty"`
				URL  string `json:"url,omitempty"`
				Data string `json:"data,omitempty"`
			} `json:"source"`
			Caption         *string `json:"caption,omitempty"`
			QuotedMessageID *string `json:"quoted_message_id,omitempty"`
			MimeType        *string `json:"mime_type,omitempty"`
			Duration        *uint32 `json:"duration,omitempty"`
		} `json:",omitempty"`
		Audio struct {
			SourceType string `json:"source_type"`
			Source     struct {
				Path string `json:"path,omitempty"`
				URL  string `json:"url,omitempty"`
				Data string `json:"data,omitempty"`
			} `json:"source"`
			QuotedMessageID *string `json:"quoted_message_id,omitempty"`
			MimeType        *string `json:"mime_type,omitempty"`
			Duration        *uint32 `json:"duration,omitempty"`
			IsVoice         bool    `json:"is_voice"`
		} `json:",omitempty"`
		Document struct {
			SourceType string `json:"source_type"`
			Source     struct {
				Path string `json:"path,omitempty"`
				URL  string `json:"url,omitempty"`
				Data string `json:"data,omitempty"`
			} `json:"source"`
			Caption         *string `json:"caption,omitempty"`
			QuotedMessageID *string `json:"quoted_message_id,omitempty"`
			MimeType        *string `json:"mime_type,omitempty"`
			Filename        *string `json:"filename,omitempty"`
		} `json:",omitempty"`
		Sticker struct {
			SourceType string `json:"source_type"`
			Source     struct {
				Path string `json:"path,omitempty"`
				URL  string `json:"url,omitempty"`
				Data string `json:"data,omitempty"`
			} `json:"source"`
			MimeType *string `json:"mime_type,omitempty"`
		} `json:",omitempty"`
	}

	// Try to parse as JSON first
	if err := json.Unmarshal([]byte(jsonContent), &messageContent); err != nil {
		w.logger.Info(fmt.Sprintf("JSON parsing failed, treating as plain text: %v", err))
		// If JSON parsing fails, treat as plain text message
		// Create a simple text message structure
		message := &waE2E.Message{
			Conversation: &jsonContent,
		}

		// Send the message
		_, err := w.client.SendMessage(context.Background(), targetJID, message)
		if err != nil {
			w.logger.Error(fmt.Sprintf("Failed to send plain text message: %v", err))
			return fmt.Errorf("failed to send message: %w", err)
		}

		w.logger.Info("Plain text message sent successfully")
		return nil
	}

	w.logger.Info(fmt.Sprintf("JSON parsing successful, message type: %s", messageContent.Type))

	var message *waE2E.Message

	switch messageContent.Type {
	case "Text":
		message = &waE2E.Message{
			Conversation: &messageContent.Text,
		}

		// Handle mentions if present
		if len(messageContent.MentionedNumbers) > 0 {
			extendedText := &waE2E.ExtendedTextMessage{
				Text: &messageContent.Text,
			}

			// Convert mentioned numbers to JIDs
			var mentionedJIDs []string
			for _, number := range messageContent.MentionedNumbers {
				if !strings.Contains(number, "@") {
					number = number + "@s.whatsapp.net"
				}
				mentionedJIDs = append(mentionedJIDs, number)
			}

			// Add context info for mentions
			if len(mentionedJIDs) > 0 {
				contextInfo := &waE2E.ContextInfo{}
				contextInfo.MentionedJID = append(contextInfo.MentionedJID, mentionedJIDs...)
				extendedText.ContextInfo = contextInfo
			}

			message = &waE2E.Message{
				ExtendedTextMessage: extendedText,
			}
		}

	case "Image":
		// Handle image messages
		if messageContent.Image.SourceType == "base64" && messageContent.Image.Source.Data != "" {
			// Decode base64 image data
			imageData, err := base64.StdEncoding.DecodeString(messageContent.Image.Source.Data)
			if err != nil {
				return fmt.Errorf("failed to decode base64 image: %w", err)
			}

			// Upload image to WhatsApp servers
			uploaded, err := w.client.Upload(w.ctx, imageData, whatsmeow.MediaImage)
			if err != nil {
				return fmt.Errorf("failed to upload image: %w", err)
			}

			// Determine MIME type
			mimeType := "image/jpeg" // Default
			if messageContent.Image.MimeType != nil && *messageContent.Image.MimeType != "" {
				mimeType = *messageContent.Image.MimeType
			} else {
				// Try to detect from data
				if len(imageData) >= 8 {
					if imageData[0] == 0x89 && imageData[1] == 0x50 && imageData[2] == 0x4E && imageData[3] == 0x47 {
						mimeType = "image/png"
					} else if imageData[0] == 0xFF && imageData[1] == 0xD8 && imageData[2] == 0xFF {
						mimeType = "image/jpeg"
					} else if len(imageData) >= 12 && string(imageData[8:12]) == "WEBP" {
						mimeType = "image/webp"
					}
				}
			}

			// Create image message
			imageMsg := &waE2E.ImageMessage{
				URL:           &uploaded.URL,
				DirectPath:    &uploaded.DirectPath,
				MediaKey:      uploaded.MediaKey,
				Mimetype:      &mimeType,
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    &uploaded.FileLength,
			}

			// Add caption if provided
			if messageContent.Image.Caption != nil && *messageContent.Image.Caption != "" {
				imageMsg.Caption = messageContent.Image.Caption
			}

			message = &waE2E.Message{
				ImageMessage: imageMsg,
			}
		} else {
			return fmt.Errorf("unsupported image source type: %s", messageContent.Image.SourceType)
		}

	case "Video":
		// Handle video messages
		if messageContent.Video.SourceType == "base64" && messageContent.Video.Source.Data != "" {
			// Decode base64 video data
			videoData, err := base64.StdEncoding.DecodeString(messageContent.Video.Source.Data)
			if err != nil {
				return fmt.Errorf("failed to decode base64 video: %w", err)
			}

			// Upload video to WhatsApp servers
			uploaded, err := w.client.Upload(w.ctx, videoData, whatsmeow.MediaVideo)
			if err != nil {
				return fmt.Errorf("failed to upload video: %w", err)
			}

			// Determine MIME type
			mimeType := "video/mp4" // Default
			if messageContent.Video.MimeType != nil && *messageContent.Video.MimeType != "" {
				mimeType = *messageContent.Video.MimeType
			}

			// Create video message
			videoMsg := &waE2E.VideoMessage{
				URL:           &uploaded.URL,
				DirectPath:    &uploaded.DirectPath,
				MediaKey:      uploaded.MediaKey,
				Mimetype:      &mimeType,
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    &uploaded.FileLength,
			}

			// Add caption if provided
			if messageContent.Video.Caption != nil && *messageContent.Video.Caption != "" {
				videoMsg.Caption = messageContent.Video.Caption
			}

			// Add duration if provided
			if messageContent.Video.Duration != nil {
				videoMsg.Seconds = messageContent.Video.Duration
			}

			message = &waE2E.Message{
				VideoMessage: videoMsg,
			}
		} else {
			return fmt.Errorf("unsupported video source type: %s", messageContent.Video.SourceType)
		}

	case "Audio":
		// Handle audio messages
		if messageContent.Audio.SourceType == "base64" && messageContent.Audio.Source.Data != "" {
			// Decode base64 audio data
			audioData, err := base64.StdEncoding.DecodeString(messageContent.Audio.Source.Data)
			if err != nil {
				return fmt.Errorf("failed to decode base64 audio: %w", err)
			}

			// Upload audio to WhatsApp servers
			uploaded, err := w.client.Upload(w.ctx, audioData, whatsmeow.MediaAudio)
			if err != nil {
				return fmt.Errorf("failed to upload audio: %w", err)
			}

			// Determine MIME type
			mimeType := "audio/mpeg" // Default
			if messageContent.Audio.MimeType != nil && *messageContent.Audio.MimeType != "" {
				mimeType = *messageContent.Audio.MimeType
			}

			// Create audio message
			audioMsg := &waE2E.AudioMessage{
				URL:           &uploaded.URL,
				DirectPath:    &uploaded.DirectPath,
				MediaKey:      uploaded.MediaKey,
				Mimetype:      &mimeType,
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    &uploaded.FileLength,
				PTT:           &messageContent.Audio.IsVoice, // Push-to-talk for voice messages
			}

			// Add duration if provided
			if messageContent.Audio.Duration != nil {
				audioMsg.Seconds = messageContent.Audio.Duration
			}

			message = &waE2E.Message{
				AudioMessage: audioMsg,
			}
		} else {
			return fmt.Errorf("unsupported audio source type: %s", messageContent.Audio.SourceType)
		}

	case "Document":
		// Handle document messages
		if messageContent.Document.SourceType == "base64" && messageContent.Document.Source.Data != "" {
			// Decode base64 document data
			docData, err := base64.StdEncoding.DecodeString(messageContent.Document.Source.Data)
			if err != nil {
				return fmt.Errorf("failed to decode base64 document: %w", err)
			}

			// Upload document to WhatsApp servers
			uploaded, err := w.client.Upload(w.ctx, docData, whatsmeow.MediaDocument)
			if err != nil {
				return fmt.Errorf("failed to upload document: %w", err)
			}

			// Determine MIME type
			mimeType := "application/octet-stream" // Default
			if messageContent.Document.MimeType != nil && *messageContent.Document.MimeType != "" {
				mimeType = *messageContent.Document.MimeType
			}

			// Create document message
			docMsg := &waE2E.DocumentMessage{
				URL:           &uploaded.URL,
				DirectPath:    &uploaded.DirectPath,
				MediaKey:      uploaded.MediaKey,
				Mimetype:      &mimeType,
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    &uploaded.FileLength,
			}

			// Add filename if provided
			if messageContent.Document.Filename != nil && *messageContent.Document.Filename != "" {
				docMsg.FileName = messageContent.Document.Filename
			}

			// Add caption if provided
			if messageContent.Document.Caption != nil && *messageContent.Document.Caption != "" {
				docMsg.Caption = messageContent.Document.Caption
			}

			message = &waE2E.Message{
				DocumentMessage: docMsg,
			}
		} else {
			return fmt.Errorf("unsupported document source type: %s", messageContent.Document.SourceType)
		}

	case "Sticker":
		// Handle sticker messages
		if messageContent.Sticker.SourceType == "base64" && messageContent.Sticker.Source.Data != "" {
			// Decode base64 sticker data
			stickerData, err := base64.StdEncoding.DecodeString(messageContent.Sticker.Source.Data)
			if err != nil {
				return fmt.Errorf("failed to decode base64 sticker: %w", err)
			}

			// Upload sticker to WhatsApp servers
			uploaded, err := w.client.Upload(w.ctx, stickerData, whatsmeow.MediaImage) // Stickers use image media type
			if err != nil {
				return fmt.Errorf("failed to upload sticker: %w", err)
			}

			// Determine MIME type
			mimeType := "image/webp" // Default for stickers
			if messageContent.Sticker.MimeType != nil && *messageContent.Sticker.MimeType != "" {
				mimeType = *messageContent.Sticker.MimeType
			}

			// Create sticker message
			stickerMsg := &waE2E.StickerMessage{
				URL:           &uploaded.URL,
				DirectPath:    &uploaded.DirectPath,
				MediaKey:      uploaded.MediaKey,
				Mimetype:      &mimeType,
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    &uploaded.FileLength,
			}

			message = &waE2E.Message{
				StickerMessage: stickerMsg,
			}
		} else {
			return fmt.Errorf("unsupported sticker source type: %s", messageContent.Sticker.SourceType)
		}

	default:
		return fmt.Errorf("unsupported message type: %s", messageContent.Type)
	}

	// Send message
	w.logger.Info("Attempting to send message via whatsmeow client...")
	_, err = w.client.SendMessage(w.ctx, targetJID, message)
	if err != nil {
		w.logger.Error(fmt.Sprintf("Failed to send message via whatsmeow: %v", err))
		return err
	}

	w.logger.Info("Message sent successfully via whatsmeow")
	return nil
}

// IsConnected returns the connection status
func (w *WhatsAppClient) IsConnected() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.connected
}

// Cleanup performs cleanup operations
func (w *WhatsAppClient) Cleanup() {
	w.logger.Debug("Cleanup: Starting cleanup process")

	w.mu.Lock()
	if w.shutdown {
		w.mu.Unlock()
		w.logger.Debug("Cleanup: Already shutdown, returning")
		return
	}
	w.shutdown = true
	w.connected = false
	w.mu.Unlock()

	w.logger.Debug("Cleanup: Set shutdown flag")

	// Disconnect first
	if w.client != nil {
		w.logger.Debug("Cleanup: Disconnecting client")
		w.client.Disconnect()
		w.logger.Debug("Cleanup: Client disconnected")
	}

	// Cancel context
	if w.cancel != nil {
		w.logger.Debug("Cleanup: Cancelling context")
		w.cancel()
		w.logger.Debug("Cleanup: Context cancelled")
	}

	// Wait for all active callbacks to complete with timeout
	w.logger.Debug("Cleanup: Waiting for active callbacks to complete")
	done := make(chan struct{})
	go func() {
		w.activeCallbacks.Wait()
		close(done)
	}()

	select {
	case <-done:
		w.logger.Debug("Cleanup: All callbacks completed")
	case <-time.After(100 * time.Millisecond):
		w.logger.Debug("Cleanup: Timeout waiting for callbacks, proceeding")
	}

	// Clear callback to prevent further invocations
	w.mu.Lock()
	w.logger.Debug("Cleanup: Clearing callback")
	w.callback = nil
	w.mu.Unlock()

	// Close database container last
	if w.container != nil {
		w.logger.Debug("Cleanup: Closing database container")
		w.container.Close()
		w.container = nil
		w.logger.Debug("Cleanup: Database container closed")
	}

	// Signal shutdown completion
	close(w.shutdownDone)
	w.logger.Debug("Cleanup: Cleanup process completed")
}
