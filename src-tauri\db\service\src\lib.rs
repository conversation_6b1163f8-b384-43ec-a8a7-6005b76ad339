//! Database service layer for the WhatsApp Sender Pro application.
//!
//! This crate provides high-level database services and business logic
//! for managing application data. It acts as an abstraction layer between
//! the application logic and the database entities.

#![deny(missing_docs)]

use std::sync::Arc;

use db_migration::run_migrations;
use derive_getters::Getters;
use sea_orm::DatabaseConnection;
use typed_builder::TypedBuilder;

mod db_init;
mod error;

pub use db_init::{
    DatabaseConfig, check_collation_health, ensure_database_exists, fix_collation_mismatches,
};
pub use error::ServiceError;

/// Service manager containing all application services
#[derive(Getters, TypedBuilder)]
pub struct ServiceManager {
    #[builder(setter(into))]
    db: Arc<DatabaseConnection>,
}

impl ServiceManager {
    /// Creates a new instance of ServiceManager with all required services.
    ///
    /// This method initializes all service implementations with a shared database connection.
    /// The connection is wrapped in an Arc to allow safe sharing across services.
    ///
    /// # Arguments
    /// * `db` - Thread-safe reference to the database connection
    ///
    /// # Returns
    /// * `Result<Self, ServiceError>` - New ServiceManager instance or error if initialization fails
    ///
    /// # Errors
    /// Returns `ServiceError` if any service initialization fails
    async fn try_new(db: Arc<DatabaseConnection>) -> Result<Self, ServiceError> {
        Ok(Self::builder().db(db.clone()).build())
    }

    /// Creates a new instance of ServiceManager with all required services and wraps it in an Arc.
    ///
    /// # Arguments
    /// * `db` - Database connection
    ///
    /// # Returns
    /// * `Result<Arc<Self>, ServiceError>` - New ServiceManager instance wrapped in Arc or error
    pub async fn new(db: DatabaseConnection) -> Result<Arc<Self>, ServiceError> {
        let db = Arc::new(db);
        let service_manager = Self::try_new(db).await?;
        Ok(Arc::new(service_manager))
    }
}

/// Sets up all services for the application
///
/// This function ensures the database exists (creating it if necessary),
/// establishes a connection, runs migrations, and initializes the service manager.
///
/// # Arguments
/// * `url` - The PostgreSQL database URL
///
/// # Returns
/// * `Result<ServiceManager, ServiceError>` - Initialized service manager or error
///
/// # Errors
/// Returns `ServiceError` if:
/// - Database initialization fails
/// - Database connection fails
/// - Migration execution fails
/// - Service manager initialization fails
pub async fn setup_services(url: &str) -> Result<ServiceManager, ServiceError> {
    // Ensure database exists, creating it if necessary
    let db = ensure_database_exists(url).await?;

    // Run migrations with error handling
    match run_migrations(&db).await {
        Ok(_) => {
            tracing::info!("Migrations completed successfully");
        }
        Err(e) => {
            tracing::warn!(
                "Migration error (this might be expected if table already exists): {:?}",
                e
            );
            // Continue anyway - the table might already exist with the correct schema
            // or we might be able to work with the existing schema
        }
    }

    ServiceManager::try_new(Arc::new(db)).await
}
