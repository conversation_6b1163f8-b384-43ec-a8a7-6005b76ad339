import { Badge } from '@/components/ui/badge';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  CheckCircle,
  Clock,
  RefreshCw,
  Wifi,
  WifiOff,
  XCircle,
} from 'lucide-react';

interface DataStatusIndicatorProps {
  status?: 'fresh' | 'stale' | 'error' | 'loading';
  lastUpdated?: Date;
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  // Additional props for empty state or custom indicators
  icon?: React.ComponentType<any>;
  title?: string;
  description?: string;
}

export function DataStatusIndicator({
  status,
  lastUpdated,
  className,
  showLabel = false,
  size = 'sm',
  icon: CustomIcon,
  title,
  description,
}: DataStatusIndicatorProps) {
  const getStatusConfig = () => {
    // If custom content is provided, use that instead of status-based content
    if (CustomIcon && title) {
      return {
        icon: CustomIcon,
        color: 'text-gray-600',
        bgColor: 'bg-gray-100',
        borderColor: 'border-gray-200',
        label: title,
        description: description || '',
        badgeVariant: 'default' as const,
        badgeClass: 'bg-gray-50 text-gray-700 border-gray-200',
      };
    }

    // Otherwise, use status-based content
    switch (status) {
      case 'fresh':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          borderColor: 'border-green-200',
          label: 'Data is current',
          badgeVariant: 'default' as const,
          badgeClass: 'bg-green-50 text-green-700 border-green-200',
        };
      case 'stale':
        return {
          icon: Clock,
          color: 'text-amber-600',
          bgColor: 'bg-amber-100',
          borderColor: 'border-amber-200',
          label: 'Data may be outdated',
          badgeVariant: 'outline' as const,
          badgeClass: 'bg-amber-50 text-amber-700 border-amber-200',
        };
      case 'error':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          borderColor: 'border-red-200',
          label: 'Failed to load data',
          badgeVariant: 'destructive' as const,
          badgeClass: 'bg-red-50 text-red-700 border-red-200',
        };
      case 'loading':
        return {
          icon: RefreshCw,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          borderColor: 'border-blue-200',
          label: 'Loading data...',
          badgeVariant: 'outline' as const,
          badgeClass: 'bg-blue-50 text-blue-700 border-blue-200',
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200',
          label: 'Status unknown',
          badgeVariant: 'default' as const,
          badgeClass: 'bg-gray-50 text-gray-700 border-gray-200',
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          dot: 'w-2 h-2',
          icon: 'w-3 h-3',
          badge: 'text-xs px-1.5 py-0.5',
        };
      case 'md':
        return {
          dot: 'w-3 h-3',
          icon: 'w-4 h-4',
          badge: 'text-sm px-2 py-1',
        };
      case 'lg':
        return {
          dot: 'w-4 h-4',
          icon: 'w-5 h-5',
          badge: 'text-base px-3 py-1.5',
        };
    }
  };

  const config = getStatusConfig();
  const sizeClasses = getSizeClasses();
  const Icon = config.icon;

  const formatLastUpdated = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60),
    );

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const tooltipContent = (
    <div className="text-center">
      <p className="font-medium">{config.label}</p>
      {lastUpdated && (
        <p className="mt-1 text-xs opacity-75">
          Last updated: {formatLastUpdated(lastUpdated)}
        </p>
      )}
    </div>
  );

  if (showLabel) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant={config.badgeVariant}
              className={cn(
                'flex items-center space-x-1.5 font-medium',
                config.badgeClass,
                sizeClasses.badge,
                className,
              )}
            >
              <Icon
                className={cn(
                  sizeClasses.icon,
                  config.color,
                  status === 'loading' && 'animate-spin',
                )}
              />
              <span>{config.label}</span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>{tooltipContent}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'flex items-center justify-center rounded-full',
              config.bgColor,
              config.borderColor,
              'border',
              sizeClasses.dot,
              className,
            )}
          >
            <Icon
              className={cn(
                sizeClasses.icon,
                config.color,
                status === 'loading' && 'animate-spin',
              )}
            />
          </div>
        </TooltipTrigger>
        <TooltipContent>{tooltipContent}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

interface ConnectionStatusProps {
  isOnline: boolean;
  className?: string;
  showLabel?: boolean;
}

export function ConnectionStatus({
  isOnline,
  className,
  showLabel = false,
}: ConnectionStatusProps) {
  const Icon = isOnline ? Wifi : WifiOff;
  const color = isOnline ? 'text-green-600' : 'text-red-600';
  const bgColor = isOnline ? 'bg-green-100' : 'bg-red-100';
  const label = isOnline ? 'Online' : 'Offline';

  if (showLabel) {
    return (
      <Badge
        variant={isOnline ? 'default' : 'destructive'}
        className={cn('flex items-center space-x-1', className)}
      >
        <Icon className="h-3 w-3" />
        <span>{label}</span>
      </Badge>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'flex h-2 w-2 items-center justify-center rounded-full',
              bgColor,
              className,
            )}
          >
            <Icon className={cn('h-3 w-3', color)} />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Connection: {label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

interface RealTimeIndicatorProps {
  isRealTime: boolean;
  updateInterval?: number;
  className?: string;
}

export function RealTimeIndicator({
  isRealTime,
  updateInterval = 30,
  className,
}: RealTimeIndicatorProps) {
  if (!isRealTime) return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'flex items-center space-x-1 rounded-full border border-green-200 bg-green-50 px-2 py-1 text-xs text-green-600',
              className,
            )}
          >
            <div className="h-1.5 w-1.5 animate-pulse rounded-full bg-green-500" />
            <span className="font-medium">Live</span>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Real-time data (updates every {updateInterval}s)</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
