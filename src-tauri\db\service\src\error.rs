//! Error types for the service layer

use sea_orm::DbErr;
use thiserror::Error;

/// Custom error type for the service layer
#[derive(E<PERSON><PERSON>, Debug)]
pub enum ServiceError {
    /// Database connection error
    #[error("Database error: {0}")]
    Database(#[from] DbErr),

    /// Database initialization error
    #[error("Database initialization error: {0}")]
    DatabaseInit(String),

    /// I/O error
    #[error("Io error: {0}")]
    Io(#[from] std::io::Error),

    /// JSON error
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),
}
