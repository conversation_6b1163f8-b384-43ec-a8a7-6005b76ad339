{"$message_type":"diagnostic","message":"initializer for `thread_local` value can be made `const`","code":{"code":"clippy::missing_const_for_thread_local","explanation":null},"level":"warning","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\connection_manager.rs","byte_start":22027,"byte_end":22049,"line_start":617,"line_end":617,"column_start":43,"column_end":65,"is_primary":true,"text":[{"text":"        static RNG: Cell<Wrapping<u64>> = Cell::new(Wrapping(1));","highlight_start":43,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#missing_const_for_thread_local","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::missing_const_for_thread_local)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"replace with","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\connection_manager.rs","byte_start":22027,"byte_end":22049,"line_start":617,"line_end":617,"column_start":43,"column_end":65,"is_primary":true,"text":[{"text":"        static RNG: Cell<Wrapping<u64>> = Cell::new(Wrapping(1));","highlight_start":43,"highlight_end":65}],"label":null,"suggested_replacement":"const { Cell::new(Wrapping(1)) }","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: initializer for `thread_local` value can be made `const`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\connection_manager.rs:617:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m617\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        static RNG: Cell<Wrapping<u64>> = Cell::new(Wrapping(1));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: replace with: `const { Cell::new(Wrapping(1)) }`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#missing_const_for_thread_local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::missing_const_for_thread_local)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"parameter is only used in recursion","code":{"code":"clippy::only_used_in_recursion","explanation":null},"level":"warning","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs","byte_start":7809,"byte_end":7813,"line_start":252,"line_end":252,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"    fn matches_filter(&self, event: &ProcessedEvent, filter: &EventFilter) -> bool {","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"parameter used here","code":null,"level":"note","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs","byte_start":8404,"byte_end":8408,"line_start":263,"line_end":263,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"                filters.iter().all(|f| self.matches_filter(event, f))","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs","byte_start":8533,"byte_end":8537,"line_start":266,"line_end":266,"column_start":40,"column_end":44,"is_primary":true,"text":[{"text":"                filters.iter().any(|f| self.matches_filter(event, f))","highlight_start":40,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs","byte_start":8621,"byte_end":8625,"line_start":268,"line_end":268,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"            EventFilter::Not(filter) => !self.matches_filter(event, filter),","highlight_start":42,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#only_used_in_recursion","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::only_used_in_recursion)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: parameter is only used in recursion\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs:252:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn matches_filter(&self, event: &ProcessedEvent, filter: &EventFilter) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: parameter used here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs:263:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m263\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                filters.iter().all(|f| self.matches_filter(event, f))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                filters.iter().any(|f| self.matches_filter(event, f))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m267\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            }\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            EventFilter::Not(filter) => !self.matches_filter(event, filter),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#only_used_in_recursion\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::only_used_in_recursion)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"consider using `sort_by_key`","code":{"code":"clippy::unnecessary_sort_by","explanation":null},"level":"warning","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs","byte_start":22842,"byte_end":22900,"line_start":630,"line_end":630,"column_start":9,"column_end":67,"is_primary":true,"text":[{"text":"        processors.sort_by(|a, b| b.priority().cmp(&a.priority()));","highlight_start":9,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_sort_by","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::unnecessary_sort_by)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs","byte_start":22842,"byte_end":22900,"line_start":630,"line_end":630,"column_start":9,"column_end":67,"is_primary":true,"text":[{"text":"        processors.sort_by(|a, b| b.priority().cmp(&a.priority()));","highlight_start":9,"highlight_end":67}],"label":null,"suggested_replacement":"processors.sort_by_key(|b| std::cmp::Reverse(b.priority()))","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: consider using `sort_by_key`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs:630:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m630\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        processors.sort_by(|a, b| b.priority().cmp(&a.priority()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `processors.sort_by_key(|b| std::cmp::Reverse(b.priority()))`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_sort_by\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::unnecessary_sort_by)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type `core::event_aggregator::uuid::Uuid` implements inherent method `to_string(&self) -> String` which shadows the implementation of `Display`","code":{"code":"clippy::inherent_to_string_shadow_display","explanation":null},"level":"error","spans":[{"file_name":"crates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs","byte_start":27628,"byte_end":28098,"line_start":768,"line_end":777,"column_start":9,"column_end":10,"is_primary":true,"text":[{"text":"        pub fn to_string(&self) -> String {","highlight_start":9,"highlight_end":44},{"text":"            format!(","highlight_start":1,"highlight_end":21},{"text":"                \"{:02x}{:02x}{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}{:02x}{:02x}{:02x}{:02x}\",","highlight_start":1,"highlight_end":120},{"text":"                self.0[0], self.0[1], self.0[2], self.0[3],","highlight_start":1,"highlight_end":60},{"text":"                self.0[4], self.0[5],","highlight_start":1,"highlight_end":38},{"text":"                self.0[6], self.0[7],","highlight_start":1,"highlight_end":38},{"text":"                self.0[8], self.0[9],","highlight_start":1,"highlight_end":38},{"text":"                self.0[10], self.0[11], self.0[12], self.0[13], self.0[14], self.0[15]","highlight_start":1,"highlight_end":87},{"text":"            )","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the inherent method from type `core::event_aggregator::uuid::Uuid`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#inherent_to_string_shadow_display","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[deny(clippy::inherent_to_string_shadow_display)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type `core::event_aggregator::uuid::Uuid` implements inherent method `to_string(&self) -> String` which shadows the implementation of `Display`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\whatsmeow_ffi\\src\\core\\event_aggregator.rs:768:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m768\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        pub fn to_string(&self) -> String {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m769\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            format!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m770\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"{:02x}{:02x}{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}{:02x}{:02x}{:02x}{:02x}\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m771\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                self.0[0], self.0[1], self.0[2], self.0[3],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m777\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: remove the inherent method from type `core::event_aggregator::uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#inherent_to_string_shadow_display\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[deny(clippy::inherent_to_string_shadow_display)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 3 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 3 warnings emitted\u001b[0m\n\n"}
