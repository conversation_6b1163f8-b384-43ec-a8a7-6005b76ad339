use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
// Arc and RwLock are used in the ContactManager implementation
use tokio::sync::mpsc;

use crate::core::errors::{Result, WhatsAppError};

/// Represents the presence status of a contact
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum PresenceStatus {
    /// Contact is currently online
    Online,
    /// Contact was last seen at a specific time
    LastSeen(DateTime<Utc>),
    /// Contact's last seen time is hidden
    Hidden,
    /// Contact's presence status is unknown
    #[default]
    Unknown,
}

/// Represents a WhatsApp contact with comprehensive metadata
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Contact {
    /// WhatsApp JID (unique identifier)
    pub jid: String,
    /// Display name of the contact
    pub name: Option<String>,
    /// Phone number in international format
    pub phone_number: String,
    /// Whether this is a business account
    pub is_business: bool,
    /// Last seen timestamp
    pub last_seen: Option<DateTime<Utc>>,
    /// Current presence status
    pub presence: PresenceStatus,
    /// Profile picture URL (if available)
    pub profile_picture_url: Option<String>,
    /// Contact's status message
    pub status_message: Option<String>,
    /// Whether the contact is blocked
    pub is_blocked: bool,
    /// Custom metadata for the contact
    pub metadata: HashMap<String, String>,
    /// Last time contact information was updated
    pub last_updated: DateTime<Utc>,
}

impl Contact {
    /// Create a new contact with minimal information
    pub fn new(jid: String, phone_number: String) -> Self {
        Self {
            jid,
            name: None,
            phone_number,
            is_business: false,
            last_seen: None,
            presence: PresenceStatus::Unknown,
            profile_picture_url: None,
            status_message: None,
            is_blocked: false,
            metadata: HashMap::new(),
            last_updated: Utc::now(),
        }
    }

    /// Update the contact's presence status
    pub fn update_presence(&mut self, presence: PresenceStatus) {
        self.presence = presence;
        self.last_updated = Utc::now();
    }

    /// Update the contact's name
    pub fn update_name(&mut self, name: Option<String>) {
        self.name = name;
        self.last_updated = Utc::now();
    }

    /// Check if the contact is currently online
    pub fn is_online(&self) -> bool {
        matches!(self.presence, PresenceStatus::Online)
    }

    /// Get the display name or phone number if name is not available
    pub fn display_name(&self) -> &str {
        self.name.as_deref().unwrap_or(&self.phone_number)
    }
}

/// Represents a group participant with their role and metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GroupParticipant {
    /// WhatsApp JID of the participant
    pub jid: String,
    /// Display name of the participant
    pub name: Option<String>,
    /// Phone number of the participant
    pub phone_number: String,
    /// Whether this participant is an admin
    pub is_admin: bool,
    /// Whether this participant is a super admin
    pub is_super_admin: bool,
    /// When the participant joined the group
    pub joined_at: DateTime<Utc>,
    /// Who added this participant to the group
    pub added_by: Option<String>,
}

impl GroupParticipant {
    /// Create a new group participant
    pub fn new(jid: String, phone_number: String) -> Self {
        Self {
            jid,
            name: None,
            phone_number,
            is_admin: false,
            is_super_admin: false,
            joined_at: Utc::now(),
            added_by: None,
        }
    }

    /// Check if the participant has admin privileges
    pub fn has_admin_privileges(&self) -> bool {
        self.is_admin || self.is_super_admin
    }

    /// Get the display name or phone number if name is not available
    pub fn display_name(&self) -> &str {
        self.name.as_deref().unwrap_or(&self.phone_number)
    }
}

/// Represents a WhatsApp group with comprehensive metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Group {
    /// WhatsApp JID of the group
    pub jid: String,
    /// Group name
    pub name: String,
    /// Group description
    pub description: Option<String>,
    /// List of group participants
    pub participants: Vec<GroupParticipant>,
    /// JIDs of group admins (for quick lookup)
    pub admins: Vec<String>,
    /// When the group was created
    pub created_at: DateTime<Utc>,
    /// Who created the group
    pub created_by: Option<String>,
    /// Group profile picture URL (if available)
    pub profile_picture_url: Option<String>,
    /// Group invite link (if available)
    pub invite_link: Option<String>,
    /// Whether the group is announcement-only
    pub is_announcement: bool,
    /// Whether the group is locked (only admins can change settings)
    pub is_locked: bool,
    /// Custom metadata for the group
    pub metadata: HashMap<String, String>,
    /// Last time group information was updated
    pub last_updated: DateTime<Utc>,
}

impl Group {
    /// Create a new group with minimal information
    pub fn new(jid: String, name: String) -> Self {
        Self {
            jid,
            name,
            description: None,
            participants: Vec::new(),
            admins: Vec::new(),
            created_at: Utc::now(),
            created_by: None,
            profile_picture_url: None,
            invite_link: None,
            is_announcement: false,
            is_locked: false,
            metadata: HashMap::new(),
            last_updated: Utc::now(),
        }
    }

    /// Add a participant to the group
    pub fn add_participant(&mut self, participant: GroupParticipant) {
        // Remove existing participant if present
        self.participants.retain(|p| p.jid != participant.jid);

        // Add to admins list if participant is admin
        if participant.has_admin_privileges() && !self.admins.contains(&participant.jid) {
            self.admins.push(participant.jid.clone());
        }

        self.participants.push(participant);
        self.last_updated = Utc::now();
    }

    /// Remove a participant from the group
    pub fn remove_participant(&mut self, jid: &str) -> bool {
        let initial_len = self.participants.len();
        self.participants.retain(|p| p.jid != jid);
        self.admins.retain(|admin_jid| admin_jid != jid);

        if self.participants.len() != initial_len {
            self.last_updated = Utc::now();
            true
        } else {
            false
        }
    }

    /// Get a participant by JID
    pub fn get_participant(&self, jid: &str) -> Option<&GroupParticipant> {
        self.participants.iter().find(|p| p.jid == jid)
    }

    /// Get a mutable reference to a participant by JID
    pub fn get_participant_mut(&mut self, jid: &str) -> Option<&mut GroupParticipant> {
        self.participants.iter_mut().find(|p| p.jid == jid)
    }

    /// Check if a JID is an admin of the group
    pub fn is_admin(&self, jid: &str) -> bool {
        self.admins.contains(&jid.to_string())
    }

    /// Promote a participant to admin
    pub fn promote_to_admin(&mut self, jid: &str) -> Result<()> {
        if let Some(participant) = self.get_participant_mut(jid) {
            participant.is_admin = true;
            if !self.admins.contains(&jid.to_string()) {
                self.admins.push(jid.to_string());
            }
            self.last_updated = Utc::now();
            Ok(())
        } else {
            Err(WhatsAppError::NotFound {
                resource: "participant".to_string(),
                id: jid.to_string(),
            })
        }
    }

    /// Demote an admin to regular participant
    pub fn demote_from_admin(&mut self, jid: &str) -> Result<()> {
        if let Some(participant) = self.get_participant_mut(jid) {
            participant.is_admin = false;
            participant.is_super_admin = false;
            self.admins.retain(|admin_jid| admin_jid != jid);
            self.last_updated = Utc::now();
            Ok(())
        } else {
            Err(WhatsAppError::NotFound {
                resource: "participant".to_string(),
                id: jid.to_string(),
            })
        }
    }

    /// Get the number of participants
    pub fn participant_count(&self) -> usize {
        self.participants.len()
    }

    /// Get the number of admins
    pub fn admin_count(&self) -> usize {
        self.admins.len()
    }
}

/// Events related to contacts and groups
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContactEvent {
    /// Contact presence status changed
    PresenceUpdate {
        jid: String,
        presence: PresenceStatus,
    },
    /// Contact information updated
    ContactUpdate { contact: Contact },
    /// New contact discovered
    ContactAdded { contact: Contact },
    /// Contact was blocked/unblocked
    ContactBlocked { jid: String, is_blocked: bool },
}

/// Events related to groups
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GroupEvent {
    /// Group information updated
    GroupUpdate { group: Group },
    /// Participant added to group
    ParticipantAdded {
        group_jid: String,
        participant: GroupParticipant,
        added_by: Option<String>,
    },
    /// Participant removed from group
    ParticipantRemoved {
        group_jid: String,
        participant_jid: String,
        removed_by: Option<String>,
    },
    /// Participant promoted to admin
    ParticipantPromoted {
        group_jid: String,
        participant_jid: String,
        promoted_by: Option<String>,
    },
    /// Participant demoted from admin
    ParticipantDemoted {
        group_jid: String,
        participant_jid: String,
        demoted_by: Option<String>,
    },
    /// Group settings changed
    GroupSettingsChanged {
        group_jid: String,
        setting: String,
        value: String,
        changed_by: Option<String>,
    },
}

/// Contact search and filtering criteria
#[derive(Debug, Clone, Default)]
pub struct ContactFilter {
    /// Filter by name (case-insensitive partial match)
    pub name_contains: Option<String>,
    /// Filter by phone number (partial match)
    pub phone_contains: Option<String>,
    /// Filter by business accounts only
    pub business_only: Option<bool>,
    /// Filter by blocked status
    pub blocked_only: Option<bool>,
    /// Filter by presence status
    pub presence_status: Option<PresenceStatus>,
    /// Filter by last seen within duration
    pub last_seen_within: Option<chrono::Duration>,
}

/// Group search and filtering criteria
#[derive(Debug, Clone, Default)]
pub struct GroupFilter {
    /// Filter by group name (case-insensitive partial match)
    pub name_contains: Option<String>,
    /// Filter by description (case-insensitive partial match)
    pub description_contains: Option<String>,
    /// Filter by minimum participant count
    pub min_participants: Option<usize>,
    /// Filter by maximum participant count
    pub max_participants: Option<usize>,
    /// Filter by announcement-only groups
    pub announcement_only: Option<bool>,
    /// Filter by groups where user is admin
    pub user_is_admin: Option<String>,
}

/// Sorting options for contacts
#[derive(Debug, Clone, Copy)]
pub enum ContactSortBy {
    Name,
    PhoneNumber,
    LastSeen,
    LastUpdated,
}

/// Sorting options for groups
#[derive(Debug, Clone, Copy)]
pub enum GroupSortBy {
    Name,
    ParticipantCount,
    CreatedAt,
    LastUpdated,
}

/// Sort direction
#[derive(Debug, Clone, Copy)]
pub enum SortDirection {
    Ascending,
    Descending,
}

/// Contact and group management configuration
#[derive(Debug, Clone)]
pub struct ContactManagerConfig {
    /// Maximum number of contacts to cache
    pub max_cached_contacts: usize,
    /// Maximum number of groups to cache
    pub max_cached_groups: usize,
    /// How often to sync contacts from WhatsApp
    pub sync_interval: chrono::Duration,
    /// How long to cache presence information
    pub presence_cache_duration: chrono::Duration,
    /// Whether to automatically sync contacts on startup
    pub auto_sync_on_startup: bool,
}

impl Default for ContactManagerConfig {
    fn default() -> Self {
        Self {
            max_cached_contacts: 10000,
            max_cached_groups: 1000,
            sync_interval: chrono::Duration::hours(1),
            presence_cache_duration: chrono::Duration::minutes(5),
            auto_sync_on_startup: true,
        }
    }
}

/// Progress information for bulk operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncProgress {
    /// Total number of items to process
    pub total: usize,
    /// Number of items processed so far
    pub processed: usize,
    /// Number of items that failed to process
    pub failed: usize,
    /// Current operation being performed
    pub current_operation: String,
    /// Whether the operation is complete
    pub is_complete: bool,
    /// Any error that occurred during the operation
    pub error: Option<String>,
}

impl SyncProgress {
    /// Create a new sync progress tracker
    pub fn new(total: usize, operation: String) -> Self {
        Self {
            total,
            processed: 0,
            failed: 0,
            current_operation: operation,
            is_complete: false,
            error: None,
        }
    }

    /// Update progress with successful processing
    pub fn increment_processed(&mut self) {
        self.processed += 1;
        self.is_complete = self.processed + self.failed >= self.total;
    }

    /// Update progress with failed processing
    pub fn increment_failed(&mut self, error: Option<String>) {
        self.failed += 1;
        if let Some(err) = error {
            self.error = Some(err);
        }
        self.is_complete = self.processed + self.failed >= self.total;
    }

    /// Get completion percentage (0.0 to 1.0)
    pub fn completion_percentage(&self) -> f64 {
        if self.total == 0 {
            1.0
        } else {
            (self.processed + self.failed) as f64 / self.total as f64
        }
    }
}

/// Trait for contact and group management operations
pub trait ContactManagerPort: Send + Sync {
    /// Synchronize contacts from WhatsApp
    fn sync_contacts(&self) -> mpsc::Receiver<SyncProgress>;

    /// Get all cached contacts
    fn get_contacts(&self) -> Result<Vec<Contact>>;

    /// Get a specific contact by JID
    fn get_contact(&self, jid: &str) -> Result<Option<Contact>>;

    /// Search contacts with filtering and sorting
    fn search_contacts(
        &self,
        filter: &ContactFilter,
        sort_by: ContactSortBy,
        direction: SortDirection,
        limit: Option<usize>,
    ) -> Result<Vec<Contact>>;

    /// Update contact information
    fn update_contact(&self, contact: &Contact) -> Result<()>;

    /// Block or unblock a contact
    fn set_contact_blocked(&self, jid: &str, blocked: bool) -> Result<()>;

    /// Get contact presence status
    fn get_contact_presence(&self, jid: &str) -> Result<PresenceStatus>;

    /// Subscribe to contact presence updates
    fn subscribe_to_presence(&self, jid: &str) -> Result<()>;

    /// Unsubscribe from contact presence updates
    fn unsubscribe_from_presence(&self, jid: &str) -> Result<()>;

    /// Get all cached groups
    fn get_groups(&self) -> Result<Vec<Group>>;

    /// Get a specific group by JID
    fn get_group(&self, jid: &str) -> Result<Option<Group>>;

    /// Search groups with filtering and sorting
    fn search_groups(
        &self,
        filter: &GroupFilter,
        sort_by: GroupSortBy,
        direction: SortDirection,
        limit: Option<usize>,
    ) -> Result<Vec<Group>>;

    /// Create a new group
    fn create_group(&self, name: &str, participants: &[String]) -> Result<Group>;

    /// Update group information
    fn update_group_info(
        &self,
        jid: &str,
        name: Option<&str>,
        description: Option<&str>,
    ) -> Result<()>;

    /// Add participants to a group
    fn add_group_participants(&self, group_jid: &str, participant_jids: &[String]) -> Result<()>;

    /// Remove participants from a group
    fn remove_group_participants(&self, group_jid: &str, participant_jids: &[String])
        -> Result<()>;

    /// Promote participants to admin
    fn promote_group_participants(
        &self,
        group_jid: &str,
        participant_jids: &[String],
    ) -> Result<()>;

    /// Demote participants from admin
    fn demote_group_participants(&self, group_jid: &str, participant_jids: &[String])
        -> Result<()>;

    /// Leave a group
    fn leave_group(&self, group_jid: &str) -> Result<()>;

    /// Get group invite link
    fn get_group_invite_link(&self, group_jid: &str) -> Result<String>;

    /// Revoke group invite link
    fn revoke_group_invite_link(&self, group_jid: &str) -> Result<String>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_contact_creation() {
        let contact = Contact::new(
            "<EMAIL>".to_string(),
            "+1234567890".to_string(),
        );

        assert_eq!(contact.jid, "<EMAIL>");
        assert_eq!(contact.phone_number, "+1234567890");
        assert_eq!(contact.name, None);
        assert!(!contact.is_business);
        assert_eq!(contact.presence, PresenceStatus::Unknown);
        assert!(!contact.is_blocked);
    }

    #[test]
    fn test_contact_presence_update() {
        let mut contact = Contact::new(
            "<EMAIL>".to_string(),
            "+1234567890".to_string(),
        );
        let initial_updated = contact.last_updated;

        // Wait a bit to ensure timestamp difference
        std::thread::sleep(std::time::Duration::from_millis(1));

        contact.update_presence(PresenceStatus::Online);

        assert_eq!(contact.presence, PresenceStatus::Online);
        assert!(contact.is_online());
        assert!(contact.last_updated > initial_updated);
    }

    #[test]
    fn test_contact_display_name() {
        let mut contact = Contact::new(
            "<EMAIL>".to_string(),
            "+1234567890".to_string(),
        );

        // Should return phone number when name is None
        assert_eq!(contact.display_name(), "+1234567890");

        // Should return name when available
        contact.name = Some("John Doe".to_string());
        assert_eq!(contact.display_name(), "John Doe");
    }

    #[test]
    fn test_group_creation() {
        let group = Group::new("<EMAIL>".to_string(), "Test Group".to_string());

        assert_eq!(group.jid, "<EMAIL>");
        assert_eq!(group.name, "Test Group");
        assert_eq!(group.description, None);
        assert!(group.participants.is_empty());
        assert!(group.admins.is_empty());
        assert!(!group.is_announcement);
        assert!(!group.is_locked);
    }

    #[test]
    fn test_group_participant_management() {
        let mut group = Group::new("<EMAIL>".to_string(), "Test Group".to_string());
        let participant = GroupParticipant::new(
            "<EMAIL>".to_string(),
            "+1234567890".to_string(),
        );

        // Add participant
        group.add_participant(participant.clone());
        assert_eq!(group.participant_count(), 1);
        assert!(group.get_participant("<EMAIL>").is_some());

        // Promote to admin
        group.promote_to_admin("<EMAIL>").unwrap();
        assert!(group.is_admin("<EMAIL>"));
        assert_eq!(group.admin_count(), 1);

        // Demote from admin
        group
            .demote_from_admin("<EMAIL>")
            .unwrap();
        assert!(!group.is_admin("<EMAIL>"));
        assert_eq!(group.admin_count(), 0);

        // Remove participant
        assert!(group.remove_participant("<EMAIL>"));
        assert_eq!(group.participant_count(), 0);
        assert!(group.get_participant("<EMAIL>").is_none());
    }

    #[test]
    fn test_group_participant_admin_privileges() {
        let mut participant = GroupParticipant::new(
            "<EMAIL>".to_string(),
            "+1234567890".to_string(),
        );

        assert!(!participant.has_admin_privileges());

        participant.is_admin = true;
        assert!(participant.has_admin_privileges());

        participant.is_admin = false;
        participant.is_super_admin = true;
        assert!(participant.has_admin_privileges());
    }

    #[test]
    fn test_sync_progress() {
        let mut progress = SyncProgress::new(100, "Syncing contacts".to_string());

        assert_eq!(progress.total, 100);
        assert_eq!(progress.processed, 0);
        assert_eq!(progress.failed, 0);
        assert!(!progress.is_complete);
        assert_eq!(progress.completion_percentage(), 0.0);

        // Process some items
        for _ in 0..50 {
            progress.increment_processed();
        }

        assert_eq!(progress.processed, 50);
        assert_eq!(progress.completion_percentage(), 0.5);
        assert!(!progress.is_complete);

        // Fail some items
        for _ in 0..30 {
            progress.increment_failed(None);
        }

        assert_eq!(progress.failed, 30);
        assert_eq!(progress.completion_percentage(), 0.8);
        assert!(!progress.is_complete);

        // Complete remaining items
        for _ in 0..20 {
            progress.increment_processed();
        }

        assert_eq!(progress.processed, 70);
        assert_eq!(progress.completion_percentage(), 1.0);
        assert!(progress.is_complete);
    }
}

// Include comprehensive tests
#[cfg(test)]
mod comprehensive_tests {
    include!("contacts_test.rs");
}
