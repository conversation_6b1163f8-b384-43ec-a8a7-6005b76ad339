//! Unit tests for service state management

use chrono::{Duration, Utc};
use whatsapp_ffi_client::{ConnectionStatus, LogLevel};

use crate::whatsapp::state::{ServiceConfig, ServiceState, ServiceStatus};

#[test]
fn test_service_config_creation() {
    let config = ServiceConfig::new("test_library.dll".to_string(), "test.db".to_string());

    assert_eq!(config.library_path, "test_library.dll");
    assert_eq!(config.db_path, "test.db");
    assert_eq!(config.log_level, LogLevel::Info);
    assert!(config.auto_reconnect);
    assert_eq!(config.max_reconnect_attempts, 5);
    assert_eq!(config.reconnect_delay_seconds, 5);
}

#[test]
fn test_service_config_builder() {
    let config = ServiceConfig::new("test.dll".to_string(), "test.db".to_string())
        .with_log_level(LogLevel::Debug)
        .with_auto_reconnect(false)
        .with_max_reconnect_attempts(10)
        .with_reconnect_delay(30);

    assert_eq!(config.log_level, LogLevel::Debug);
    assert!(!config.auto_reconnect);
    assert_eq!(config.max_reconnect_attempts, 10);
    assert_eq!(config.reconnect_delay_seconds, 30);
}

#[test]
fn test_service_config_validation_valid() {
    let config = ServiceConfig::new(
        "src-tauri/Cargo.toml".to_string(), // Use existing file for test
        "test.db".to_string(),
    );

    assert!(config.validate().is_ok());
}

#[test]
fn test_service_config_validation_empty_library_path() {
    let config = ServiceConfig::new("".to_string(), "test.db".to_string());

    let result = config.validate();
    assert!(result.is_err());
    assert!(result.unwrap_err().contains("Library path cannot be empty"));
}

#[test]
fn test_service_config_validation_nonexistent_library() {
    let config = ServiceConfig::new("nonexistent_library.dll".to_string(), "test.db".to_string());

    let result = config.validate();
    assert!(result.is_err());
    assert!(result.unwrap_err().contains("Library path does not exist"));
}

#[test]
fn test_service_config_validation_empty_db_path() {
    let config = ServiceConfig::new("src-tauri/Cargo.toml".to_string(), "".to_string());

    let result = config.validate();
    assert!(result.is_err());
    assert!(
        result
            .unwrap_err()
            .contains("Database path cannot be empty")
    );
}

#[test]
fn test_service_config_validation_zero_reconnect_attempts() {
    let mut config = ServiceConfig::new("src-tauri/Cargo.toml".to_string(), "test.db".to_string());
    config.max_reconnect_attempts = 0;

    let result = config.validate();
    assert!(result.is_err());
    assert!(
        result
            .unwrap_err()
            .contains("Max reconnect attempts must be greater than 0")
    );
}

#[test]
fn test_service_config_validation_zero_reconnect_delay() {
    let mut config = ServiceConfig::new("src-tauri/Cargo.toml".to_string(), "test.db".to_string());
    config.reconnect_delay_seconds = 0;

    let result = config.validate();
    assert!(result.is_err());
    assert!(
        result
            .unwrap_err()
            .contains("Reconnect delay must be greater than 0")
    );
}

#[test]
fn test_service_state_creation() {
    let state = ServiceState::new();

    assert_eq!(state.status(), ServiceStatus::NotInitialized);
    assert_eq!(state.connection_status(), ConnectionStatus::Disconnected);
    assert!(state.last_error().is_none());
    assert_eq!(state.connection_attempts(), 0);
    assert_eq!(state.messages_sent(), 0);
    assert_eq!(state.messages_received(), 0);
    assert!(state.is_healthy());
    assert!(!state.can_operate());
}

#[test]
fn test_service_state_status_transitions() {
    let mut state = ServiceState::new();

    // Test status transitions
    state.set_status(ServiceStatus::Initializing);
    assert_eq!(state.status(), ServiceStatus::Initializing);

    state.set_status(ServiceStatus::Connected);
    assert_eq!(state.status(), ServiceStatus::Connected);
    assert!(state.can_operate());

    state.set_status(ServiceStatus::Error);
    assert_eq!(state.status(), ServiceStatus::Error);
    assert!(!state.is_healthy());
}

#[test]
fn test_service_state_connection_status() {
    let mut state = ServiceState::new();

    // Test connection status changes
    state.set_connection_status(ConnectionStatus::Connecting);
    assert_eq!(state.connection_status(), ConnectionStatus::Connecting);
    assert_eq!(state.connection_attempts(), 1);

    state.set_connection_status(ConnectionStatus::Connected);
    assert_eq!(state.connection_status(), ConnectionStatus::Connected);

    // Another connecting attempt should increment counter
    state.set_connection_status(ConnectionStatus::Connecting);
    assert_eq!(state.connection_attempts(), 2);
}

#[test]
fn test_service_state_error_handling() {
    let mut state = ServiceState::new();

    // Set error
    state.set_last_error(Some("Test error".to_string()));
    assert_eq!(state.last_error(), Some("Test error"));
    assert!(!state.is_healthy());

    // Clear error by changing status
    state.set_status(ServiceStatus::Connected);
    assert!(state.last_error().is_none());
    assert!(state.is_healthy());
}

#[test]
fn test_service_state_message_counters() {
    let mut state = ServiceState::new();

    // Test message counters
    state.increment_messages_sent();
    state.increment_messages_sent();
    assert_eq!(state.messages_sent(), 2);

    state.increment_messages_received();
    assert_eq!(state.messages_received(), 1);
}

#[test]
fn test_service_state_connection_attempts() {
    let mut state = ServiceState::new();

    // Simulate connection attempts
    state.set_connection_status(ConnectionStatus::Connecting);
    state.set_connection_status(ConnectionStatus::Connecting);
    state.set_connection_status(ConnectionStatus::Connecting);
    assert_eq!(state.connection_attempts(), 3);

    // Reset attempts
    state.reset_connection_attempts();
    assert_eq!(state.connection_attempts(), 0);
}

#[test]
fn test_service_state_timestamps() {
    let state = ServiceState::new();

    // Test uptime calculation
    let uptime = state.uptime();
    assert!(uptime >= Duration::zero());

    // Test time since last change
    let time_since_change = state.time_since_last_change();
    assert!(time_since_change >= Duration::zero());
}

#[test]
fn test_service_state_statistics() {
    let mut state = ServiceState::new();

    // Add some data
    state.increment_messages_sent();
    state.increment_messages_received();
    state.set_connection_status(ConnectionStatus::Connecting);

    let stats = state.get_stats();
    assert_eq!(stats.messages_sent, 1);
    assert_eq!(stats.messages_received, 1);
    assert_eq!(stats.connection_attempts, 1);
    assert!(stats.is_healthy);
}

#[test]
fn test_service_state_health_checks() {
    let mut state = ServiceState::new();

    // Healthy states
    state.set_status(ServiceStatus::Initialized);
    assert!(state.is_healthy());

    state.set_status(ServiceStatus::Connected);
    assert!(state.is_healthy());
    assert!(state.can_operate());

    state.set_status(ServiceStatus::Disconnected);
    assert!(state.is_healthy());
    assert!(!state.can_operate());

    // Unhealthy states
    state.set_status(ServiceStatus::Error);
    assert!(!state.is_healthy());

    state.set_last_error(Some("Error occurred".to_string()));
    assert!(!state.is_healthy());
}

#[test]
fn test_service_state_connected_timestamp() {
    let mut state = ServiceState::new();

    // Initially no connection time
    assert!(state.last_connected_at().is_none());

    // Set connected status
    state.set_status(ServiceStatus::Connected);
    assert!(state.last_connected_at().is_some());

    let connected_time = state.last_connected_at().unwrap();
    assert!(connected_time <= Utc::now());
}

#[test]
fn test_service_status_default() {
    let status = ServiceStatus::default();
    assert_eq!(status, ServiceStatus::NotInitialized);
}

#[test]
fn test_service_config_default() {
    let config = ServiceConfig::default();
    assert_eq!(config.library_path, "whatsmeow_ffi.dll");
    assert_eq!(config.db_path, "whatsapp.db");
    assert_eq!(config.log_level, LogLevel::Info);
    assert!(config.auto_reconnect);
    assert_eq!(config.max_reconnect_attempts, 5);
    assert_eq!(config.reconnect_delay_seconds, 5);
}

#[test]
fn test_service_state_default() {
    let state = ServiceState::default();
    assert_eq!(state.status(), ServiceStatus::NotInitialized);
    assert_eq!(state.connection_status(), ConnectionStatus::Disconnected);
}
