use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use whatsapp_ffi_client::core::bulk_manager::{
    BulkManagerConfig, BulkMessageManager, MessagePriority, ProgressCallback, BulkProgress, BulkMessage,
};
use whatsapp_ffi_client::core::entities::MessageContent;
use whatsapp_ffi_client::core::errors::WhatsAppError;

/// Example progress callback implementation
struct ExampleProgressCallback;

#[async_trait::async_trait]
impl ProgressCallback for ExampleProgressCallback {
    async fn on_progress(&self, progress: &BulkProgress) {
        println!(
            "Operation {}: {}/{} messages processed ({}% complete)",
            progress.operation_id,
            progress.completed(),
            progress.total_messages,
            (progress.completed() as f64 / progress.total_messages as f64 * 100.0) as u32
        );
    }

    async fn on_message_sent(&self, message: &BulkMessage, message_id: String) {
        println!("✓ Message {} sent successfully: {}", message.id, message_id);
    }

    async fn on_message_failed(&self, message: &BulkMessage, error: &WhatsAppError) {
        println!("✗ Message {} failed: {}", message.id, error);
    }

    async fn on_operation_complete(&self, progress: &BulkProgress) {
        println!(
            "🎉 Operation {} completed! Success rate: {:.1}%",
            progress.operation_id,
            progress.success_rate() * 100.0
        );
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing for logging
    tracing_subscriber::fmt::init();

    println!("🚀 Starting Bulk Message Manager Example");

    // Create bulk manager configuration
    let config = BulkManagerConfig::default();
    
    // Create the bulk message manager
    let manager = BulkMessageManager::new(config);
    
    // Start the manager (this spawns worker tasks)
    manager.start().await?;
    
    println!("📋 Bulk message manager started with {} workers", manager.worker_count());

    // Create some example messages
    let messages = vec![
        (
            "<EMAIL>".to_string(),
            MessageContent::Text {
                text: "Hello! This is a bulk message #1".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::High,
        ),
        (
            "<EMAIL>".to_string(),
            MessageContent::Text {
                text: "Hello! This is a bulk message #2".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::Normal,
        ),
        (
            "<EMAIL>".to_string(),
            MessageContent::Text {
                text: "Hello! This is a bulk message #3".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::Low,
        ),
        (
            "<EMAIL>".to_string(),
            MessageContent::Text {
                text: "Hello! This is a bulk message #4".to_string(),
                quoted_message_id: None,
                mentioned_numbers: None,
            },
            MessagePriority::Critical,
        ),
    ];

    // Create progress callback
    let callback = Arc::new(ExampleProgressCallback);

    // Queue the messages for bulk sending
    let operation_id = manager
        .queue_messages(
            "example_bulk_op".to_string(),
            messages,
            Some(3), // Max retries
            Some(callback.clone()),
        )
        .await?;

    println!("📤 Queued bulk operation: {}", operation_id);
    println!("📊 Initial queue size: {}", manager.queue_size().await);

    // Give workers a moment to start processing
    sleep(Duration::from_millis(100)).await;

    // Monitor progress
    let mut last_completed = 0;
    let mut monitoring_count = 0;
    const MAX_MONITORING_ITERATIONS: u32 = 60; // 30 seconds max
    
    loop {
        if let Some(progress) = manager.get_progress(&operation_id).await {
            let current_completed = progress.completed();
            
            // Show progress if it changed or every 10 iterations
            if current_completed != last_completed || monitoring_count % 10 == 0 {
                callback.on_progress(&progress).await;
                last_completed = current_completed;
            }
            
            if progress.is_complete() {
                callback.on_operation_complete(&progress).await;
                break;
            }
        } else {
            println!("⚠️  Progress tracking lost for operation: {}", operation_id);
            break;
        }
        
        monitoring_count += 1;
        if monitoring_count > MAX_MONITORING_ITERATIONS {
            println!("⏰ Monitoring timeout reached, stopping...");
            break;
        }
        
        sleep(Duration::from_millis(500)).await;
    }

    // Show final statistics
    let stats = manager.get_statistics().await;
    println!("\n📊 Final Statistics:");
    println!("  Total operations: {}", stats.total_operations);
    println!("  Messages processed: {}", stats.total_messages_processed);
    println!("  Messages sent: {}", stats.total_messages_sent);
    println!("  Messages failed: {}", stats.total_messages_failed);
    println!("  Rate limit hits: {}", stats.rate_limit_hits);
    println!("  Retry attempts: {}", stats.retry_attempts);
    println!("  Average success rate: {:.1}%", stats.average_success_rate * 100.0);

    // Demonstrate rate limiting
    println!("\n⏱️  Rate Limiting Demo:");
    println!("Can send now: {}", manager.can_send_now().await);
    let wait_time = manager.time_until_next_send().await;
    if wait_time > Duration::from_secs(0) {
        println!("Time until next send: {:?}", wait_time);
    }

    // Show active operations
    let active_ops = manager.get_active_operations().await;
    println!("\n🔄 Active operations: {}", active_ops.len());
    for (op_id, progress) in active_ops {
        println!("  {}: {}/{} messages", op_id, progress.completed(), progress.total_messages);
    }

    // Stop the manager
    manager.stop().await?;
    println!("\n🛑 Bulk message manager stopped");

    Ok(())
}