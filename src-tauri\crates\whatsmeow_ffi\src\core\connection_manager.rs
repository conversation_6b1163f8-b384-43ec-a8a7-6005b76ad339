use serde::{Deserialize, Serialize};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex};
use tokio::time::{sleep, timeout};
use tracing::{debug, error, info, warn};

use crate::core::entities::{ConnectionStatus, WhatsAppEvent};
use crate::core::errors::{Result, WhatsAppError};

/// Configuration for reconnection behavior
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReconnectConfig {
    /// Maximum number of reconnection attempts
    pub max_attempts: u32,
    /// Initial delay between reconnection attempts (in milliseconds)
    pub initial_delay_ms: u64,
    /// Maximum delay between reconnection attempts (in milliseconds)
    pub max_delay_ms: u64,
    /// Multiplier for exponential backoff
    pub backoff_multiplier: f64,
    /// Jitter factor to add randomness to delays (0.0 to 1.0)
    pub jitter_factor: f64,
    /// Timeout for each connection attempt (in milliseconds)
    pub connection_timeout_ms: u64,
}

impl Default for ReconnectConfig {
    fn default() -> Self {
        Self {
            max_attempts: 10,
            initial_delay_ms: 1000, // 1 second
            max_delay_ms: 300_000,  // 5 minutes
            backoff_multiplier: 2.0,
            jitter_factor: 0.1,
            connection_timeout_ms: 30_000, // 30 seconds
        }
    }
}

/// Enhanced connection status with detailed state information
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DetailedConnectionStatus {
    Disconnected,
    Connecting,
    Connected {
        connected_at: chrono::DateTime<chrono::Utc>,
        session_id: Option<String>,
    },
    Authenticating,
    Authenticated {
        authenticated_at: chrono::DateTime<chrono::Utc>,
        user_jid: Option<String>,
    },
    Reconnecting {
        attempt: u32,
        #[serde(skip, default = "Instant::now")]
        next_retry: Instant,
        last_error: Option<String>,
    },
    Failed {
        error: String,
        #[serde(skip, default = "Instant::now")]
        last_attempt: Instant,
        total_attempts: u32,
    },
    ShuttingDown,
}

impl DetailedConnectionStatus {
    /// Convert to simple ConnectionStatus for backward compatibility
    pub fn to_simple(&self) -> ConnectionStatus {
        match self {
            Self::Disconnected | Self::Failed { .. } => ConnectionStatus::Disconnected,
            Self::Connecting | Self::Reconnecting { .. } => ConnectionStatus::Connecting,
            Self::Connected { .. } => ConnectionStatus::Connected,
            Self::Authenticating | Self::Authenticated { .. } => ConnectionStatus::Connected,
            Self::ShuttingDown => ConnectionStatus::LoggedOut,
        }
    }

    /// Check if the status indicates an active connection
    pub fn is_connected(&self) -> bool {
        matches!(self, Self::Connected { .. } | Self::Authenticated { .. })
    }

    /// Check if the status indicates a connection attempt in progress
    pub fn is_connecting(&self) -> bool {
        matches!(
            self,
            Self::Connecting | Self::Authenticating | Self::Reconnecting { .. }
        )
    }

    /// Check if the status indicates a failed state
    pub fn is_failed(&self) -> bool {
        matches!(self, Self::Failed { .. })
    }
}

/// Session data for persistence and restoration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionData {
    pub session_id: String,
    pub user_jid: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_used: chrono::DateTime<chrono::Utc>,
    pub device_info: Option<DeviceInfo>,
    pub auth_token: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_id: String,
    pub platform: String,
    pub app_version: String,
}

/// Trait for session storage implementations
#[async_trait::async_trait]
pub trait SessionStore: Send + Sync {
    /// Save session data
    async fn save_session(&self, session: &SessionData) -> Result<()>;

    /// Load session data
    async fn load_session(&self, session_id: &str) -> Result<Option<SessionData>>;

    /// Delete session data
    async fn delete_session(&self, session_id: &str) -> Result<()>;

    /// List all stored sessions
    async fn list_sessions(&self) -> Result<Vec<String>>;

    /// Check if session exists
    async fn session_exists(&self, session_id: &str) -> Result<bool>;
}

/// In-memory session store for testing and simple use cases
#[derive(Debug, Default)]
pub struct InMemorySessionStore {
    sessions: Arc<Mutex<std::collections::HashMap<String, SessionData>>>,
}

#[async_trait::async_trait]
impl SessionStore for InMemorySessionStore {
    async fn save_session(&self, session: &SessionData) -> Result<()> {
        let mut sessions = self.sessions.lock().await;
        sessions.insert(session.session_id.clone(), session.clone());
        debug!(session_id = %session.session_id, "Session saved to memory store");
        Ok(())
    }

    async fn load_session(&self, session_id: &str) -> Result<Option<SessionData>> {
        let sessions = self.sessions.lock().await;
        let session = sessions.get(session_id).cloned();
        debug!(session_id = %session_id, found = session.is_some(), "Session loaded from memory store");
        Ok(session)
    }

    async fn delete_session(&self, session_id: &str) -> Result<()> {
        let mut sessions = self.sessions.lock().await;
        let removed = sessions.remove(session_id).is_some();
        debug!(session_id = %session_id, removed = removed, "Session deleted from memory store");
        Ok(())
    }

    async fn list_sessions(&self) -> Result<Vec<String>> {
        let sessions = self.sessions.lock().await;
        let session_ids: Vec<String> = sessions.keys().cloned().collect();
        debug!(
            count = session_ids.len(),
            "Listed sessions from memory store"
        );
        Ok(session_ids)
    }

    async fn session_exists(&self, session_id: &str) -> Result<bool> {
        let sessions = self.sessions.lock().await;
        let exists = sessions.contains_key(session_id);
        debug!(session_id = %session_id, exists = exists, "Checked session existence in memory store");
        Ok(exists)
    }
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    /// Interval between health checks (in milliseconds)
    pub check_interval_ms: u64,
    /// Timeout for each health check (in milliseconds)
    pub check_timeout_ms: u64,
    /// Number of consecutive failed checks before marking as unhealthy
    pub failure_threshold: u32,
    /// Enable/disable health monitoring
    pub enabled: bool,
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            check_interval_ms: 30_000, // 30 seconds
            check_timeout_ms: 5_000,   // 5 seconds
            failure_threshold: 3,
            enabled: true,
        }
    }
}

/// Health status information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatus {
    pub is_healthy: bool,
    pub last_check: chrono::DateTime<chrono::Utc>,
    pub consecutive_failures: u32,
    pub last_error: Option<String>,
    pub uptime: Duration,
    pub total_checks: u64,
    pub successful_checks: u64,
}

/// Connection manager with auto-reconnection and health monitoring
pub struct ConnectionManager {
    status: Arc<RwLock<DetailedConnectionStatus>>,
    reconnect_config: ReconnectConfig,
    health_config: HealthCheckConfig,
    session_store: Arc<dyn SessionStore>,
    event_sender: Option<mpsc::UnboundedSender<WhatsAppEvent>>,
    health_status: Arc<RwLock<HealthStatus>>,
    shutdown_signal: Arc<tokio::sync::Notify>,
    connection_start_time: Arc<RwLock<Option<Instant>>>,
}

impl ConnectionManager {
    /// Create a new connection manager
    pub fn new(
        reconnect_config: ReconnectConfig,
        health_config: HealthCheckConfig,
        session_store: Arc<dyn SessionStore>,
    ) -> Self {
        let now = chrono::Utc::now();
        let health_status = HealthStatus {
            is_healthy: false,
            last_check: now,
            consecutive_failures: 0,
            last_error: None,
            uptime: Duration::from_secs(0),
            total_checks: 0,
            successful_checks: 0,
        };

        Self {
            status: Arc::new(RwLock::new(DetailedConnectionStatus::Disconnected)),
            reconnect_config,
            health_config,
            session_store,
            event_sender: None,
            health_status: Arc::new(RwLock::new(health_status)),
            shutdown_signal: Arc::new(tokio::sync::Notify::new()),
            connection_start_time: Arc::new(RwLock::new(None)),
        }
    }

    /// Set event sender for emitting connection events
    pub fn set_event_sender(&mut self, sender: mpsc::UnboundedSender<WhatsAppEvent>) {
        self.event_sender = Some(sender);
    }

    /// Get current connection status
    pub fn get_status(&self) -> DetailedConnectionStatus {
        self.status.read().unwrap().clone()
    }

    /// Get current health status
    pub fn get_health_status(&self) -> HealthStatus {
        let mut health = self.health_status.read().unwrap().clone();

        // Update uptime if connected
        if let Some(start_time) = *self.connection_start_time.read().unwrap() {
            health.uptime = start_time.elapsed();
        }

        health
    }

    /// Update connection status and emit events
    pub fn update_status(&self, new_status: DetailedConnectionStatus) {
        let old_status = {
            let mut status = self.status.write().unwrap();
            let old = status.clone();
            *status = new_status.clone();
            old
        };

        // Update connection start time
        match &new_status {
            DetailedConnectionStatus::Connected { .. }
            | DetailedConnectionStatus::Authenticated { .. } => {
                if !old_status.is_connected() {
                    *self.connection_start_time.write().unwrap() = Some(Instant::now());
                }
            }
            DetailedConnectionStatus::Disconnected | DetailedConnectionStatus::Failed { .. } => {
                *self.connection_start_time.write().unwrap() = None;
            }
            _ => {}
        }

        info!(
            old_status = ?old_status,
            new_status = ?new_status,
            "Connection status changed"
        );

        // Emit event if sender is available
        if let Some(sender) = &self.event_sender {
            let event = WhatsAppEvent::ConnectionStatusChanged {
                status: new_status.to_simple(),
                reason: match &new_status {
                    DetailedConnectionStatus::Failed { error, .. } => Some(error.clone()),
                    DetailedConnectionStatus::Reconnecting { last_error, .. } => last_error.clone(),
                    _ => None,
                },
            };

            if let Err(e) = sender.send(event) {
                error!(error = %e, "Failed to send connection status event");
            }
        }
    }

    /// Start connection with auto-reconnection
    pub async fn connect<F, Fut>(&self, connect_fn: F) -> Result<()>
    where
        F: Fn() -> Fut + Send + Sync + 'static,
        Fut: std::future::Future<Output = Result<()>> + Send + 'static,
    {
        let connect_fn = Arc::new(connect_fn);
        self.connect_with_retry(connect_fn).await
    }

    /// Internal method to handle connection with retry logic
    async fn connect_with_retry<F, Fut>(&self, connect_fn: Arc<F>) -> Result<()>
    where
        F: Fn() -> Fut + Send + Sync + 'static,
        Fut: std::future::Future<Output = Result<()>> + Send + 'static,
    {
        let mut attempt = 0;
        let mut delay = Duration::from_millis(self.reconnect_config.initial_delay_ms);

        loop {
            attempt += 1;

            // Check if we should stop trying
            if attempt > self.reconnect_config.max_attempts {
                let error_msg = format!(
                    "Failed to connect after {} attempts",
                    self.reconnect_config.max_attempts
                );
                self.update_status(DetailedConnectionStatus::Failed {
                    error: error_msg.clone(),
                    last_attempt: Instant::now(),
                    total_attempts: attempt - 1,
                });
                return Err(WhatsAppError::Internal(error_msg));
            }

            // Update status to show reconnection attempt
            if attempt > 1 {
                self.update_status(DetailedConnectionStatus::Reconnecting {
                    attempt,
                    next_retry: Instant::now() + delay,
                    last_error: None,
                });

                info!(
                    attempt = attempt,
                    delay_ms = delay.as_millis(),
                    "Waiting before reconnection attempt"
                );
                sleep(delay).await;
            }

            self.update_status(DetailedConnectionStatus::Connecting);

            info!(attempt = attempt, "Attempting to connect");

            // Try to connect with timeout
            let connect_result = timeout(
                Duration::from_millis(self.reconnect_config.connection_timeout_ms),
                connect_fn(),
            )
            .await;

            match connect_result {
                Ok(Ok(())) => {
                    info!(attempt = attempt, "Connection successful");
                    self.update_status(DetailedConnectionStatus::Connected {
                        connected_at: chrono::Utc::now(),
                        session_id: None,
                    });

                    // Start health monitoring if enabled
                    if self.health_config.enabled {
                        self.start_health_monitoring().await;
                    }

                    return Ok(());
                }
                Ok(Err(e)) => {
                    warn!(
                        attempt = attempt,
                        error = %e,
                        "Connection attempt failed"
                    );

                    // Check if error is recoverable
                    if !e.is_recoverable() {
                        let error_msg = format!("Non-recoverable error: {}", e);
                        self.update_status(DetailedConnectionStatus::Failed {
                            error: error_msg.clone(),
                            last_attempt: Instant::now(),
                            total_attempts: attempt,
                        });
                        return Err(e);
                    }
                }
                Err(_) => {
                    warn!(
                        attempt = attempt,
                        timeout_ms = self.reconnect_config.connection_timeout_ms,
                        "Connection attempt timed out"
                    );
                }
            }

            // Calculate next delay with exponential backoff and jitter
            delay = self.calculate_next_delay(delay);
        }
    }

    /// Calculate next delay with exponential backoff and jitter
    fn calculate_next_delay(&self, current_delay: Duration) -> Duration {
        let next_delay_ms =
            (current_delay.as_millis() as f64 * self.reconnect_config.backoff_multiplier) as u64;
        let capped_delay_ms = next_delay_ms.min(self.reconnect_config.max_delay_ms);

        // Add jitter to prevent thundering herd
        let jitter_range = (capped_delay_ms as f64 * self.reconnect_config.jitter_factor) as u64;
        let jitter = if jitter_range > 0 {
            fastrand::u64(0..=jitter_range)
        } else {
            0
        };

        Duration::from_millis(capped_delay_ms + jitter)
    }

    /// Start health monitoring task
    async fn start_health_monitoring(&self) {
        let status = self.status.clone();
        let health_status = self.health_status.clone();
        let health_config = self.health_config.clone();
        let shutdown_signal = self.shutdown_signal.clone();
        let event_sender = self.event_sender.clone();

        tokio::spawn(async move {
            let mut interval =
                tokio::time::interval(Duration::from_millis(health_config.check_interval_ms));

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        // Perform health check
                        let check_result = Self::perform_health_check(&status, &health_config).await;

                        // Update health status
                        {
                            let mut health = health_status.write().unwrap();
                            health.last_check = chrono::Utc::now();
                            health.total_checks += 1;

                            match check_result {
                                Ok(()) => {
                                    health.is_healthy = true;
                                    health.consecutive_failures = 0;
                                    health.successful_checks += 1;
                                    health.last_error = None;
                                }
                                Err(e) => {
                                    health.consecutive_failures += 1;
                                    health.last_error = Some(e.to_string());

                                    if health.consecutive_failures >= health_config.failure_threshold {
                                        health.is_healthy = false;

                                        // Emit health failure event
                                        if let Some(sender) = &event_sender {
                                            let event = WhatsAppEvent::Error {
                                                code: -1,
                                                message: format!("Health check failed: {}", e),
                                            };
                                            let _ = sender.send(event);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    _ = shutdown_signal.notified() => {
                        debug!("Health monitoring task shutting down");
                        break;
                    }
                }
            }
        });
    }

    /// Perform a single health check
    async fn perform_health_check(
        status: &Arc<RwLock<DetailedConnectionStatus>>,
        _config: &HealthCheckConfig,
    ) -> Result<()> {
        let current_status = status.read().unwrap().clone();

        // Simple health check - verify connection status
        match current_status {
            DetailedConnectionStatus::Connected { .. }
            | DetailedConnectionStatus::Authenticated { .. } => {
                // Connection appears healthy
                Ok(())
            }
            DetailedConnectionStatus::Disconnected => Err(WhatsAppError::NotConnected),
            DetailedConnectionStatus::Failed { error, .. } => Err(WhatsAppError::Internal(
                format!("Connection failed: {}", error),
            )),
            _ => {
                // Connecting states are considered temporarily unhealthy but not failed
                Err(WhatsAppError::Internal(
                    "Connection in progress".to_string(),
                ))
            }
        }
    }

    /// Disconnect and stop all monitoring
    pub async fn disconnect(&self) -> Result<()> {
        info!("Disconnecting connection manager");

        self.update_status(DetailedConnectionStatus::ShuttingDown);

        // Signal shutdown to health monitoring
        self.shutdown_signal.notify_waiters();

        // Reset connection start time
        *self.connection_start_time.write().unwrap() = None;

        self.update_status(DetailedConnectionStatus::Disconnected);

        Ok(())
    }

    /// Save current session
    pub async fn save_session(&self, session: SessionData) -> Result<()> {
        debug!(session_id = %session.session_id, "Saving session");
        self.session_store.save_session(&session).await
    }

    /// Load session by ID
    pub async fn load_session(&self, session_id: &str) -> Result<Option<SessionData>> {
        debug!(session_id = %session_id, "Loading session");
        self.session_store.load_session(session_id).await
    }

    /// Delete session by ID
    pub async fn delete_session(&self, session_id: &str) -> Result<()> {
        debug!(session_id = %session_id, "Deleting session");
        self.session_store.delete_session(session_id).await
    }

    /// List all stored sessions
    pub async fn list_sessions(&self) -> Result<Vec<String>> {
        debug!("Listing all sessions");
        self.session_store.list_sessions().await
    }

    /// Check if session exists
    pub async fn session_exists(&self, session_id: &str) -> Result<bool> {
        self.session_store.session_exists(session_id).await
    }

    /// Force reconnection
    pub async fn force_reconnect<F, Fut>(&self, connect_fn: F) -> Result<()>
    where
        F: Fn() -> Fut + Send + Sync + 'static,
        Fut: std::future::Future<Output = Result<()>> + Send + 'static,
    {
        info!("Forcing reconnection");

        // Disconnect first
        self.disconnect().await?;

        // Wait a moment before reconnecting
        sleep(Duration::from_millis(1000)).await;

        // Reconnect
        self.connect(connect_fn).await
    }
}

// Add fastrand dependency for jitter calculation
// This is a lightweight random number generator
mod fastrand {
    use std::cell::Cell;
    use std::num::Wrapping;

    thread_local! {
        static RNG: Cell<Wrapping<u64>> = const { Cell::new(Wrapping(1)) };
    }

    pub fn u64(range: std::ops::RangeInclusive<u64>) -> u64 {
        let start = *range.start();
        let end = *range.end();

        if start == end {
            return start;
        }

        RNG.with(|rng| {
            let mut x = rng.get();
            x ^= x << 13;
            x ^= x >> 7;
            x ^= x << 17;
            rng.set(x);

            let range_size = end - start + 1;
            start + (x.0 % range_size)
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicU32, Ordering};
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_connection_manager_creation() {
        let config = ReconnectConfig::default();
        let health_config = HealthCheckConfig::default();
        let session_store = Arc::new(InMemorySessionStore::default());

        let manager = ConnectionManager::new(config, health_config, session_store);

        assert_eq!(manager.get_status(), DetailedConnectionStatus::Disconnected);
        assert!(!manager.get_health_status().is_healthy);
    }

    #[tokio::test]
    async fn test_successful_connection() {
        let config = ReconnectConfig::default();
        let health_config = HealthCheckConfig {
            enabled: false,
            ..Default::default()
        };
        let session_store = Arc::new(InMemorySessionStore::default());

        let manager = ConnectionManager::new(config, health_config, session_store);

        // Mock successful connection
        let connect_fn = || async { Ok(()) };

        let result = manager.connect(connect_fn).await;
        assert!(result.is_ok());
        assert!(manager.get_status().is_connected());
    }

    #[tokio::test]
    async fn test_connection_retry_on_failure() {
        let config = ReconnectConfig {
            max_attempts: 3,
            initial_delay_ms: 10, // Very short delay for testing
            ..Default::default()
        };
        let health_config = HealthCheckConfig {
            enabled: false,
            ..Default::default()
        };
        let session_store = Arc::new(InMemorySessionStore::default());

        let manager = ConnectionManager::new(config, health_config, session_store);

        let attempt_count = Arc::new(AtomicU32::new(0));
        let attempt_count_clone = attempt_count.clone();

        // Mock connection that fails twice then succeeds
        let connect_fn = move || {
            let count = attempt_count_clone.clone();
            async move {
                let current = count.fetch_add(1, Ordering::SeqCst);
                if current < 2 {
                    Err(WhatsAppError::NotConnected)
                } else {
                    Ok(())
                }
            }
        };

        let result = manager.connect(connect_fn).await;
        assert!(result.is_ok());
        assert!(manager.get_status().is_connected());
        assert_eq!(attempt_count.load(Ordering::SeqCst), 3);
    }

    #[tokio::test]
    async fn test_connection_failure_after_max_attempts() {
        let config = ReconnectConfig {
            max_attempts: 2,
            initial_delay_ms: 10,
            ..Default::default()
        };
        let health_config = HealthCheckConfig {
            enabled: false,
            ..Default::default()
        };
        let session_store = Arc::new(InMemorySessionStore::default());

        let manager = ConnectionManager::new(config, health_config, session_store);

        // Mock connection that always fails
        let connect_fn = || async { Err(WhatsAppError::NotConnected) };

        let result = manager.connect(connect_fn).await;
        assert!(result.is_err());
        assert!(manager.get_status().is_failed());
    }

    #[tokio::test]
    async fn test_session_persistence() {
        let config = ReconnectConfig::default();
        let health_config = HealthCheckConfig::default();
        let session_store = Arc::new(InMemorySessionStore::default());

        let manager = ConnectionManager::new(config, health_config, session_store);

        let session = SessionData {
            session_id: "test_session".to_string(),
            user_jid: Some("<EMAIL>".to_string()),
            created_at: chrono::Utc::now(),
            last_used: chrono::Utc::now(),
            device_info: None,
            auth_token: Some("token123".to_string()),
        };

        // Save session
        let result = manager.save_session(session.clone()).await;
        assert!(result.is_ok());

        // Load session
        let loaded = manager.load_session("test_session").await.unwrap();
        assert!(loaded.is_some());
        assert_eq!(loaded.unwrap().session_id, "test_session");

        // Check existence
        let exists = manager.session_exists("test_session").await.unwrap();
        assert!(exists);

        // Delete session
        let result = manager.delete_session("test_session").await;
        assert!(result.is_ok());

        // Verify deletion
        let exists = manager.session_exists("test_session").await.unwrap();
        assert!(!exists);
    }

    #[tokio::test]
    async fn test_exponential_backoff_calculation() {
        let config = ReconnectConfig {
            initial_delay_ms: 1000,
            max_delay_ms: 10000,
            backoff_multiplier: 2.0,
            jitter_factor: 0.0, // No jitter for predictable testing
            ..Default::default()
        };
        let health_config = HealthCheckConfig::default();
        let session_store = Arc::new(InMemorySessionStore::default());

        let manager = ConnectionManager::new(config, health_config, session_store);

        let delay1 = Duration::from_millis(1000);
        let delay2 = manager.calculate_next_delay(delay1);
        assert_eq!(delay2.as_millis(), 2000);

        let delay3 = manager.calculate_next_delay(delay2);
        assert_eq!(delay3.as_millis(), 4000);

        let delay4 = manager.calculate_next_delay(delay3);
        assert_eq!(delay4.as_millis(), 8000);

        // Should cap at max_delay_ms
        let delay5 = manager.calculate_next_delay(delay4);
        assert_eq!(delay5.as_millis(), 10000);
    }

    #[tokio::test]
    async fn test_event_emission() {
        let config = ReconnectConfig::default();
        let health_config = HealthCheckConfig {
            enabled: false,
            ..Default::default()
        };
        let session_store = Arc::new(InMemorySessionStore::default());

        let mut manager = ConnectionManager::new(config, health_config, session_store);

        let (tx, mut rx) = mpsc::unbounded_channel();
        manager.set_event_sender(tx);

        // Update status should emit event
        manager.update_status(DetailedConnectionStatus::Connected {
            connected_at: chrono::Utc::now(),
            session_id: None,
        });

        let event = rx.recv().await.unwrap();
        match event {
            WhatsAppEvent::ConnectionStatusChanged { status, .. } => {
                assert_eq!(status, ConnectionStatus::Connected);
            }
            _ => panic!("Expected ConnectionStatusChanged event"),
        }
    }

    #[tokio::test]
    async fn test_disconnect() {
        let config = ReconnectConfig::default();
        let health_config = HealthCheckConfig {
            enabled: false,
            ..Default::default()
        };
        let session_store = Arc::new(InMemorySessionStore::default());

        let manager = ConnectionManager::new(config, health_config, session_store);

        // Connect first
        let connect_fn = || async { Ok(()) };
        let _ = manager.connect(connect_fn).await;
        assert!(manager.get_status().is_connected());

        // Disconnect
        let result = manager.disconnect().await;
        assert!(result.is_ok());
        assert_eq!(manager.get_status(), DetailedConnectionStatus::Disconnected);
    }
}
#[tokio::test]
async fn test_connection_state_transitions() {
    let config = ReconnectConfig::default();
    let health_config = HealthCheckConfig {
        enabled: false,
        ..Default::default()
    };
    let session_store = Arc::new(InMemorySessionStore::default());

    let manager = ConnectionManager::new(config, health_config, session_store);

    // Initial state should be Disconnected
    assert_eq!(manager.get_status(), DetailedConnectionStatus::Disconnected);

    // Test state transitions
    manager.update_status(DetailedConnectionStatus::Connecting);
    assert_eq!(manager.get_status(), DetailedConnectionStatus::Connecting);
    assert!(manager.get_status().is_connecting());

    manager.update_status(DetailedConnectionStatus::Connected {
        connected_at: chrono::Utc::now(),
        session_id: Some("test_session".to_string()),
    });
    assert!(manager.get_status().is_connected());

    manager.update_status(DetailedConnectionStatus::Authenticating);
    assert!(manager.get_status().is_connecting());

    manager.update_status(DetailedConnectionStatus::Authenticated {
        authenticated_at: chrono::Utc::now(),
        user_jid: Some("<EMAIL>".to_string()),
    });
    assert!(manager.get_status().is_connected());

    manager.update_status(DetailedConnectionStatus::Failed {
        error: "Test error".to_string(),
        last_attempt: Instant::now(),
        total_attempts: 1,
    });
    assert!(manager.get_status().is_failed());
}

#[tokio::test]
async fn test_exponential_backoff_with_jitter() {
    let config = ReconnectConfig {
        initial_delay_ms: 1000,
        max_delay_ms: 10000,
        backoff_multiplier: 2.0,
        jitter_factor: 0.5, // 50% jitter
        ..Default::default()
    };
    let health_config = HealthCheckConfig::default();
    let session_store = Arc::new(InMemorySessionStore::default());

    let manager = ConnectionManager::new(config, health_config, session_store);

    let delay1 = Duration::from_millis(1000);
    let delay2 = manager.calculate_next_delay(delay1);

    // With jitter, delay should be between 2000ms and 3000ms (2000 + 50% jitter)
    assert!(delay2.as_millis() >= 2000);
    assert!(delay2.as_millis() <= 3000);

    let delay3 = manager.calculate_next_delay(delay2);
    // Should be around 4000ms with jitter
    assert!(delay3.as_millis() >= 4000);
    assert!(delay3.as_millis() <= 6000);
}

#[tokio::test]
async fn test_connection_timeout_scenario() {
    let config = ReconnectConfig {
        max_attempts: 2,
        initial_delay_ms: 10,
        connection_timeout_ms: 50, // Very short timeout
        ..Default::default()
    };
    let health_config = HealthCheckConfig {
        enabled: false,
        ..Default::default()
    };
    let session_store = Arc::new(InMemorySessionStore::default());

    let manager = ConnectionManager::new(config, health_config, session_store);

    // Mock connection that takes too long
    let connect_fn = || async {
        sleep(Duration::from_millis(100)).await; // Longer than timeout
        Ok(())
    };

    let result = manager.connect(connect_fn).await;
    assert!(result.is_err());
    assert!(manager.get_status().is_failed());
}

#[tokio::test]
async fn test_non_recoverable_error_handling() {
    let config = ReconnectConfig {
        max_attempts: 5,
        initial_delay_ms: 10,
        ..Default::default()
    };
    let health_config = HealthCheckConfig {
        enabled: false,
        ..Default::default()
    };
    let session_store = Arc::new(InMemorySessionStore::default());

    let manager = ConnectionManager::new(config, health_config, session_store);

    // Mock connection that returns non-recoverable error
    let connect_fn = || async {
        Err(WhatsAppError::Internal(
            "Non-recoverable library error".to_string(),
        ))
    };

    let result = manager.connect(connect_fn).await;
    assert!(result.is_err());
    assert!(manager.get_status().is_failed());

    // Should fail immediately without retries for non-recoverable errors
    if let DetailedConnectionStatus::Failed { total_attempts, .. } = manager.get_status() {
        assert_eq!(total_attempts, 1);
    } else {
        panic!("Expected Failed status");
    }
}

#[tokio::test]
async fn test_session_corruption_handling() {
    let session_store = Arc::new(InMemorySessionStore::default());

    // Create a session with invalid data
    let invalid_session = SessionData {
        session_id: "corrupted_session".to_string(),
        user_jid: None,
        created_at: chrono::Utc::now(),
        last_used: chrono::Utc::now() - chrono::Duration::days(30), // Very old
        device_info: None,
        auth_token: None,
    };

    let config = ReconnectConfig::default();
    let health_config = HealthCheckConfig::default();
    let manager = ConnectionManager::new(config, health_config, session_store.clone());

    // Save the corrupted session
    let result = manager.save_session(invalid_session).await;
    assert!(result.is_ok());

    // Load and verify we can handle it
    let loaded = manager.load_session("corrupted_session").await.unwrap();
    assert!(loaded.is_some());

    let session = loaded.unwrap();
    assert!(session.auth_token.is_none());
    assert!(session.user_jid.is_none());

    // Delete corrupted session
    let result = manager.delete_session("corrupted_session").await;
    assert!(result.is_ok());

    // Verify deletion
    let exists = manager.session_exists("corrupted_session").await.unwrap();
    assert!(!exists);
}

#[tokio::test]
async fn test_health_check_failure_threshold() {
    let config = ReconnectConfig::default();
    let health_config = HealthCheckConfig {
        enabled: true,
        check_interval_ms: 10, // Very frequent checks
        failure_threshold: 2,
        ..Default::default()
    };
    let session_store = Arc::new(InMemorySessionStore::default());

    let failure_threshold = health_config.failure_threshold;
    let manager = ConnectionManager::new(config, health_config, session_store);

    // Set up event receiver
    let (tx, _rx) = mpsc::unbounded_channel();
    let mut manager_mut = manager;
    manager_mut.set_event_sender(tx);

    // Set status to failed to trigger health check failures
    manager_mut.update_status(DetailedConnectionStatus::Failed {
        error: "Test failure".to_string(),
        last_attempt: Instant::now(),
        total_attempts: 1,
    });

    // Start health monitoring
    manager_mut.start_health_monitoring().await;

    // Wait for health check events
    tokio::time::sleep(Duration::from_millis(50)).await;

    let health_status = manager_mut.get_health_status();
    assert!(!health_status.is_healthy);
    assert!(health_status.consecutive_failures >= failure_threshold);
}

#[tokio::test]
async fn test_force_reconnect() {
    let config = ReconnectConfig::default();
    let health_config = HealthCheckConfig {
        enabled: false,
        ..Default::default()
    };
    let session_store = Arc::new(InMemorySessionStore::default());

    let manager = ConnectionManager::new(config, health_config, session_store);

    // First connect
    let connect_fn = || async { Ok(()) };
    let result = manager.connect(connect_fn).await;
    assert!(result.is_ok());
    assert!(manager.get_status().is_connected());

    // Force reconnect
    let reconnect_fn = || async { Ok(()) };
    let result = manager.force_reconnect(reconnect_fn).await;
    assert!(result.is_ok());
    assert!(manager.get_status().is_connected());
}

#[tokio::test]
async fn test_session_list_operations() {
    let session_store = Arc::new(InMemorySessionStore::default());
    let config = ReconnectConfig::default();
    let health_config = HealthCheckConfig::default();
    let manager = ConnectionManager::new(config, health_config, session_store);

    // Create multiple sessions
    let sessions = vec![
        SessionData {
            session_id: "session1".to_string(),
            user_jid: Some("<EMAIL>".to_string()),
            created_at: chrono::Utc::now(),
            last_used: chrono::Utc::now(),
            device_info: None,
            auth_token: Some("token1".to_string()),
        },
        SessionData {
            session_id: "session2".to_string(),
            user_jid: Some("<EMAIL>".to_string()),
            created_at: chrono::Utc::now(),
            last_used: chrono::Utc::now(),
            device_info: None,
            auth_token: Some("token2".to_string()),
        },
    ];

    // Save sessions
    for session in &sessions {
        let result = manager.save_session(session.clone()).await;
        assert!(result.is_ok());
    }

    // List sessions
    let session_list = manager.list_sessions().await.unwrap();
    assert_eq!(session_list.len(), 2);
    assert!(session_list.contains(&"session1".to_string()));
    assert!(session_list.contains(&"session2".to_string()));

    // Check existence
    for session in &sessions {
        let exists = manager.session_exists(&session.session_id).await.unwrap();
        assert!(exists);
    }

    // Delete one session
    let result = manager.delete_session("session1").await;
    assert!(result.is_ok());

    // Verify updated list
    let updated_list = manager.list_sessions().await.unwrap();
    assert_eq!(updated_list.len(), 1);
    assert!(updated_list.contains(&"session2".to_string()));
    assert!(!updated_list.contains(&"session1".to_string()));
}

#[tokio::test]
async fn test_connection_uptime_tracking() {
    let config = ReconnectConfig::default();
    let health_config = HealthCheckConfig {
        enabled: false,
        ..Default::default()
    };
    let session_store = Arc::new(InMemorySessionStore::default());

    let manager = ConnectionManager::new(config, health_config, session_store);

    // Initially no uptime
    let initial_health = manager.get_health_status();
    assert_eq!(initial_health.uptime, Duration::from_secs(0));

    // Connect
    let connect_fn = || async { Ok(()) };
    let result = manager.connect(connect_fn).await;
    assert!(result.is_ok());

    // Wait a bit
    tokio::time::sleep(Duration::from_millis(50)).await;

    // Check uptime is being tracked
    let health_after_connect = manager.get_health_status();
    assert!(health_after_connect.uptime > Duration::from_millis(40));

    // Disconnect
    let result = manager.disconnect().await;
    assert!(result.is_ok());

    // Uptime should reset
    let health_after_disconnect = manager.get_health_status();
    assert_eq!(health_after_disconnect.uptime, Duration::from_secs(0));
}
