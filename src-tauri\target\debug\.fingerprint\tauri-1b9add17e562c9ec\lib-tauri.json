{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 14562594517924070096, "deps": [[40386456601120721, "percent_encoding", false, 17688296499909026107], [654232091421095663, "tauri_utils", false, 15355600758100773933], [1200537532907108615, "url<PERSON><PERSON>n", false, 15495546638958747663], [2013030631243296465, "webview2_com", false, 5504809740823490624], [3150220818285335163, "url", false, 16586463868209902736], [3331586631144870129, "getrandom", false, 2177425873348933176], [4143744114649553716, "raw_window_handle", false, 3850038477068984947], [4494683389616423722, "muda", false, 516124033735968384], [4919829919303820331, "serialize_to_javascript", false, 6495653555952588675], [5986029879202738730, "log", false, 3210362874149672375], [9010263965687315507, "http", false, 3354175764241350141], [9689903380558560274, "serde", false, 10996426815917291500], [10229185211513642314, "mime", false, 1599519404717596838], [10806645703491011684, "thiserror", false, 44651847147331727], [11989259058781683633, "dunce", false, 13340844955090052542], [12092653563678505622, "build_script_build", false, 10303223714395961871], [12304025191202589669, "tauri_runtime_wry", false, 14339425978326584786], [12565293087094287914, "window_vibrancy", false, 17557742469680236381], [12943761728066819757, "tauri_runtime", false, 5922118006371360892], [12986574360607194341, "serde_repr", false, 11609501881873135158], [13077543566650298139, "heck", false, 1670613786372742531], [13116089016666501665, "windows", false, 9661768058793115304], [13405681745520956630, "tauri_macros", false, 18190884089854671094], [13625485746686963219, "anyhow", false, 8308486080552771400], [16362055519698394275, "serde_json", false, 7262301444845053042], [16928111194414003569, "dirs", false, 6060340896348627099], [17155886227862585100, "glob", false, 16192797392537921819], [17531218394775549125, "tokio", false, 16632263990803218314]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-1b9add17e562c9ec\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}