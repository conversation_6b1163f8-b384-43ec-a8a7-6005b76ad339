//! Tauri command handlers for WhatsApp operations

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tauri::Manager;
use tauri::{AppHandle, State, ipc::Invoke, path::BaseDirectory};
use tracing::{info, instrument};
use whatsapp_ffi_client::{ConnectionStatus, LogLevel};

use super::service::WhatsAppService;
use super::state::{ServiceConfig, ServiceState, ServiceStats, ServiceStatus};

/// Error type for Tauri command responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TauriError {
    pub code: String,
    pub message: String,
    pub details: Option<String>,
}

impl TauriError {
    pub fn new(code: impl Into<String>, message: impl Into<String>) -> Self {
        Self {
            code: code.into(),
            message: message.into(),
            details: None,
        }
    }

    pub fn _with_details(mut self, details: impl Into<String>) -> Self {
        self.details = Some(details.into());
        self
    }

    pub fn internal_error(message: impl Into<String>) -> Self {
        Self::new("INTERNAL_ERROR", message)
    }

    pub fn not_connected() -> Self {
        Self::new("NOT_CONNECTED", "WhatsApp client is not connected")
    }

    pub fn _not_initialized() -> Self {
        Self::new("NOT_INITIALIZED", "WhatsApp service is not initialized")
    }

    pub fn invalid_input(message: impl Into<String>) -> Self {
        Self::new("INVALID_INPUT", message)
    }

    pub fn _connection_failed(message: impl Into<String>) -> Self {
        Self::new("CONNECTION_FAILED", message)
    }
}

/// Convert WhatsAppError to TauriError
impl From<whatsapp_ffi_client::WhatsAppError> for TauriError {
    fn from(error: whatsapp_ffi_client::WhatsAppError) -> Self {
        match error {
            whatsapp_ffi_client::WhatsAppError::NotConnected => TauriError::not_connected(),
            whatsapp_ffi_client::WhatsAppError::InvalidJid { jid, reason } => {
                TauriError::invalid_input(format!("Invalid JID {}: {}", jid, reason))
            }
            whatsapp_ffi_client::WhatsAppError::SendFailed { jid, reason } => TauriError::new(
                "SEND_FAILED",
                format!("Failed to send to {}: {}", jid, reason),
            ),
            whatsapp_ffi_client::WhatsAppError::Timeout {
                operation,
                duration_ms,
            } => TauriError::new(
                "TIMEOUT",
                format!(
                    "Operation '{}' timed out after {}ms",
                    operation, duration_ms
                ),
            ),
            whatsapp_ffi_client::WhatsAppError::LibraryLoad(e) => TauriError::new(
                "LIBRARY_LOAD_ERROR",
                format!("Failed to load library: {}", e),
            ),
            whatsapp_ffi_client::WhatsAppError::AlreadyConnected => {
                TauriError::new("ALREADY_CONNECTED", "Already connected to WhatsApp")
            }
            whatsapp_ffi_client::WhatsAppError::ShuttingDown => {
                TauriError::new("SHUTTING_DOWN", "Service is shutting down")
            }
            _ => TauriError::internal_error(error.to_string()),
        }
    }
}

/// Result type for Tauri commands
pub type TauriResult<T> = Result<T, TauriError>;

/// Initialize WhatsApp service command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_initialize(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<ServiceState> {
    info!("Initializing WhatsApp service via Tauri command");

    service.initialize().await.map_err(TauriError::from)?;

    let state = service.get_state().await;
    info!("WhatsApp service initialized successfully");
    Ok(state)
}

/// Connect to WhatsApp command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_connect(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<ServiceState> {
    info!("Connecting to WhatsApp via Tauri command");

    service.connect().await.map_err(TauriError::from)?;

    let state = service.get_state().await;
    info!("Connected to WhatsApp successfully");
    Ok(state)
}

/// Disconnect from WhatsApp command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_disconnect(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<ServiceState> {
    info!("Disconnecting from WhatsApp via Tauri command");

    service.disconnect().await.map_err(TauriError::from)?;

    let state = service.get_state().await;
    info!("Disconnected from WhatsApp successfully");
    Ok(state)
}

/// Send message command
#[tauri::command]
#[instrument(skip(service, message))]
pub async fn whatsapp_send_message(
    service: State<'_, Arc<WhatsAppService>>,
    recipient: String,
    message: String,
) -> TauriResult<String> {
    info!("Sending message to {} via Tauri command", recipient);

    // Validate inputs
    if recipient.trim().is_empty() {
        return Err(TauriError::invalid_input("Recipient cannot be empty"));
    }

    if message.trim().is_empty() {
        return Err(TauriError::invalid_input("Message cannot be empty"));
    }

    let message_id = service
        .send_message(&recipient, &message)
        .await
        .map_err(TauriError::from)?;

    info!("Message sent successfully with ID: {}", message_id);
    Ok(message_id)
}

/// Get service state command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_get_state(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<ServiceState> {
    let state = service.get_state().await;
    Ok(state)
}

/// Get service configuration command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_get_config(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<ServiceConfig> {
    let config = service.get_config().await;
    Ok(config)
}

/// Update service configuration command
#[tauri::command]
#[instrument(skip(service, config))]
pub async fn whatsapp_update_config(
    service: State<'_, Arc<WhatsAppService>>,
    config: ServiceConfig,
) -> TauriResult<()> {
    info!("Updating WhatsApp service configuration via Tauri command");

    service
        .update_config(config)
        .await
        .map_err(TauriError::from)?;

    info!("Service configuration updated successfully");
    Ok(())
}

/// Check connection status command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_is_connected(service: State<'_, Arc<WhatsAppService>>) -> TauriResult<bool> {
    let is_connected = service.is_connected().await;
    Ok(is_connected)
}

/// Get connection status command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_get_connection_status(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<ConnectionStatus> {
    let status = service.get_connection_status().await;
    Ok(status)
}

/// Get service statistics command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_get_stats(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<ServiceStats> {
    let state = service.get_state().await;
    let stats = state.get_stats();
    Ok(stats)
}

/// Shutdown service command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_shutdown(service: State<'_, Arc<WhatsAppService>>) -> TauriResult<()> {
    info!("Shutting down WhatsApp service via Tauri command");

    service.shutdown().await.map_err(TauriError::from)?;

    info!("WhatsApp service shutdown completed");
    Ok(())
}

/// Start event processing command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_start_event_processing(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<()> {
    info!("Starting event processing via Tauri command");

    service
        .start_event_processing()
        .await
        .map_err(TauriError::from)?;

    info!("Event processing started successfully");
    Ok(())
}

/// Configuration update request
#[derive(Debug, Deserialize)]
pub struct ConfigUpdateRequest {
    pub library_path: Option<String>,
    pub db_path: Option<String>,
    pub log_level: Option<LogLevel>,
    pub auto_reconnect: Option<bool>,
    pub max_reconnect_attempts: Option<u32>,
    pub reconnect_delay_seconds: Option<u64>,
}

/// Update partial configuration command
#[tauri::command]
#[instrument(skip(service, updates))]
pub async fn whatsapp_update_partial_config(
    service: State<'_, Arc<WhatsAppService>>,
    updates: ConfigUpdateRequest,
) -> TauriResult<ServiceConfig> {
    info!("Updating partial WhatsApp service configuration via Tauri command");

    let mut config = service.get_config().await;

    // Apply updates
    if let Some(library_path) = updates.library_path {
        config.library_path = library_path;
    }
    if let Some(db_path) = updates.db_path {
        config.db_path = db_path;
    }
    if let Some(log_level) = updates.log_level {
        config.log_level = log_level;
    }
    if let Some(auto_reconnect) = updates.auto_reconnect {
        config.auto_reconnect = auto_reconnect;
    }
    if let Some(max_reconnect_attempts) = updates.max_reconnect_attempts {
        config.max_reconnect_attempts = max_reconnect_attempts;
    }
    if let Some(reconnect_delay_seconds) = updates.reconnect_delay_seconds {
        config.reconnect_delay_seconds = reconnect_delay_seconds;
    }

    // Update the configuration
    service
        .update_config(config.clone())
        .await
        .map_err(TauriError::from)?;

    info!("Partial service configuration updated successfully");
    Ok(config)
}

/// Health check command
#[tauri::command]
#[instrument(skip(service))]
pub async fn whatsapp_health_check(
    service: State<'_, Arc<WhatsAppService>>,
) -> TauriResult<HealthCheckResponse> {
    let state = service.get_state().await;
    let config = service.get_config().await;

    let response = HealthCheckResponse {
        is_healthy: state.is_healthy(),
        can_operate: state.can_operate(),
        service_status: state.status(),
        connection_status: state.connection_status(),
        uptime_seconds: state.uptime().num_seconds() as u64,
        last_error: state.last_error().map(|s| s.to_string()),
        config_valid: config.validate().is_ok(),
    };

    Ok(response)
}

/// Health check response
#[derive(Debug, Serialize)]
pub struct HealthCheckResponse {
    pub is_healthy: bool,
    pub can_operate: bool,
    pub service_status: ServiceStatus,
    pub connection_status: ConnectionStatus,
    pub uptime_seconds: u64,
    pub last_error: Option<String>,
    pub config_valid: bool,
}

/// Get the path to the embedded WhatsApp FFI DLL resource
#[tauri::command]
#[instrument]
pub async fn whatsapp_get_dll_path(app_handle: AppHandle) -> Result<String, TauriError> {
    info!("Getting WhatsApp FFI DLL path from resources");

    let resource_path = app_handle
        .path()
        .resolve("whatsapp_ffi.dll", BaseDirectory::Resource)
        .map_err(|e| {
            TauriError::internal_error(format!("Failed to resolve DLL resource path: {}", e))
        })?;

    let dll_path = resource_path.to_string_lossy().to_string();
    info!("WhatsApp FFI DLL path: {}", dll_path);

    Ok(dll_path)
}

/// Get all available Tauri command handlers
pub fn get_command_handlers() -> impl Fn(Invoke) -> bool + Send + Sync + 'static {
    tauri::generate_handler![
        whatsapp_initialize,
        whatsapp_connect,
        whatsapp_disconnect,
        whatsapp_send_message,
        whatsapp_get_state,
        whatsapp_get_config,
        whatsapp_update_config,
        whatsapp_update_partial_config,
        whatsapp_is_connected,
        whatsapp_get_connection_status,
        whatsapp_get_stats,
        whatsapp_shutdown,
        whatsapp_start_event_processing,
        whatsapp_health_check,
        whatsapp_get_dll_path,
    ]
}
