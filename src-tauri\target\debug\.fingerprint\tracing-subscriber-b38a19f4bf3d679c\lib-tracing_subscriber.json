{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 15939273003786564609, "deps": [[1009387600818341822, "matchers", false, 18292945294381642908], [1017461770342116999, "sharded_slab", false, 7998385626293153321], [3722963349756955755, "once_cell", false, 16821080632637768006], [6048213226671835012, "smallvec", false, 1428031378608136440], [8606274917505247608, "tracing", false, 13752143806742742294], [8614575489689151157, "nu_ansi_term", false, 3153992907096629763], [9451456094439810778, "regex", false, 1995452313642458860], [10806489435541507125, "tracing_log", false, 12598937076718410457], [11033263105862272874, "tracing_core", false, 1941578643623782416], [12427285511609802057, "thread_local", false, 5452421818686238881]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-b38a19f4bf3d679c\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}