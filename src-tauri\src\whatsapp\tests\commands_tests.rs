//! Unit tests for Tauri command handlers

use std::sync::Arc;
use whatsapp_ffi_client::{ConnectionStatus, LogLevel};

use crate::whatsapp::{
    commands::{ConfigUpdateRequest, TauriError},
    events::{EventEmitter, MockEventEmitter},
    service::WhatsAppService,
    state::{ServiceConfig, ServiceStatus},
};

/// Create a test service for command testing
fn create_test_service_for_commands() -> Arc<WhatsAppService> {
    let config = ServiceConfig::new("test_library.dll".to_string(), "test.db".to_string());

    let event_emitter = Arc::new(EventEmitter::Mock(MockEventEmitter::new()));
    Arc::new(WhatsAppService::new(config, event_emitter))
}

#[tokio::test]
async fn test_whatsapp_service_state_access() {
    let service = create_test_service_for_commands();

    let state = service.get_state().await;
    assert_eq!(state.status(), ServiceStatus::NotInitialized);
    assert_eq!(state.connection_status(), ConnectionStatus::Disconnected);
}

#[tokio::test]
async fn test_whatsapp_service_config_access() {
    let service = create_test_service_for_commands();

    let config = service.get_config().await;
    assert_eq!(config.library_path, "test_library.dll");
    assert_eq!(config.db_path, "test.db");
}

#[tokio::test]
async fn test_whatsapp_service_connection_status() {
    let service = create_test_service_for_commands();

    let is_connected = service.is_connected().await;
    assert!(!is_connected); // Should not be connected initially
}

#[tokio::test]
async fn test_whatsapp_service_config_update() {
    let service = create_test_service_for_commands();

    let new_config = ServiceConfig::new(
        "src-tauri/Cargo.toml".to_string(), // Use existing file
        "new_test.db".to_string(),
    );

    let result = service.update_config(new_config.clone()).await;
    assert!(result.is_ok());

    // Verify config was updated
    let updated_config = service.get_config().await;
    assert_eq!(updated_config.library_path, "src-tauri/Cargo.toml");
    assert_eq!(updated_config.db_path, "new_test.db");
}

#[tokio::test]
async fn test_whatsapp_service_config_update_invalid() {
    let service = create_test_service_for_commands();

    let invalid_config = ServiceConfig::new(
        "".to_string(), // Invalid empty path
        "test.db".to_string(),
    );

    let result = service.update_config(invalid_config).await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_config_update_request_structure() {
    let updates = ConfigUpdateRequest {
        library_path: Some("new_library.dll".to_string()),
        db_path: None,
        log_level: Some(LogLevel::Debug),
        auto_reconnect: Some(false),
        max_reconnect_attempts: Some(15),
        reconnect_delay_seconds: None,
    };

    // Test that the structure is created correctly
    assert_eq!(updates.library_path, Some("new_library.dll".to_string()));
    assert!(updates.db_path.is_none());
    assert_eq!(updates.log_level, Some(LogLevel::Debug));
    assert_eq!(updates.auto_reconnect, Some(false));
    assert_eq!(updates.max_reconnect_attempts, Some(15));
    assert!(updates.reconnect_delay_seconds.is_none());
}

#[tokio::test]
async fn test_whatsapp_service_health_check() {
    let service = create_test_service_for_commands();

    let state = service.get_state().await;
    let config = service.get_config().await;

    assert!(state.is_healthy());
    assert!(!state.can_operate()); // Not connected
    assert_eq!(state.status(), ServiceStatus::NotInitialized);
    assert_eq!(state.connection_status(), ConnectionStatus::Disconnected);
    assert!(state.uptime().num_seconds() >= 0);
    assert!(state.last_error().is_none());
    assert!(config.validate().is_err()); // Config has invalid paths
}

#[test]
fn test_tauri_error_creation() {
    let error = TauriError::new("TEST_CODE", "Test message");
    assert_eq!(error.code, "TEST_CODE");
    assert_eq!(error.message, "Test message");
    assert!(error.details.is_none());
}

#[test]
fn test_tauri_error_with_details() {
    let error = TauriError::new("TEST_CODE", "Test message")._with_details("Additional details");

    assert_eq!(error.code, "TEST_CODE");
    assert_eq!(error.message, "Test message");
    assert_eq!(error.details, Some("Additional details".to_string()));
}

#[test]
fn test_tauri_error_convenience_methods() {
    let internal_error = TauriError::internal_error("Internal problem");
    assert_eq!(internal_error.code, "INTERNAL_ERROR");
    assert_eq!(internal_error.message, "Internal problem");

    let not_connected = TauriError::not_connected();
    assert_eq!(not_connected.code, "NOT_CONNECTED");

    let not_initialized = TauriError::_not_initialized();
    assert_eq!(not_initialized.code, "NOT_INITIALIZED");

    let invalid_input = TauriError::invalid_input("Bad input");
    assert_eq!(invalid_input.code, "INVALID_INPUT");
    assert_eq!(invalid_input.message, "Bad input");

    let connection_failed = TauriError::_connection_failed("Connection lost");
    assert_eq!(connection_failed.code, "CONNECTION_FAILED");
    assert_eq!(connection_failed.message, "Connection lost");
}

#[test]
fn test_whatsapp_error_conversion() {
    let whatsapp_error = whatsapp_ffi_client::WhatsAppError::NotConnected;
    let tauri_error: TauriError = whatsapp_error.into();

    assert_eq!(tauri_error.code, "NOT_CONNECTED");
    assert_eq!(tauri_error.message, "WhatsApp client is not connected");
}

#[test]
fn test_whatsapp_error_conversion_invalid_jid() {
    let whatsapp_error = whatsapp_ffi_client::WhatsAppError::InvalidJid {
        jid: "invalid_jid".to_string(),
        reason: "Bad format".to_string(),
    };
    let tauri_error: TauriError = whatsapp_error.into();

    assert_eq!(tauri_error.code, "INVALID_INPUT");
    assert!(tauri_error.message.contains("invalid_jid"));
    assert!(tauri_error.message.contains("Bad format"));
}

#[test]
fn test_whatsapp_error_conversion_send_failed() {
    let whatsapp_error = whatsapp_ffi_client::WhatsAppError::SendFailed {
        jid: "1234567890".to_string(),
        reason: "Network error".to_string(),
    };
    let tauri_error: TauriError = whatsapp_error.into();

    assert_eq!(tauri_error.code, "SEND_FAILED");
    assert!(tauri_error.message.contains("1234567890"));
    assert!(tauri_error.message.contains("Network error"));
}

#[test]
fn test_whatsapp_error_conversion_timeout() {
    let whatsapp_error = whatsapp_ffi_client::WhatsAppError::Timeout {
        operation: "connect".to_string(),
        duration_ms: 5000,
    };
    let tauri_error: TauriError = whatsapp_error.into();

    assert_eq!(tauri_error.code, "TIMEOUT");
    assert!(tauri_error.message.contains("connect"));
    assert!(tauri_error.message.contains("5000ms"));
}

#[test]
fn test_whatsapp_error_conversion_already_connected() {
    let whatsapp_error = whatsapp_ffi_client::WhatsAppError::AlreadyConnected;
    let tauri_error: TauriError = whatsapp_error.into();

    assert_eq!(tauri_error.code, "ALREADY_CONNECTED");
    assert_eq!(tauri_error.message, "Already connected to WhatsApp");
}

#[test]
fn test_whatsapp_error_conversion_shutting_down() {
    let whatsapp_error = whatsapp_ffi_client::WhatsAppError::ShuttingDown;
    let tauri_error: TauriError = whatsapp_error.into();

    assert_eq!(tauri_error.code, "SHUTTING_DOWN");
    assert_eq!(tauri_error.message, "Service is shutting down");
}

#[test]
fn test_config_update_request_partial() {
    let request = ConfigUpdateRequest {
        library_path: Some("new_lib.dll".to_string()),
        db_path: None,
        log_level: Some(LogLevel::Error),
        auto_reconnect: None,
        max_reconnect_attempts: Some(10),
        reconnect_delay_seconds: None,
    };

    assert_eq!(request.library_path, Some("new_lib.dll".to_string()));
    assert!(request.db_path.is_none());
    assert_eq!(request.log_level, Some(LogLevel::Error));
    assert!(request.auto_reconnect.is_none());
    assert_eq!(request.max_reconnect_attempts, Some(10));
    assert!(request.reconnect_delay_seconds.is_none());
}

#[test]
fn test_config_update_request_all_none() {
    let request = ConfigUpdateRequest {
        library_path: None,
        db_path: None,
        log_level: None,
        auto_reconnect: None,
        max_reconnect_attempts: None,
        reconnect_delay_seconds: None,
    };

    assert!(request.library_path.is_none());
    assert!(request.db_path.is_none());
    assert!(request.log_level.is_none());
    assert!(request.auto_reconnect.is_none());
    assert!(request.max_reconnect_attempts.is_none());
    assert!(request.reconnect_delay_seconds.is_none());
}
