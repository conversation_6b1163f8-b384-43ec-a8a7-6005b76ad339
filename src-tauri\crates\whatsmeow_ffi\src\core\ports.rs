use async_trait::async_trait;
use std::any::Any;

use super::{entities::LogLevel, errors::Result};

/// Port for WhatsApp client operations
#[async_trait]
pub trait WhatsAppClientPort: Send + Sync {
    /// Connect to WhatsApp
    async fn connect(&self) -> Result<()>;

    /// Disconnect from WhatsApp
    async fn disconnect(&self) -> Result<()>;

    /// Send a text message
    async fn send_message(&self, jid: &str, text: &str) -> Result<()>;

    /// Check if the client is connected
    async fn is_connected(&self) -> Result<bool>;

    /// Check if events are available (implementation-specific)
    fn has_events(&self) -> bool;

    /// Set global log level for all WhatsApp clients
    fn set_global_log_level(&self, level: LogLevel) -> Result<()>;

    /// Get current global log level
    fn get_global_log_level(&self) -> Result<LogLevel>;

    /// Set log level for this specific client
    fn set_client_log_level(&self, level: Log<PERSON>evel) -> Result<()>;

    /// Get log level for this specific client
    fn get_client_log_level(&self) -> Result<LogLevel>;

    /// Downcast to concrete type for advanced operations
    fn as_any(&self) -> &dyn Any;
}

/// Port for FFI operations
pub trait FfiPort: Send + Sync {
    /// Connect the client
    fn connect(&self, handle: usize) -> Result<()>;

    /// Disconnect the client
    fn disconnect(&self, handle: usize) -> Result<()>;

    /// Send a message
    fn send_message(&self, handle: usize, jid: &str, text: &str) -> Result<()>;

    /// Check connection status
    fn is_connected(&self, handle: usize) -> Result<bool>;

    /// Destroy the client
    fn destroy_client(&self, handle: usize) -> Result<()>;
}
