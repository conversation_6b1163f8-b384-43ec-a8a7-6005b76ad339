import { Outlet, createRootRouteWithContext } from '@tanstack/react-router';

import { Toaster } from '@/components/ui/sonner';
import type { QueryClient } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

interface MyRouterContext {
  queryClient: QueryClient;
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  component: RootComponent,
});

function RootComponent() {
  return (
    <>
      <Outlet />

      {/* Global UI Components */}
      <ReactQueryDevtools buttonPosition="bottom-right" />
      <Toaster theme="light" richColors expand />
    </>
  );
}
