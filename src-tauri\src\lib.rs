//! WhatsApp Sender Pro - Main application library.
//!
//! This is the main library crate for the WhatsApp Sender Pro desktop application.
//! It provides the core functionality for initializing and running the Tauri-based
//! desktop application with database services and state management.

#![deny(missing_docs)]

mod prelude;
mod state;
mod whatsapp;

use state::try_init_state;
use std::sync::Arc;
use tauri::Manager;
use whatsapp::{
    commands::get_command_handlers,
    events::{EventEmitter, TauriEventEmitterImpl},
    service::WhatsAppService,
    state::ServiceConfig,
};

/// Runs the main application.
///
/// This function initializes the application state, sets up the database services,
/// and starts the Tauri application with all necessary plugins and handlers.
///
/// # Panics
///
/// This function will panic if:
/// - The application state fails to initialize
/// - The Tauri application fails to start
/// - Database initialization fails
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub async fn run() {
    // Set up services
    let app_state = try_init_state()
        .await
        .expect("Failed to initialize application state");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .setup(|app| {
            // Initialize WhatsApp service
            let app_handle = app.handle().clone();

            // Create default WhatsApp service configuration
            let whatsapp_config =
                ServiceConfig::new("whatsmeow_ffi.dll".to_string(), "whatsapp.db".to_string());

            // Create event emitter
            let tauri_emitter = TauriEventEmitterImpl::new(app_handle);
            let event_emitter = Arc::new(EventEmitter::Tauri(tauri_emitter));

            // Create WhatsApp service
            let whatsapp_service = Arc::new(WhatsAppService::new(whatsapp_config, event_emitter));

            // Manage the WhatsApp service in Tauri state
            app.manage(whatsapp_service);

            Ok(())
        })
        .invoke_handler(get_command_handlers())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
