//! WhatsApp Sender Pro - Main application library.
//!
//! This is the main library crate for the WhatsApp Sender Pro desktop application.
//! It provides the core functionality for initializing and running the Tauri-based
//! desktop application with database services and state management.

#![deny(missing_docs)]

mod prelude;
mod state;

use state::try_init_state;

/// Runs the main application.
///
/// This function initializes the application state, sets up the database services,
/// and starts the Tauri application with all necessary plugins and handlers.
///
/// # Panics
///
/// This function will panic if:
/// - The application state fails to initialize
/// - The Tauri application fails to start
/// - Database initialization fails
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub async fn run() {
    // Set up services
    let app_state = try_init_state()
        .await
        .expect("Failed to initialize application state");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
