use crate::prelude::AppResult;
use db_service::{ServiceManager, setup_services};
use derive_getters::Getters;
use tracing::{error, info, instrument};
use typed_builder::TypedBuilder;

#[derive(TypedBuilder, Getters)]
pub struct AppState {
    #[builder(setter(into))]
    service_manager: ServiceManager,
}

impl AppState {
    /// Cleanup state before shutdown
    #[instrument(skip(self))]
    pub async fn cleanup(&self) -> AppResult<()> {
        info!("Cleaning up application state...");

        // Add cleanup logic here
        // For example:
        // - Save application settings
        // - Close file handles
        // - Cancel running tasks

        Ok(())
    }
}

#[instrument]
pub async fn try_init_state() -> AppResult<AppState> {
    info!("Attempting to initialize application state");

    let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL must be set");

    info!("Initializing database connection");
    match setup_services(&database_url).await {
        Ok(manager) => Ok(AppState::builder().service_manager(manager).build()),
        Err(e) => {
            error!("Failed to setup services: {:?}", e);
            Err(e.into())
        }
    }
}
