{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 15449633999378378596, "deps": [[1988483478007900009, "unicode_ident", false, 11534298646957259127], [3060637413840920116, "proc_macro2", false, 13833104526989948624], [17990358020177143287, "quote", false, 7153210784266032170]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-ec41d369d3a94c5d\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}