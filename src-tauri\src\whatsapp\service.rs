//! WhatsApp service implementation for managing client lifecycle and operations

use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tracing::{error, info, instrument, warn};
use whatsapp_ffi_client::{
    ConnectionStatus, WhatsAppClient, WhatsAppClientBuilder, WhatsAppError, WhatsAppEvent,
};

use super::events::EventEmitter;
use super::state::{ServiceConfig, ServiceState, ServiceStatus};

/// WhatsApp service for managing client lifecycle and operations
pub struct WhatsAppService {
    /// Current service state
    state: Arc<RwLock<ServiceState>>,
    /// WhatsApp client instance (None when not initialized)
    client: Arc<Mutex<Option<WhatsAppClient>>>,
    /// Event emitter for sending events to frontend
    event_emitter: Arc<EventEmitter>,
    /// Service configuration
    config: Arc<RwLock<ServiceConfig>>,
}

impl WhatsAppService {
    /// Create a new WhatsApp service
    pub fn new(config: ServiceConfig, event_emitter: Arc<EventEmitter>) -> Self {
        Self {
            state: Arc::new(RwLock::new(ServiceState::new())),
            client: Arc::new(Mutex::new(None)),
            event_emitter,
            config: Arc::new(RwLock::new(config)),
        }
    }

    /// Initialize the WhatsApp client with current configuration
    #[instrument(skip(self))]
    pub async fn initialize(&self) -> Result<(), WhatsAppError> {
        info!("Initializing WhatsApp service");

        // Update state to initializing
        {
            let mut state = self.state.write().await;
            state.set_status(ServiceStatus::Initializing);
        }

        let config = self.config.read().await;

        // Build the WhatsApp client
        let client = WhatsAppClientBuilder::new()
            .library_path(&config.library_path)
            .db_path(&config.db_path)
            .with_log_level(config.log_level)
            .build()
            .map_err(|e| {
                error!("Failed to build WhatsApp client: {}", e);
                e
            })?;

        // Store the client
        {
            let mut client_guard = self.client.lock().await;
            *client_guard = Some(client);
        }

        // Update state to initialized
        {
            let mut state = self.state.write().await;
            state.set_status(ServiceStatus::Initialized);
        }

        info!("WhatsApp service initialized successfully");

        // Emit initialization event
        self.event_emitter
            .emit_service_status_changed(ServiceStatus::Initialized)
            .await;

        Ok(())
    }

    /// Connect to WhatsApp
    #[instrument(skip(self))]
    pub async fn connect(&self) -> Result<(), WhatsAppError> {
        info!("Connecting to WhatsApp");

        // Check if client is initialized
        {
            let client_guard = self.client.lock().await;
            if client_guard.is_none() {
                return Err(WhatsAppError::Internal(
                    "Client not initialized".to_string(),
                ));
            }
        }

        // Update state to connecting
        {
            let mut state = self.state.write().await;
            state.set_status(ServiceStatus::Connecting);
        }

        // Emit connecting event
        self.event_emitter
            .emit_service_status_changed(ServiceStatus::Connecting)
            .await;

        // Attempt connection
        let result = {
            let client_guard = self.client.lock().await;
            if let Some(client) = client_guard.as_ref() {
                client.connect().await
            } else {
                return Err(WhatsAppError::Internal(
                    "Client not initialized".to_string(),
                ));
            }
        };

        match result {
            Ok(()) => {
                let mut state = self.state.write().await;
                state.set_status(ServiceStatus::Connected);
                state.set_connection_status(ConnectionStatus::Connected);

                info!("Successfully connected to WhatsApp");

                // Emit connected event
                self.event_emitter
                    .emit_service_status_changed(ServiceStatus::Connected)
                    .await;

                Ok(())
            }
            Err(e) => {
                error!("Failed to connect to WhatsApp: {}", e);

                let mut state = self.state.write().await;
                state.set_status(ServiceStatus::Error);
                state.set_last_error(Some(e.to_string()));

                // Emit error event
                self.event_emitter.emit_service_error(&e.to_string()).await;

                Err(e)
            }
        }
    }

    /// Disconnect from WhatsApp
    #[instrument(skip(self))]
    pub async fn disconnect(&self) -> Result<(), WhatsAppError> {
        info!("Disconnecting from WhatsApp");

        // Check if client is initialized
        {
            let client_guard = self.client.lock().await;
            if client_guard.is_none() {
                return Err(WhatsAppError::Internal(
                    "Client not initialized".to_string(),
                ));
            }
        }

        // Update state to disconnecting
        {
            let mut state = self.state.write().await;
            state.set_status(ServiceStatus::Disconnecting);
        }

        // Emit disconnecting event
        self.event_emitter
            .emit_service_status_changed(ServiceStatus::Disconnecting)
            .await;

        // Attempt disconnection
        let result = {
            let client_guard = self.client.lock().await;
            if let Some(client) = client_guard.as_ref() {
                client.disconnect().await
            } else {
                return Err(WhatsAppError::Internal(
                    "Client not initialized".to_string(),
                ));
            }
        };

        match result {
            Ok(()) => {
                let mut state = self.state.write().await;
                state.set_status(ServiceStatus::Disconnected);
                state.set_connection_status(ConnectionStatus::Disconnected);

                info!("Successfully disconnected from WhatsApp");

                // Emit disconnected event
                self.event_emitter
                    .emit_service_status_changed(ServiceStatus::Disconnected)
                    .await;

                Ok(())
            }
            Err(e) => {
                error!("Failed to disconnect from WhatsApp: {}", e);

                let mut state = self.state.write().await;
                state.set_last_error(Some(e.to_string()));

                // Emit error event
                self.event_emitter.emit_service_error(&e.to_string()).await;

                Err(e)
            }
        }
    }

    /// Send a text message
    #[instrument(skip(self, message))]
    pub async fn send_message(
        &self,
        recipient: &str,
        message: &str,
    ) -> Result<String, WhatsAppError> {
        info!("Sending message to {}", recipient);

        // Check if client is initialized
        {
            let client_guard = self.client.lock().await;
            if client_guard.is_none() {
                return Err(WhatsAppError::Internal(
                    "Client not initialized".to_string(),
                ));
            }
        }

        // Check if connected
        let is_connected = {
            let state = self.state.read().await;
            matches!(state.status(), ServiceStatus::Connected)
        };

        if !is_connected {
            return Err(WhatsAppError::NotConnected);
        }

        // Send the message
        let result = {
            let client_guard = self.client.lock().await;
            if let Some(client) = client_guard.as_ref() {
                client.send_message(recipient, message).await
            } else {
                return Err(WhatsAppError::Internal(
                    "Client not initialized".to_string(),
                ));
            }
        };

        match result {
            Ok(()) => {
                info!("Message sent successfully to {}", recipient);

                // Generate a message ID for tracking (in real implementation, this would come from the client)
                let message_id = format!("msg_{}", chrono::Utc::now().timestamp_millis());

                // Emit message sent event
                self.event_emitter
                    .emit_message_sent(recipient, message, &message_id)
                    .await;

                Ok(message_id)
            }
            Err(e) => {
                error!("Failed to send message to {}: {}", recipient, e);

                // Emit error event
                self.event_emitter
                    .emit_service_error(&format!("Failed to send message: {}", e))
                    .await;

                Err(e)
            }
        }
    }

    /// Get current service state
    pub async fn get_state(&self) -> ServiceState {
        self.state.read().await.clone()
    }

    /// Get current service configuration
    pub async fn get_config(&self) -> ServiceConfig {
        self.config.read().await.clone()
    }

    /// Update service configuration
    #[instrument(skip(self, new_config))]
    pub async fn update_config(&self, new_config: ServiceConfig) -> Result<(), WhatsAppError> {
        info!("Updating service configuration");
        
        // Validate configuration
        new_config
            .validate()
            .map_err(|e| WhatsAppError::Internal(format!("Invalid configuration: {}", e)))?;

        // Update configuration
        {
            let mut config = self.config.write().await;
            *config = new_config;
        }

        info!("Service configuration updated successfully");
        Ok(())
    }

    /// Check if the service is connected
    pub async fn is_connected(&self) -> bool {
        let state = self.state.read().await;
        matches!(state.status(), ServiceStatus::Connected)
    }

    /// Get connection status
    pub async fn get_connection_status(&self) -> ConnectionStatus {
        let state = self.state.read().await;
        state.connection_status()
    }

    /// Shutdown the service and cleanup resources
    #[instrument(skip(self))]
    pub async fn shutdown(&self) -> Result<(), WhatsAppError> {
        info!("Shutting down WhatsApp service");

        // Update state to shutting down
        {
            let mut state = self.state.write().await;
            state.set_status(ServiceStatus::ShuttingDown);
        }

        // Emit shutting down event
        self.event_emitter
            .emit_service_status_changed(ServiceStatus::ShuttingDown)
            .await;

        // Disconnect if connected
        if self.is_connected().await {
            if let Err(e) = self.disconnect().await {
                warn!("Error during disconnect in shutdown: {}", e);
            }
        }

        // Destroy the client
        {
            let mut client_guard = self.client.lock().await;
            if let Some(client) = client_guard.take() {
                if let Err(e) = client.destroy().await {
                    warn!("Error destroying client during shutdown: {}", e);
                }
            }
        }

        // Update state to shutdown
        {
            let mut state = self.state.write().await;
            state.set_status(ServiceStatus::Shutdown);
        }

        info!("WhatsApp service shutdown completed");

        // Emit shutdown event
        self.event_emitter
            .emit_service_status_changed(ServiceStatus::Shutdown)
            .await;

        Ok(())
    }

    /// Start event processing loop
    #[instrument(skip(self))]
    pub async fn start_event_processing(&self) -> Result<(), WhatsAppError> {
        info!("Starting event processing");

        // Get event stream from client
        let event_stream = {
            let mut client_guard = self.client.lock().await;
            if let Some(client) = client_guard.as_mut() {
                client.event_stream()
            } else {
                return Err(WhatsAppError::Internal(
                    "Client not initialized".to_string(),
                ));
            }
        };

        if let Some(mut event_stream) = event_stream {
            let event_emitter = Arc::clone(&self.event_emitter);
            let state = Arc::clone(&self.state);

            // Spawn event processing task
            tokio::spawn(async move {
                info!("Event processing loop started");

                while let Some(event) = event_stream.recv().await {
                    match event {
                        WhatsAppEvent::QRCode(qr_code) => {
                            info!("Received QR code event");
                            event_emitter.emit_qr_code(&qr_code).await;
                        }
                        WhatsAppEvent::MessageReceived(message) => {
                            info!("Received message from {}", message.from);
                            event_emitter.emit_message_received(&message).await;
                        }
                        WhatsAppEvent::ConnectionStatusChanged { status, reason } => {
                            info!("Connection status changed to {:?}", status);

                            // Update internal state
                            {
                                let mut state_guard = state.write().await;
                                state_guard.set_connection_status(status);

                                // Update service status based on connection status
                                let service_status = match status {
                                    ConnectionStatus::Connected => ServiceStatus::Connected,
                                    ConnectionStatus::Connecting => ServiceStatus::Connecting,
                                    ConnectionStatus::Disconnected => ServiceStatus::Disconnected,
                                    ConnectionStatus::LoggedOut => ServiceStatus::Disconnected,
                                };
                                state_guard.set_status(service_status);
                            }

                            event_emitter
                                .emit_connection_status_changed(status, reason)
                                .await;
                        }
                        WhatsAppEvent::Error { code, message } => {
                            error!("Received error event: {} - {}", code, message);

                            // Update state with error
                            {
                                let mut state_guard = state.write().await;
                                state_guard.set_status(ServiceStatus::Error);
                                state_guard.set_last_error(Some(message.clone()));
                            }

                            event_emitter.emit_service_error(&message).await;
                        }
                    }
                }

                info!("Event processing loop ended");
            });
        }

        Ok(())
    }
}

impl Drop for WhatsAppService {
    fn drop(&mut self) {
        // Note: We can't call async methods in Drop, so we just log
        info!("WhatsAppService is being dropped");
    }
}
