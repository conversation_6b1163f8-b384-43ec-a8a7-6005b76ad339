package main

/*
#cgo CFLAGS: -I.
#include "cdefs.h"
*/
import "C"

import (
	"unsafe"
)

//export whatsapp_create_client
func whatsapp_create_client(dbPath *C.char, callback C.EventCallback, userData unsafe.Pointer) C.uintptr_t {
	goDbPath := C.GoString(dbPath)

	client, err := NewWhatsAppClient(goDbPath, callback, userData)
	if err != nil {
		// Return 0 to indicate error
		return 0
	}

	handle := registerClient(client)
	return C.uintptr_t(handle)
}

//export whatsapp_connect
func whatsapp_connect(handle C.uintptr_t) C.int {
	client := getClient(uintptr(handle))
	if client == nil {
		return C.int(ErrorInvalidHandle)
	}

	err := client.Connect()
	if err != nil {
		return C.int(ErrorInternal)
	}

	return C.int(ErrorNone)
}

//export whatsapp_disconnect
func whatsapp_disconnect(handle C.uintptr_t) C.int {
	client := getClient(uintptr(handle))
	if client == nil {
		return C.int(ErrorInvalidHandle)
	}

	client.Disconnect()
	return C.int(ErrorNone)
}

//export whatsapp_send_message
func whatsapp_send_message(handle C.uintptr_t, jid *C.char, text *C.char) C.int {
	client := getClient(uintptr(handle))
	if client == nil {
		return C.int(ErrorInvalidHandle)
	}

	if !client.IsConnected() {
		return C.int(ErrorNotConnected)
	}

	goJid := C.GoString(jid)
	goText := C.GoString(text)

	err := client.SendMessage(goJid, goText)
	if err != nil {
		return C.int(ErrorSendFailed)
	}

	return C.int(ErrorNone)
}

//export whatsapp_is_connected
func whatsapp_is_connected(handle C.uintptr_t) C.int {
	client := getClient(uintptr(handle))
	if client == nil {
		return 0
	}

	if client.IsConnected() {
		return 1
	}
	return 0
}

//export whatsapp_destroy_client
func whatsapp_destroy_client(handle C.uintptr_t) C.int {
	client := getClient(uintptr(handle))
	if client == nil {
		return C.int(ErrorInvalidHandle)
	}

	client.Cleanup()

	unregisterClient(uintptr(handle))
	return C.int(ErrorNone)
}

//export whatsapp_get_error_message
func whatsapp_get_error_message(errorCode C.int) *C.char {
	var message string

	switch int(errorCode) {
	case int(ErrorNone):
		message = "No error"
	case int(ErrorInvalidHandle):
		message = "Invalid client handle"
	case int(ErrorNotConnected):
		message = "Client not connected"
	case int(ErrorInvalidJID):
		message = "Invalid JID format"
	case int(ErrorSendFailed):
		message = "Failed to send message"
	case int(ErrorInternal):
		message = "Internal error"
	default:
		message = "Unknown error"
	}

	return C.CString(message)
}

//export whatsapp_free_string
func whatsapp_free_string(str *C.char) {
	C.free(unsafe.Pointer(str))
}

//export whatsapp_set_log_level
func whatsapp_set_log_level(level C.int) C.int {
	logLevel := LogLevel(level)

	SetGlobalLogLevel(logLevel)

	return C.int(ErrorNone)
}

//export whatsapp_get_log_level
func whatsapp_get_log_level() C.int {
	level := GetGlobalLogLevel()
	return C.int(level)
}

//export whatsapp_set_client_log_level
func whatsapp_set_client_log_level(handle C.uintptr_t, level C.int) C.int {

	client := getClient(uintptr(handle))
	if client == nil {
		return C.int(ErrorInvalidHandle)
	}

	logLevel := LogLevel(level)
	client.logger.SetLevel(logLevel)

	return C.int(ErrorNone)
}

//export whatsapp_get_client_log_level
func whatsapp_get_client_log_level(handle C.uintptr_t) C.int {
	client := getClient(uintptr(handle))
	if client == nil {
		return C.int(ErrorInvalidHandle)
	}

	level := client.logger.GetLevel()
	return C.int(level)
}

// Initialize global logger on library load
func init() {
	// Initialize with INFO level by default, can be changed via FFI
	InitializeLogger(LogLevelInfo, nil)
}

// Required for shared library
func main() {}
