# Design Document

## Overview

This design document outlines the architecture and user experience for a beautiful, modern WhatsApp dashboard frontend specifically designed for pharmacy businesses. The application will provide an intuitive interface for managing WhatsApp campaigns, patient communications, and healthcare messaging using React 19, TypeScript, Zustand, TanStack Query, and Shadcn UI components.

The design emphasizes user experience, performance, and pharmacy-specific workflows while maintaining a professional and accessible interface.

## Architecture

### Frontend Architecture

```mermaid
graph TB
    subgraph "React Application"
        subgraph "Pages & Routes"
            D[Dashboard]
            C[Campaigns]
            P[Patients]
            T[Templates]
            M[Media]
            S[Settings]
        end
        
        subgraph "Components"
            UI[Shadcn UI Components]
            CC[Custom Components]
            L[Layout Components]
        end
        
        subgraph "State Management"
            ZS[Zustand Stores]
            TQ[TanStack Query]
            LS[Local State]
        end
        
        subgraph "Services"
            API[API Services]
            WS[WebSocket Service]
            FS[File Service]
        end
    end
    
    subgraph "Tauri Backend"
        TC[Tauri Commands]
        TE[Tauri Events]
        WA[WhatsApp API]
    end
    
    D --> ZS
    C --> ZS
    P --> ZS
    T --> ZS
    M --> ZS
    
    ZS --> TQ
    TQ --> API
    API --> TC
    
    WS --> TE
    TE --> ZS
    
    UI --> CC
    CC --> L
```

### Component Hierarchy

The application follows a hierarchical component structure with clear separation of concerns:

1. **App Shell**: Main layout with navigation and global state
2. **Page Components**: Route-specific containers for major features
3. **Feature Components**: Specialized components for campaigns, patients, etc.
4. **UI Components**: Reusable Shadcn UI components and custom variants
5. **Utility Components**: Loading states, error boundaries, and helpers

## Components and Interfaces

### 1. Application Shell & Layout

**Location**: `src/components/layout/`

```typescript
interface AppShellProps {
  children: React.ReactNode;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType;
  href: string;
  badge?: number;
  isActive?: boolean;
}

interface SidebarProps {
  navigation: NavigationItem[];
  isCollapsed: boolean;
  onToggle: () => void;
}
```

**Key Features**:
- Responsive sidebar navigation with collapsible design
- Header with connection status, notifications, and user menu
- Breadcrumb navigation for deep pages
- Global search functionality
- Theme switcher (light/dark mode)

### 2. Dashboard Components

**Location**: `src/components/dashboard/`

```typescript
interface DashboardMetrics {
  totalPatients: number;
  activeCampaigns: number;
  messagesDelivered: number;
  engagementRate: number;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  trend?: 'up' | 'down' | 'neutral';
  icon: React.ComponentType;
  color?: 'blue' | 'green' | 'orange' | 'red';
}

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }[];
}
```

**Dashboard Sections**:
- **Metrics Overview**: Key performance indicators with trend indicators
- **Campaign Performance**: Interactive charts showing delivery rates and engagement
- **Recent Activity**: Timeline of recent campaigns and patient interactions
- **Quick Actions**: Shortcuts to create campaigns, add patients, and send messages
- **Connection Status**: Real-time WhatsApp connection monitoring

### 3. Campaign Management

**Location**: `src/components/campaigns/`

```typescript
interface Campaign {
  id: string;
  name: string;
  type: 'medication_reminder' | 'prescription_ready' | 'health_tip' | 'promotion' | 'emergency';
  status: 'draft' | 'scheduled' | 'running' | 'completed' | 'paused';
  recipients: PatientGroup[];
  message: MessageContent;
  schedule: CampaignSchedule;
  metrics: CampaignMetrics;
  createdAt: Date;
  updatedAt: Date;
}

interface CampaignSchedule {
  type: 'immediate' | 'scheduled' | 'recurring';
  sendAt?: Date;
  recurrence?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: Date;
  };
}

interface MessageContent {
  type: 'text' | 'image' | 'document' | 'video';
  text?: string;
  media?: MediaFile;
  template?: MessageTemplate;
}
```

**Campaign Features**:
- **Campaign Builder**: Step-by-step wizard for creating campaigns
- **Template Selection**: Pre-built templates for pharmacy communications
- **Recipient Targeting**: Advanced filtering and patient group selection
- **Scheduling**: Flexible scheduling with recurring options
- **Preview**: Real-time message preview with sample data
- **Analytics**: Detailed campaign performance metrics

### 4. Patient Management

**Location**: `src/components/patients/`

```typescript
interface Patient {
  id: string;
  name: string;
  phoneNumber: string;
  email?: string;
  dateOfBirth?: Date;
  medicalConditions: string[];
  currentMedications: Medication[];
  allergies: string[];
  communicationPreferences: CommunicationPreferences;
  groups: PatientGroup[];
  lastContact?: Date;
  engagementScore: number;
  notes: string;
}

interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  startDate: Date;
  endDate?: Date;
  prescribingDoctor?: string;
  instructions: string;
}

interface PatientGroup {
  id: string;
  name: string;
  description: string;
  criteria: GroupCriteria;
  patientCount: number;
  color: string;
}
```

**Patient Features**:
- **Patient Database**: Comprehensive patient information management
- **Medical History**: Medication tracking and health condition management
- **Group Management**: Dynamic patient grouping with custom criteria
- **Communication History**: Complete message history and engagement tracking
- **Import/Export**: CSV import with data validation and duplicate detection

### 5. Message Templates

**Location**: `src/components/templates/`

```typescript
interface MessageTemplate {
  id: string;
  name: string;
  category: 'medication' | 'prescription' | 'health_tip' | 'appointment' | 'emergency' | 'promotion';
  content: string;
  variables: TemplateVariable[];
  media?: MediaFile;
  compliance: ComplianceInfo;
  usage: TemplateUsage;
  isActive: boolean;
}

interface TemplateVariable {
  name: string;
  type: 'text' | 'date' | 'number';
  required: boolean;
  defaultValue?: string;
  description: string;
}

interface ComplianceInfo {
  isCompliant: boolean;
  guidelines: string[];
  warnings: string[];
  lastReviewed: Date;
}
```

**Template Features**:
- **Template Library**: Categorized templates for different communication types
- **Variable System**: Dynamic content with patient-specific information
- **Compliance Checker**: Healthcare communication guidelines validation
- **Preview System**: Real-time template preview with sample data
- **Usage Analytics**: Template performance and usage statistics

### 6. Media Management

**Location**: `src/components/media/`

```typescript
interface MediaFile {
  id: string;
  name: string;
  type: 'image' | 'document' | 'video' | 'audio';
  mimeType: string;
  size: number;
  url: string;
  thumbnail?: string;
  folder: string;
  tags: string[];
  uploadedAt: Date;
  usageCount: number;
}

interface MediaFolder {
  id: string;
  name: string;
  parentId?: string;
  fileCount: number;
  color: string;
}
```

**Media Features**:
- **File Upload**: Drag-and-drop upload with progress indicators
- **Organization**: Folder structure with tagging and search
- **Preview**: Built-in media preview for all supported formats
- **Optimization**: Automatic compression and format conversion
- **Usage Tracking**: Media usage analytics and optimization suggestions

## State Management with Zustand

### Store Architecture

```typescript
// Main application store
interface AppStore {
  // UI State
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
  
  // Connection State
  whatsappStatus: ConnectionStatus;
  lastConnected?: Date;
  
  // Actions
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
  addNotification: (notification: Notification) => void;
  updateConnectionStatus: (status: ConnectionStatus) => void;
}

// Campaign store
interface CampaignStore {
  campaigns: Campaign[];
  activeCampaign?: Campaign;
  filters: CampaignFilters;
  
  // Actions
  setCampaigns: (campaigns: Campaign[]) => void;
  addCampaign: (campaign: Campaign) => void;
  updateCampaign: (id: string, updates: Partial<Campaign>) => void;
  deleteCampaign: (id: string) => void;
  setActiveCampaign: (campaign: Campaign) => void;
  setFilters: (filters: CampaignFilters) => void;
}

// Patient store
interface PatientStore {
  patients: Patient[];
  groups: PatientGroup[];
  selectedPatients: string[];
  searchQuery: string;
  
  // Actions
  setPatients: (patients: Patient[]) => void;
  addPatient: (patient: Patient) => void;
  updatePatient: (id: string, updates: Partial<Patient>) => void;
  deletePatient: (id: string) => void;
  setSelectedPatients: (ids: string[]) => void;
  setSearchQuery: (query: string) => void;
}
```

### Store Integration

- **Persistence**: Automatic state persistence for user preferences
- **Optimistic Updates**: Immediate UI updates with rollback on failure
- **Real-time Sync**: WebSocket integration for live data updates
- **Error Handling**: Centralized error state management

## Data Fetching with TanStack Query

### Query Configuration

```typescript
// Campaign queries
const useCampaigns = () => {
  return useQuery({
    queryKey: ['campaigns'],
    queryFn: fetchCampaigns,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

const useCampaignMetrics = (campaignId: string) => {
  return useQuery({
    queryKey: ['campaign-metrics', campaignId],
    queryFn: () => fetchCampaignMetrics(campaignId),
    refetchInterval: 30 * 1000, // 30 seconds for real-time updates
  });
};

// Patient queries
const usePatients = (filters: PatientFilters) => {
  return useQuery({
    queryKey: ['patients', filters],
    queryFn: () => fetchPatients(filters),
    keepPreviousData: true,
  });
};

// Mutations
const useCreateCampaign = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createCampaign,
    onSuccess: () => {
      queryClient.invalidateQueries(['campaigns']);
    },
    onError: (error) => {
      // Handle error with toast notification
    },
  });
};
```

### Caching Strategy

- **Background Updates**: Automatic data refresh for critical information
- **Optimistic Updates**: Immediate UI feedback for user actions
- **Error Recovery**: Automatic retry with exponential backoff
- **Offline Support**: Cached data availability when offline

## UI Design System

### Shadcn UI Integration

**Core Components**:
- **Button**: Primary, secondary, outline, ghost variants with loading states
- **Card**: Content containers with headers, footers, and actions
- **Table**: Data tables with sorting, filtering, and pagination
- **Form**: Form controls with validation and error handling
- **Dialog**: Modal dialogs for confirmations and detailed views
- **Sheet**: Slide-out panels for quick actions and details
- **Toast**: Notification system for feedback and alerts

**Custom Component Extensions**:
```typescript
// Enhanced metric card
interface MetricCardProps extends CardProps {
  metric: {
    label: string;
    value: string | number;
    change?: number;
    trend?: 'up' | 'down' | 'neutral';
  };
  icon: React.ComponentType;
  color?: 'blue' | 'green' | 'orange' | 'red';
}

// Campaign status badge
interface StatusBadgeProps {
  status: Campaign['status'];
  size?: 'sm' | 'md' | 'lg';
}

// Patient avatar with status
interface PatientAvatarProps {
  patient: Patient;
  showStatus?: boolean;
  size?: 'sm' | 'md' | 'lg';
}
```

### Theme System

**Color Palette**:
- **Primary**: Medical blue (#0066CC) for primary actions and branding
- **Secondary**: Healthcare green (#00AA44) for success states and health-related content
- **Warning**: Amber (#FF8800) for cautions and pending states
- **Error**: Medical red (#CC0000) for errors and critical alerts
- **Neutral**: Gray scale for text and backgrounds

**Typography**:
- **Headings**: Inter font family for clarity and professionalism
- **Body**: System font stack for optimal readability
- **Code**: Monospace for technical content and IDs

## Responsive Design

### Breakpoint Strategy

```typescript
const breakpoints = {
  sm: '640px',   // Small tablets
  md: '768px',   // Large tablets
  lg: '1024px',  // Small desktops
  xl: '1280px',  // Large desktops
  '2xl': '1536px' // Extra large screens
};
```

### Layout Adaptations

- **Desktop First**: Optimized for desktop pharmacy workstations
- **Sidebar**: Collapsible navigation for space efficiency
- **Tables**: Responsive tables with horizontal scrolling and column hiding
- **Modals**: Adaptive modal sizing based on screen real estate
- **Charts**: Responsive charts that maintain readability across screen sizes

## Performance Optimization

### Code Splitting

```typescript
// Route-based code splitting
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Campaigns = lazy(() => import('./pages/Campaigns'));
const Patients = lazy(() => import('./pages/Patients'));

// Component-based splitting for heavy components
const CampaignBuilder = lazy(() => import('./components/campaigns/CampaignBuilder'));
const PatientImporter = lazy(() => import('./components/patients/PatientImporter'));
```

### Optimization Strategies

- **Virtual Scrolling**: For large patient lists and campaign histories
- **Image Optimization**: Automatic image compression and lazy loading
- **Bundle Optimization**: Tree shaking and dynamic imports
- **Caching**: Aggressive caching for static assets and API responses
- **Prefetching**: Intelligent prefetching of likely-needed data

## Accessibility

### WCAG 2.1 Compliance

- **Keyboard Navigation**: Full keyboard accessibility for all interactions
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Minimum 4.5:1 contrast ratio for all text
- **Focus Management**: Clear focus indicators and logical tab order
- **Alternative Text**: Descriptive alt text for all images and icons

### Healthcare-Specific Accessibility

- **High Contrast Mode**: Enhanced contrast for users with visual impairments
- **Large Text Support**: Scalable text up to 200% without horizontal scrolling
- **Voice Navigation**: Support for voice control software
- **Error Prevention**: Clear validation and confirmation for critical actions

This design provides a comprehensive foundation for a beautiful, functional, and accessible WhatsApp dashboard specifically tailored for pharmacy businesses and healthcare communication needs.