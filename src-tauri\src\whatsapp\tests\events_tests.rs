//! Unit tests for event emission and handling

use std::sync::Arc;
use whatsapp_ffi_client::{ConnectionStatus, Message};

use crate::whatsapp::{
    events::{MockEventEmitter, TauriEventEmitter},
    state::ServiceStatus,
};

#[tokio::test]
async fn test_mock_event_emitter_creation() {
    let emitter = MockEventEmitter::new();
    let events = emitter.get_events().await;
    assert!(events.is_empty());
}

#[tokio::test]
async fn test_mock_event_emitter_qr_code() {
    let emitter = MockEventEmitter::new();

    emitter.emit_qr_code("test_qr_code").await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 1);
    assert_eq!(events[0], "qr_code");
}

#[tokio::test]
async fn test_mock_event_emitter_message_received() {
    let emitter = MockEventEmitter::new();

    let message = Message::new_text(
        "<EMAIL>".to_string(),
        "Test message".to_string(),
        1234567890,
        "msg_123".to_string(),
    );

    emitter.emit_message_received(&message).await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 1);
    assert_eq!(events[0], "message_received");
}

#[tokio::test]
async fn test_mock_event_emitter_message_sent() {
    let emitter = MockEventEmitter::new();

    emitter
        .emit_message_sent("1234567890", "Test message", "msg_123")
        .await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 1);
    assert_eq!(events[0], "message_sent");
}

#[tokio::test]
async fn test_mock_event_emitter_connection_status_changed() {
    let emitter = MockEventEmitter::new();

    emitter
        .emit_connection_status_changed(
            ConnectionStatus::Connected,
            Some("Connected successfully".to_string()),
        )
        .await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 1);
    assert_eq!(events[0], "connection_status_changed");
}

#[tokio::test]
async fn test_mock_event_emitter_service_status_changed() {
    let emitter = MockEventEmitter::new();

    emitter
        .emit_service_status_changed(ServiceStatus::Connected)
        .await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 1);
    assert_eq!(events[0], "service_status_changed");
}

#[tokio::test]
async fn test_mock_event_emitter_service_error() {
    let emitter = MockEventEmitter::new();

    emitter.emit_service_error("Test error").await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 1);
    assert_eq!(events[0], "service_error");
}

#[tokio::test]
async fn test_mock_event_emitter_multiple_events() {
    let emitter = MockEventEmitter::new();

    emitter.emit_qr_code("qr1").await;
    emitter.emit_service_error("error1").await;
    emitter
        .emit_service_status_changed(ServiceStatus::Connected)
        .await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 3);
    assert_eq!(events[0], "qr_code");
    assert_eq!(events[1], "service_error");
    assert_eq!(events[2], "service_status_changed");
}

#[tokio::test]
async fn test_mock_event_emitter_clear_events() {
    let emitter = MockEventEmitter::new();

    emitter.emit_qr_code("qr1").await;
    emitter.emit_service_error("error1").await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 2);

    emitter.clear_events().await;

    let events = emitter.get_events().await;
    assert!(events.is_empty());
}

#[tokio::test]
async fn test_mock_event_emitter_concurrent_access() {
    let emitter = Arc::new(MockEventEmitter::new());

    // Spawn multiple tasks that emit events concurrently
    let handles: Vec<_> = (0..10)
        .map(|i| {
            let emitter = Arc::clone(&emitter);
            tokio::spawn(async move {
                emitter.emit_qr_code(&format!("qr_{}", i)).await;
            })
        })
        .collect();

    // Wait for all tasks to complete
    for handle in handles {
        handle.await.unwrap();
    }

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 10);

    // All events should be qr_code events
    for event in events {
        assert_eq!(event, "qr_code");
    }
}

#[tokio::test]
async fn test_mock_event_emitter_thread_safety() {
    let emitter = Arc::new(MockEventEmitter::new());

    // Test concurrent reads and writes
    let read_handles: Vec<_> = (0..5)
        .map(|_| {
            let emitter = Arc::clone(&emitter);
            tokio::spawn(async move { emitter.get_events().await })
        })
        .collect();

    let write_handles: Vec<_> = (0..5)
        .map(|i| {
            let emitter = Arc::clone(&emitter);
            tokio::spawn(async move {
                emitter.emit_service_error(&format!("error_{}", i)).await;
            })
        })
        .collect();

    // Wait for all operations to complete
    for handle in read_handles {
        handle.await.unwrap();
    }

    for handle in write_handles {
        handle.await.unwrap();
    }

    let final_events = emitter.get_events().await;
    assert_eq!(final_events.len(), 5);
}

#[tokio::test]
async fn test_event_ordering() {
    let emitter = MockEventEmitter::new();

    // Emit events in specific order
    emitter
        .emit_service_status_changed(ServiceStatus::Initializing)
        .await;
    emitter.emit_qr_code("qr_code").await;
    emitter
        .emit_connection_status_changed(ConnectionStatus::Connected, None)
        .await;
    emitter
        .emit_service_status_changed(ServiceStatus::Connected)
        .await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 4);
    assert_eq!(events[0], "service_status_changed");
    assert_eq!(events[1], "qr_code");
    assert_eq!(events[2], "connection_status_changed");
    assert_eq!(events[3], "service_status_changed");
}

#[tokio::test]
async fn test_event_emitter_with_different_message_types() {
    let emitter = MockEventEmitter::new();

    // Test with text message
    let text_message = Message::new_text(
        "<EMAIL>".to_string(),
        "Hello world".to_string(),
        1234567890,
        "msg_text".to_string(),
    );
    emitter.emit_message_received(&text_message).await;

    // Note: For more complex message types, we would need to create them
    // using the appropriate constructors from the Message struct

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 1);
    assert_eq!(events[0], "message_received");
}

#[tokio::test]
async fn test_event_emitter_error_scenarios() {
    let emitter = MockEventEmitter::new();

    // Test with empty strings
    emitter.emit_qr_code("").await;
    emitter.emit_service_error("").await;
    emitter.emit_message_sent("", "", "").await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 3);

    // Events should still be recorded even with empty data
    assert_eq!(events[0], "qr_code");
    assert_eq!(events[1], "service_error");
    assert_eq!(events[2], "message_sent");
}

#[tokio::test]
async fn test_event_emitter_large_data() {
    let emitter = MockEventEmitter::new();

    // Test with large strings
    let large_qr = "x".repeat(10000);
    let large_error = "error ".repeat(1000);

    emitter.emit_qr_code(&large_qr).await;
    emitter.emit_service_error(&large_error).await;

    let events = emitter.get_events().await;
    assert_eq!(events.len(), 2);
    assert_eq!(events[0], "qr_code");
    assert_eq!(events[1], "service_error");
}
