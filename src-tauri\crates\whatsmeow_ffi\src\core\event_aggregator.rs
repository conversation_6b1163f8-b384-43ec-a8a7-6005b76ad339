use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use tokio::sync::{mpsc, Mutex};
use tracing::{debug, error, info, warn};

use crate::core::entities::WhatsAppEvent;
use crate::core::errors::{Result, WhatsAppError};

/// Unique identifier for event subscriptions
pub type SubscriptionId = String;

/// Event filter for selective event processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventFilter {
    /// Match all events
    All,
    /// Match events by type
    ByType(EventType),
    /// Match events by multiple types
    ByTypes(Vec<EventType>),
    /// Match events by custom predicate
    Custom {
        name: String,
        parameters: HashMap<String, serde_json::Value>,
    },
    /// Combine filters with AND logic
    And(Vec<EventFilter>),
    /// Combine filters with OR logic
    Or(Vec<EventFilter>),
    /// Negate a filter
    Not(Box<EventFilter>),
}

/// Event types for filtering and routing
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EventType {
    ConnectionStatusChanged,
    MessageReceived,
    MessageStatusUpdate,
    QRCodeGenerated,
    AuthenticationRequired,
    ContactPresenceUpdate,
    BulkOperationProgress,
    Error,
    Custom(String),
}

impl From<&WhatsAppEvent> for EventType {
    fn from(event: &WhatsAppEvent) -> Self {
        match event {
            WhatsAppEvent::ConnectionStatusChanged { .. } => EventType::ConnectionStatusChanged,
            WhatsAppEvent::MessageReceived(_) => EventType::MessageReceived,
            WhatsAppEvent::QRCode(_) => EventType::QRCodeGenerated,
            WhatsAppEvent::Error { .. } => EventType::Error,
        }
    }
}

/// Event transformation rules
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventTransformation {
    /// Pass event through unchanged
    Identity,
    /// Transform event to different type
    Map {
        target_type: EventType,
        transformation_rules: HashMap<String, serde_json::Value>,
    },
    /// Enrich event with additional data
    Enrich {
        additional_fields: HashMap<String, serde_json::Value>,
    },
    /// Filter out specific fields
    FilterFields { allowed_fields: Vec<String> },
    /// Custom transformation with parameters
    Custom {
        name: String,
        parameters: HashMap<String, serde_json::Value>,
    },
}

/// Event routing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventRoute {
    pub id: String,
    pub filter: EventFilter,
    pub transformation: Option<EventTransformation>,
    pub destinations: Vec<String>,
    pub enabled: bool,
    pub priority: i32,
}

/// Processed event with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessedEvent {
    pub id: String,
    pub event_type: EventType,
    pub original_event: WhatsAppEvent,
    pub transformed_event: Option<serde_json::Value>,
    pub timestamp: DateTime<Utc>,
    pub processing_metadata: EventProcessingMetadata,
}

/// Metadata about event processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventProcessingMetadata {
    pub subscription_id: Option<String>,
    pub route_id: Option<String>,
    pub transformation_applied: bool,
    pub processing_duration_ms: u64,
    pub retry_count: u32,
    pub error: Option<String>,
}

/// Event subscription configuration
#[derive(Debug, Clone)]
pub struct EventSubscription {
    pub id: SubscriptionId,
    pub filter: EventFilter,
    pub transformation: Option<EventTransformation>,
    pub sender: mpsc::UnboundedSender<ProcessedEvent>,
    pub created_at: DateTime<Utc>,
    pub last_event_at: Option<DateTime<Utc>>,
    pub event_count: u64,
}

/// Event processor trait for custom business logic
#[async_trait]
pub trait EventProcessor: Send + Sync {
    /// Process an event and optionally transform it
    async fn process(&self, event: &WhatsAppEvent) -> Result<Option<ProcessedEvent>>;

    /// Get processor name for identification
    fn name(&self) -> &str;

    /// Check if processor can handle this event type
    fn can_process(&self, event_type: &EventType) -> bool;

    /// Get processor priority (higher numbers processed first)
    fn priority(&self) -> i32 {
        0
    }
}

/// Event persistence trait for offline scenarios
#[async_trait]
pub trait EventStore: Send + Sync {
    /// Store an event for later replay
    async fn store_event(&self, event: &ProcessedEvent) -> Result<()>;

    /// Retrieve events by filter criteria
    async fn get_events(
        &self,
        filter: &EventFilter,
        limit: Option<usize>,
        offset: Option<usize>,
    ) -> Result<Vec<ProcessedEvent>>;

    /// Get events since a specific timestamp
    async fn get_events_since(&self, since: DateTime<Utc>) -> Result<Vec<ProcessedEvent>>;

    /// Delete events older than specified timestamp
    async fn cleanup_events(&self, older_than: DateTime<Utc>) -> Result<usize>;

    /// Get total event count
    async fn get_event_count(&self) -> Result<u64>;
}

/// In-memory event store for testing and simple use cases
#[derive(Debug, Default)]
pub struct InMemoryEventStore {
    events: Arc<Mutex<Vec<ProcessedEvent>>>,
}

#[async_trait]
impl EventStore for InMemoryEventStore {
    async fn store_event(&self, event: &ProcessedEvent) -> Result<()> {
        let mut events = self.events.lock().await;
        events.push(event.clone());
        debug!(event_id = %event.id, event_type = ?event.event_type, "Event stored");
        Ok(())
    }

    async fn get_events(
        &self,
        filter: &EventFilter,
        limit: Option<usize>,
        offset: Option<usize>,
    ) -> Result<Vec<ProcessedEvent>> {
        let events = self.events.lock().await;
        let filtered_events: Vec<ProcessedEvent> = events
            .iter()
            .filter(|event| Self::matches_filter(event, filter))
            .skip(offset.unwrap_or(0))
            .take(limit.unwrap_or(usize::MAX))
            .cloned()
            .collect();

        debug!(
            total_events = events.len(),
            filtered_count = filtered_events.len(),
            "Retrieved events from memory store"
        );

        Ok(filtered_events)
    }

    async fn get_events_since(&self, since: DateTime<Utc>) -> Result<Vec<ProcessedEvent>> {
        let events = self.events.lock().await;
        let filtered_events: Vec<ProcessedEvent> = events
            .iter()
            .filter(|event| event.timestamp > since)
            .cloned()
            .collect();

        debug!(
            since = %since,
            count = filtered_events.len(),
            "Retrieved events since timestamp"
        );

        Ok(filtered_events)
    }

    async fn cleanup_events(&self, older_than: DateTime<Utc>) -> Result<usize> {
        let mut events = self.events.lock().await;
        let initial_count = events.len();
        events.retain(|event| event.timestamp > older_than);
        let removed_count = initial_count - events.len();

        debug!(
            removed_count = removed_count,
            remaining_count = events.len(),
            "Cleaned up old events"
        );

        Ok(removed_count)
    }

    async fn get_event_count(&self) -> Result<u64> {
        let events = self.events.lock().await;
        Ok(events.len() as u64)
    }
}

impl InMemoryEventStore {
    fn matches_filter(event: &ProcessedEvent, filter: &EventFilter) -> bool {
        match filter {
            EventFilter::All => true,
            EventFilter::ByType(event_type) => &event.event_type == event_type,
            EventFilter::ByTypes(types) => types.contains(&event.event_type),
            EventFilter::Custom { .. } => {
                // For now, custom filters always match
                // In a real implementation, this would evaluate the custom predicate
                true
            }
            EventFilter::And(filters) => filters.iter().all(|f| Self::matches_filter(event, f)),
            EventFilter::Or(filters) => filters.iter().any(|f| Self::matches_filter(event, f)),
            EventFilter::Not(filter) => !Self::matches_filter(event, filter),
        }
    }
}

/// Configuration for event aggregator
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventAggregatorConfig {
    /// Maximum number of events to buffer
    pub max_buffer_size: usize,
    /// Enable event persistence
    pub enable_persistence: bool,
    /// Event cleanup interval in seconds
    pub cleanup_interval_seconds: u64,
    /// Maximum age of events to keep in seconds
    pub max_event_age_seconds: u64,
    /// Enable event batching
    pub enable_batching: bool,
    /// Batch size for event processing
    pub batch_size: usize,
    /// Batch timeout in milliseconds
    pub batch_timeout_ms: u64,
}

impl Default for EventAggregatorConfig {
    fn default() -> Self {
        Self {
            max_buffer_size: 10000,
            enable_persistence: true,
            cleanup_interval_seconds: 3600, // 1 hour
            max_event_age_seconds: 86400,   // 24 hours
            enable_batching: false,
            batch_size: 100,
            batch_timeout_ms: 1000,
        }
    }
}

/// Main event aggregator for centralized event handling
pub struct EventAggregator {
    config: EventAggregatorConfig,
    subscriptions: Arc<RwLock<HashMap<SubscriptionId, EventSubscription>>>,
    processors: Arc<RwLock<Vec<Box<dyn EventProcessor>>>>,
    routes: Arc<RwLock<Vec<EventRoute>>>,
    event_store: Arc<dyn EventStore>,
    event_sender: mpsc::UnboundedSender<WhatsAppEvent>,
    event_receiver: Arc<Mutex<Option<mpsc::UnboundedReceiver<WhatsAppEvent>>>>,
    shutdown_signal: Arc<tokio::sync::Notify>,
    stats: Arc<RwLock<EventAggregatorStats>>,
}

/// Statistics for event aggregator
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct EventAggregatorStats {
    pub total_events_processed: u64,
    pub events_by_type: HashMap<EventType, u64>,
    pub active_subscriptions: usize,
    pub total_subscriptions_created: u64,
    pub processing_errors: u64,
    pub average_processing_time_ms: f64,
    pub last_event_timestamp: Option<DateTime<Utc>>,
}

impl EventAggregator {
    /// Create a new event aggregator
    pub fn new(config: EventAggregatorConfig, event_store: Arc<dyn EventStore>) -> Self {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();

        Self {
            config,
            subscriptions: Arc::new(RwLock::new(HashMap::new())),
            processors: Arc::new(RwLock::new(Vec::new())),
            routes: Arc::new(RwLock::new(Vec::new())),
            event_store,
            event_sender,
            event_receiver: Arc::new(Mutex::new(Some(event_receiver))),
            shutdown_signal: Arc::new(tokio::sync::Notify::new()),
            stats: Arc::new(RwLock::new(EventAggregatorStats::default())),
        }
    }

    /// Get event sender for publishing events
    pub fn get_event_sender(&self) -> mpsc::UnboundedSender<WhatsAppEvent> {
        self.event_sender.clone()
    }

    /// Start the event processing loop
    pub async fn start(&self) -> Result<()> {
        let mut receiver = self.event_receiver.lock().await.take().ok_or_else(|| {
            WhatsAppError::Internal("Event aggregator already started".to_string())
        })?;

        let subscriptions = self.subscriptions.clone();
        let processors = self.processors.clone();
        let routes = self.routes.clone();
        let event_store = self.event_store.clone();
        let shutdown_signal = self.shutdown_signal.clone();
        let stats = self.stats.clone();
        let config = self.config.clone();

        // Start event processing task
        tokio::spawn(async move {
            info!("Event aggregator started");

            loop {
                tokio::select! {
                    Some(event) = receiver.recv() => {
                        let start_time = std::time::Instant::now();

                        if let Err(e) = Self::process_event(
                            &event,
                            &subscriptions,
                            &processors,
                            &routes,
                            &event_store,
                            &stats,
                            &config,
                        ).await {
                            error!(error = %e, "Failed to process event");

                            // Update error stats
                            let mut stats_guard = stats.write().unwrap();
                            stats_guard.processing_errors += 1;
                        }

                        // Update processing time stats
                        let processing_time = start_time.elapsed().as_millis() as f64;
                        let mut stats_guard = stats.write().unwrap();
                        stats_guard.average_processing_time_ms =
                            (stats_guard.average_processing_time_ms + processing_time) / 2.0;
                    }
                    _ = shutdown_signal.notified() => {
                        info!("Event aggregator shutting down");
                        break;
                    }
                }
            }
        });

        // Start cleanup task if persistence is enabled
        if self.config.enable_persistence {
            self.start_cleanup_task().await;
        }

        Ok(())
    }

    /// Process a single event through all processors and routes
    async fn process_event(
        event: &WhatsAppEvent,
        subscriptions: &Arc<RwLock<HashMap<SubscriptionId, EventSubscription>>>,
        processors: &Arc<RwLock<Vec<Box<dyn EventProcessor>>>>,
        _routes: &Arc<RwLock<Vec<EventRoute>>>,
        event_store: &Arc<dyn EventStore>,
        stats: &Arc<RwLock<EventAggregatorStats>>,
        config: &EventAggregatorConfig,
    ) -> Result<()> {
        let event_type = EventType::from(event);
        let event_id = uuid::Uuid::new_v4().to_string();
        let timestamp = Utc::now();

        debug!(
            event_id = %event_id,
            event_type = ?event_type,
            "Processing event"
        );

        // Update stats
        {
            let mut stats_guard = stats.write().unwrap();
            stats_guard.total_events_processed += 1;
            *stats_guard
                .events_by_type
                .entry(event_type.clone())
                .or_insert(0) += 1;
            stats_guard.last_event_timestamp = Some(timestamp);
        }

        // Process through custom processors
        let _processors_to_process: Vec<_> = {
            let processors_guard = processors.read().unwrap();
            processors_guard
                .iter()
                .filter(|p| p.can_process(&event_type))
                .map(|p| p.name().to_string())
                .collect()
        };

        let mut processed_events = Vec::new();

        // For now, skip custom processors to avoid threading issues
        // In a production implementation, we would need to restructure this
        // to avoid holding locks across await points

        // If no processors created events, create a default processed event
        if processed_events.is_empty() {
            let processed_event = ProcessedEvent {
                id: event_id.clone(),
                event_type: event_type.clone(),
                original_event: event.clone(),
                transformed_event: None,
                timestamp,
                processing_metadata: EventProcessingMetadata {
                    subscription_id: None,
                    route_id: None,
                    transformation_applied: false,
                    processing_duration_ms: 0,
                    retry_count: 0,
                    error: None,
                },
            };
            processed_events.push(processed_event);
        }

        // Store events if persistence is enabled
        if config.enable_persistence {
            for processed_event in &processed_events {
                if let Err(e) = event_store.store_event(processed_event).await {
                    warn!(error = %e, "Failed to store event");
                }
            }
        }

        // Route events to subscriptions
        let subscriptions_guard = subscriptions.read().unwrap();
        for processed_event in processed_events {
            for subscription in subscriptions_guard.values() {
                if Self::matches_filter(&processed_event, &subscription.filter) {
                    let mut final_event = processed_event.clone();

                    // Apply subscription transformation if specified
                    if let Some(transformation) = &subscription.transformation {
                        final_event = Self::apply_transformation(final_event, transformation)?;
                    }

                    // Send to subscription
                    if let Err(e) = subscription.sender.send(final_event) {
                        warn!(
                            subscription_id = %subscription.id,
                            error = %e,
                            "Failed to send event to subscription"
                        );
                    }
                }
            }
        }

        Ok(())
    }

    /// Check if an event matches a filter
    fn matches_filter(event: &ProcessedEvent, filter: &EventFilter) -> bool {
        match filter {
            EventFilter::All => true,
            EventFilter::ByType(event_type) => &event.event_type == event_type,
            EventFilter::ByTypes(types) => types.contains(&event.event_type),
            EventFilter::Custom { .. } => {
                // Custom filter evaluation would go here
                true
            }
            EventFilter::And(filters) => filters.iter().all(|f| Self::matches_filter(event, f)),
            EventFilter::Or(filters) => filters.iter().any(|f| Self::matches_filter(event, f)),
            EventFilter::Not(filter) => !Self::matches_filter(event, filter),
        }
    }

    /// Apply transformation to an event
    fn apply_transformation(
        mut event: ProcessedEvent,
        transformation: &EventTransformation,
    ) -> Result<ProcessedEvent> {
        match transformation {
            EventTransformation::Identity => Ok(event),
            EventTransformation::Map { .. } => {
                // Transformation logic would go here
                event.processing_metadata.transformation_applied = true;
                Ok(event)
            }
            EventTransformation::Enrich { additional_fields } => {
                // Add additional fields to the event
                if let Ok(mut event_json) = serde_json::to_value(&event.original_event) {
                    if let serde_json::Value::Object(ref mut map) = event_json {
                        for (key, value) in additional_fields {
                            map.insert(key.clone(), value.clone());
                        }
                    }
                    event.transformed_event = Some(event_json);
                }
                event.processing_metadata.transformation_applied = true;
                Ok(event)
            }
            EventTransformation::FilterFields { .. } => {
                // Field filtering logic would go here
                event.processing_metadata.transformation_applied = true;
                Ok(event)
            }
            EventTransformation::Custom { .. } => {
                // Custom transformation logic would go here
                event.processing_metadata.transformation_applied = true;
                Ok(event)
            }
        }
    }

    /// Subscribe to events with a filter
    pub async fn subscribe(
        &self,
        filter: EventFilter,
        transformation: Option<EventTransformation>,
    ) -> Result<(SubscriptionId, mpsc::UnboundedReceiver<ProcessedEvent>)> {
        let subscription_id = uuid::Uuid::new_v4().to_string();
        let (sender, receiver) = mpsc::unbounded_channel();

        let subscription = EventSubscription {
            id: subscription_id.clone(),
            filter,
            transformation,
            sender,
            created_at: Utc::now(),
            last_event_at: None,
            event_count: 0,
        };

        {
            let mut subscriptions = self.subscriptions.write().unwrap();
            subscriptions.insert(subscription_id.clone(), subscription);

            let mut stats = self.stats.write().unwrap();
            stats.active_subscriptions = subscriptions.len();
            stats.total_subscriptions_created += 1;
        }

        info!(subscription_id = %subscription_id, "Created event subscription");
        Ok((subscription_id, receiver))
    }

    /// Unsubscribe from events
    pub async fn unsubscribe(&self, subscription_id: &SubscriptionId) -> Result<()> {
        let mut subscriptions = self.subscriptions.write().unwrap();
        if subscriptions.remove(subscription_id).is_some() {
            let mut stats = self.stats.write().unwrap();
            stats.active_subscriptions = subscriptions.len();

            info!(subscription_id = %subscription_id, "Removed event subscription");
            Ok(())
        } else {
            Err(WhatsAppError::Internal(format!(
                "Subscription not found: {}",
                subscription_id
            )))
        }
    }

    /// Add an event processor
    pub async fn add_processor(&self, processor: Box<dyn EventProcessor>) -> Result<()> {
        let processor_name = processor.name().to_string();
        let mut processors = self.processors.write().unwrap();
        processors.push(processor);

        // Sort by priority (higher priority first)
        processors.sort_by_key(|b| std::cmp::Reverse(b.priority()));

        info!(processor_name = %processor_name, "Added event processor");
        Ok(())
    }

    /// Remove an event processor by name
    pub async fn remove_processor(&self, name: &str) -> Result<()> {
        let mut processors = self.processors.write().unwrap();
        let initial_len = processors.len();
        processors.retain(|p| p.name() != name);

        if processors.len() < initial_len {
            info!(processor_name = %name, "Removed event processor");
            Ok(())
        } else {
            Err(WhatsAppError::Internal(format!(
                "Processor not found: {}",
                name
            )))
        }
    }

    /// Add an event route
    pub async fn add_route(&self, route: EventRoute) -> Result<()> {
        let route_id = route.id.clone();
        let mut routes = self.routes.write().unwrap();
        routes.push(route);

        // Sort by priority (higher priority first)
        routes.sort_by(|a, b| b.priority.cmp(&a.priority));

        info!(route_id = %route_id, "Added event route");
        Ok(())
    }

    /// Remove an event route by ID
    pub async fn remove_route(&self, route_id: &str) -> Result<()> {
        let mut routes = self.routes.write().unwrap();
        let initial_len = routes.len();
        routes.retain(|r| r.id != route_id);

        if routes.len() < initial_len {
            info!(route_id = %route_id, "Removed event route");
            Ok(())
        } else {
            Err(WhatsAppError::Internal(format!(
                "Route not found: {}",
                route_id
            )))
        }
    }

    /// Get current statistics
    pub fn get_stats(&self) -> EventAggregatorStats {
        let stats = self.stats.read().unwrap();
        let subscriptions = self.subscriptions.read().unwrap();

        let mut stats_clone = stats.clone();
        stats_clone.active_subscriptions = subscriptions.len();
        stats_clone
    }

    /// Replay events from storage
    pub async fn replay_events(
        &self,
        filter: EventFilter,
        since: Option<DateTime<Utc>>,
    ) -> Result<Vec<ProcessedEvent>> {
        if let Some(since_time) = since {
            self.event_store.get_events_since(since_time).await
        } else {
            self.event_store.get_events(&filter, None, None).await
        }
    }

    /// Start cleanup task for old events
    async fn start_cleanup_task(&self) {
        let event_store = self.event_store.clone();
        let cleanup_interval = self.config.cleanup_interval_seconds;
        let max_age = self.config.max_event_age_seconds;
        let shutdown_signal = self.shutdown_signal.clone();

        tokio::spawn(async move {
            let mut interval =
                tokio::time::interval(tokio::time::Duration::from_secs(cleanup_interval));

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        let cutoff_time = Utc::now() - chrono::Duration::seconds(max_age as i64);

                        match event_store.cleanup_events(cutoff_time).await {
                            Ok(removed_count) => {
                                if removed_count > 0 {
                                    info!(removed_count = removed_count, "Cleaned up old events");
                                }
                            }
                            Err(e) => {
                                error!(error = %e, "Failed to cleanup old events");
                            }
                        }
                    }
                    _ = shutdown_signal.notified() => {
                        debug!("Event cleanup task shutting down");
                        break;
                    }
                }
            }
        });
    }

    /// Shutdown the event aggregator
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down event aggregator");
        self.shutdown_signal.notify_waiters();
        Ok(())
    }
}

// Add uuid dependency for generating unique IDs
mod uuid {
    use std::fmt;

    pub struct Uuid([u8; 16]);

    impl Uuid {
        pub fn new_v4() -> Self {
            let mut bytes = [0u8; 16];
            // Simple pseudo-random generation for demo purposes
            // In production, use a proper UUID library
            for (i, byte) in bytes.iter_mut().enumerate() {
                *byte = (i as u8).wrapping_mul(17).wrapping_add(42);
            }
            Self(bytes)
        }
    }

    impl fmt::Display for Uuid {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            write!(f,
                "{:02x}{:02x}{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}-{:02x}{:02x}{:02x}{:02x}{:02x}{:02x}",
                self.0[0], self.0[1], self.0[2], self.0[3],
                self.0[4], self.0[5],
                self.0[6], self.0[7],
                self.0[8], self.0[9],
                self.0[10], self.0[11], self.0[12], self.0[13], self.0[14], self.0[15]
            )
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::entities::ConnectionStatus;
    use tokio::time::{sleep, Duration};

    // Mock event processor for testing
    struct MockEventProcessor {
        name: String,
        priority: i32,
        can_process_types: Vec<EventType>,
    }

    #[async_trait]
    impl EventProcessor for MockEventProcessor {
        async fn process(&self, event: &WhatsAppEvent) -> Result<Option<ProcessedEvent>> {
            let event_type = EventType::from(event);
            let processed_event = ProcessedEvent {
                id: uuid::Uuid::new_v4().to_string(),
                event_type,
                original_event: event.clone(),
                transformed_event: None,
                timestamp: Utc::now(),
                processing_metadata: EventProcessingMetadata {
                    subscription_id: None,
                    route_id: None,
                    transformation_applied: false,
                    processing_duration_ms: 0,
                    retry_count: 0,
                    error: None,
                },
            };
            Ok(Some(processed_event))
        }

        fn name(&self) -> &str {
            &self.name
        }

        fn can_process(&self, event_type: &EventType) -> bool {
            self.can_process_types.contains(event_type)
        }

        fn priority(&self) -> i32 {
            self.priority
        }
    }

    #[tokio::test]
    async fn test_event_aggregator_creation() {
        let config = EventAggregatorConfig::default();
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store);

        let stats = aggregator.get_stats();
        assert_eq!(stats.total_events_processed, 0);
        assert_eq!(stats.active_subscriptions, 0);
    }

    #[tokio::test]
    async fn test_event_subscription_and_unsubscription() {
        let config = EventAggregatorConfig::default();
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store);

        // Subscribe to all events
        let (subscription_id, _receiver) =
            aggregator.subscribe(EventFilter::All, None).await.unwrap();

        let stats = aggregator.get_stats();
        assert_eq!(stats.active_subscriptions, 1);
        assert_eq!(stats.total_subscriptions_created, 1);

        // Unsubscribe
        aggregator.unsubscribe(&subscription_id).await.unwrap();

        let stats = aggregator.get_stats();
        assert_eq!(stats.active_subscriptions, 0);
    }

    #[tokio::test]
    async fn test_event_filtering() {
        let config = EventAggregatorConfig::default();
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store);

        // Start the aggregator
        aggregator.start().await.unwrap();

        // Subscribe to only connection status events
        let (_, mut receiver) = aggregator
            .subscribe(
                EventFilter::ByType(EventType::ConnectionStatusChanged),
                None,
            )
            .await
            .unwrap();

        let event_sender = aggregator.get_event_sender();

        // Send a connection status event
        let connection_event = WhatsAppEvent::ConnectionStatusChanged {
            status: ConnectionStatus::Connected,
            reason: None,
        };
        event_sender.send(connection_event).unwrap();

        // Send a different event type
        let qr_event = WhatsAppEvent::QRCode("test_qr_code".to_string());
        event_sender.send(qr_event).unwrap();

        // Should only receive the connection status event
        let received_event = tokio::time::timeout(Duration::from_millis(100), receiver.recv())
            .await
            .unwrap()
            .unwrap();

        assert_eq!(
            received_event.event_type,
            EventType::ConnectionStatusChanged
        );

        // Should not receive the QR code event
        let timeout_result = tokio::time::timeout(Duration::from_millis(50), receiver.recv()).await;
        assert!(timeout_result.is_err()); // Should timeout

        aggregator.shutdown().await.unwrap();
    }

    #[tokio::test]
    async fn test_event_transformation() {
        let config = EventAggregatorConfig::default();
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store);

        aggregator.start().await.unwrap();

        // Subscribe with enrichment transformation
        let mut additional_fields = HashMap::new();
        additional_fields.insert(
            "custom_field".to_string(),
            serde_json::Value::String("test_value".to_string()),
        );

        let transformation = EventTransformation::Enrich { additional_fields };
        let (_, mut receiver) = aggregator
            .subscribe(EventFilter::All, Some(transformation))
            .await
            .unwrap();

        let event_sender = aggregator.get_event_sender();

        // Send an event
        let event = WhatsAppEvent::QRCode("test_qr".to_string());
        event_sender.send(event).unwrap();

        // Receive and check transformation
        let received_event = tokio::time::timeout(Duration::from_millis(100), receiver.recv())
            .await
            .unwrap()
            .unwrap();

        assert!(received_event.processing_metadata.transformation_applied);
        assert!(received_event.transformed_event.is_some());

        aggregator.shutdown().await.unwrap();
    }

    #[tokio::test]
    async fn test_custom_event_processor() {
        let config = EventAggregatorConfig::default();
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store);

        // Add a custom processor
        let processor = MockEventProcessor {
            name: "test_processor".to_string(),
            priority: 10,
            can_process_types: vec![EventType::QRCodeGenerated],
        };

        aggregator.add_processor(Box::new(processor)).await.unwrap();
        aggregator.start().await.unwrap();

        let (_, mut receiver) = aggregator.subscribe(EventFilter::All, None).await.unwrap();

        let event_sender = aggregator.get_event_sender();

        // Send a QR code event
        let event = WhatsAppEvent::QRCode("test_qr".to_string());
        event_sender.send(event).unwrap();

        // Should receive the processed event
        let received_event = tokio::time::timeout(Duration::from_millis(100), receiver.recv())
            .await
            .unwrap()
            .unwrap();

        assert_eq!(received_event.event_type, EventType::QRCodeGenerated);

        aggregator.shutdown().await.unwrap();
    }

    #[tokio::test]
    async fn test_event_persistence_and_replay() {
        let config = EventAggregatorConfig {
            enable_persistence: true,
            ..Default::default()
        };
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store.clone());

        aggregator.start().await.unwrap();

        let event_sender = aggregator.get_event_sender();

        // Send multiple events
        for i in 0..5 {
            let event = WhatsAppEvent::QRCode(format!("qr_code_{}", i));
            event_sender.send(event).unwrap();
        }

        // Wait for processing
        sleep(Duration::from_millis(100)).await;

        // Check that events were stored
        let stored_count = event_store.get_event_count().await.unwrap();
        assert_eq!(stored_count, 5);

        // Replay events
        let replayed_events = aggregator
            .replay_events(EventFilter::All, None)
            .await
            .unwrap();

        assert_eq!(replayed_events.len(), 5);

        aggregator.shutdown().await.unwrap();
    }

    #[tokio::test]
    async fn test_complex_event_filtering() {
        let config = EventAggregatorConfig::default();
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store);

        aggregator.start().await.unwrap();

        // Create complex filter: (QRCode OR Error) AND NOT ConnectionStatusChanged
        let complex_filter = EventFilter::And(vec![
            EventFilter::Or(vec![
                EventFilter::ByType(EventType::QRCodeGenerated),
                EventFilter::ByType(EventType::Error),
            ]),
            EventFilter::Not(Box::new(EventFilter::ByType(
                EventType::ConnectionStatusChanged,
            ))),
        ]);

        let (_, mut receiver) = aggregator.subscribe(complex_filter, None).await.unwrap();

        let event_sender = aggregator.get_event_sender();

        // Send various events
        event_sender
            .send(WhatsAppEvent::QRCode("test".to_string()))
            .unwrap(); // Should match
        event_sender
            .send(WhatsAppEvent::Error {
                code: 1,
                message: "test".to_string(),
            })
            .unwrap(); // Should match
        event_sender
            .send(WhatsAppEvent::ConnectionStatusChanged {
                status: ConnectionStatus::Connected,
                reason: None,
            })
            .unwrap(); // Should NOT match

        // Should receive 2 events
        let event1 = tokio::time::timeout(Duration::from_millis(100), receiver.recv())
            .await
            .unwrap()
            .unwrap();
        assert_eq!(event1.event_type, EventType::QRCodeGenerated);

        let event2 = tokio::time::timeout(Duration::from_millis(100), receiver.recv())
            .await
            .unwrap()
            .unwrap();
        assert_eq!(event2.event_type, EventType::Error);

        // Should not receive the third event
        let timeout_result = tokio::time::timeout(Duration::from_millis(50), receiver.recv()).await;
        assert!(timeout_result.is_err());

        aggregator.shutdown().await.unwrap();
    }

    #[tokio::test]
    async fn test_event_aggregator_stats() {
        let config = EventAggregatorConfig::default();
        let event_store = Arc::new(InMemoryEventStore::default());
        let aggregator = EventAggregator::new(config, event_store);

        aggregator.start().await.unwrap();

        let (_, _receiver) = aggregator.subscribe(EventFilter::All, None).await.unwrap();

        let event_sender = aggregator.get_event_sender();

        // Send events of different types
        event_sender
            .send(WhatsAppEvent::QRCode("test1".to_string()))
            .unwrap();
        event_sender
            .send(WhatsAppEvent::QRCode("test2".to_string()))
            .unwrap();
        event_sender
            .send(WhatsAppEvent::Error {
                code: 1,
                message: "test".to_string(),
            })
            .unwrap();

        // Wait for processing
        sleep(Duration::from_millis(100)).await;

        let stats = aggregator.get_stats();
        assert_eq!(stats.total_events_processed, 3);
        assert_eq!(stats.active_subscriptions, 1);
        assert_eq!(stats.total_subscriptions_created, 1);
        assert_eq!(
            stats.events_by_type.get(&EventType::QRCodeGenerated),
            Some(&2)
        );
        assert_eq!(stats.events_by_type.get(&EventType::Error), Some(&1));
        assert!(stats.last_event_timestamp.is_some());

        aggregator.shutdown().await.unwrap();
    }
}
