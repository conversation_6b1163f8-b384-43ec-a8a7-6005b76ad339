{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 15939273003786564609, "deps": [[1017461770342116999, "sharded_slab", false, 1936949103541278707], [6048213226671835012, "smallvec", false, 12079860214132295425], [8614575489689151157, "nu_ansi_term", false, 7019536533202004455], [10806489435541507125, "tracing_log", false, 2894748687463250978], [11033263105862272874, "tracing_core", false, 13280334004191246713], [12427285511609802057, "thread_local", false, 12639677016776838856]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-a8992cbf89a64e35\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}