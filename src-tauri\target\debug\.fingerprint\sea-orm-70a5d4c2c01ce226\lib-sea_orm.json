{"rustc": 16591470773350601817, "features": "[\"bigdecimal\", \"chrono\", \"default\", \"macros\", \"mock\", \"postgres-array\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sea-query-binder\", \"serde_json\", \"sqlx\", \"sqlx-dep\", \"sqlx-postgres\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"debug-print\", \"default\", \"ipnetwork\", \"json-array\", \"macros\", \"mock\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"proxy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sea-orm-internal\", \"sea-query-binder\", \"seaography\", \"serde_json\", \"sqlite-use-returning-for-3_35\", \"sqlx\", \"sqlx-all\", \"sqlx-dep\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"tests-cfg\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 5911612443439219948, "profile": 15657897354478470176, "path": 15205570603458590620, "deps": [[1188017320647144970, "async_stream", false, 12823218456837989918], [2995469292676432503, "uuid", false, 6769240453884269147], [3150220818285335163, "url", false, 16586463868209902736], [3302295501534065768, "strum", false, 331647704579921194], [3525657182790186941, "ouroboros", false, 1023164379858213347], [5986029879202738730, "log", false, 3210362874149672375], [7161281228672193341, "sea_query", false, 3205212379452710621], [7817431159498251116, "sea_query_binder", false, 8738364181399071367], [8606274917505247608, "tracing", false, 13752143806742742294], [9689903380558560274, "serde", false, 10996426815917291500], [9897246384292347999, "chrono", false, 541124106390407368], [10629569228670356391, "futures_util", false, 12545152981789545953], [10806645703491011684, "thiserror", false, 44651847147331727], [11946729385090170470, "async_trait", false, 2389517705436373950], [12409575957772518135, "time", false, 16913109520808322110], [14647456484942590313, "bigdecimal", false, 16054569543174291623], [16119793329258425851, "rust_decimal", false, 15692620249086610884], [16362055519698394275, "serde_json", false, 7262301444845053042], [17625815326946657219, "sea_orm_macros", false, 2708302198842247183], [17982831385697850842, "sqlx", false, 8997539274098623822]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-orm-70a5d4c2c01ce226\\dep-lib-sea_orm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}