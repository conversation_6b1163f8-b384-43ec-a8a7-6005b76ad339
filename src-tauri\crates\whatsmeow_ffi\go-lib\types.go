package main

/*
#cgo CFLAGS: -I.
#include "cdefs.h"
*/
import "C"

import (
	"encoding/json"
	"fmt"
	"unsafe"
)

// Event types matching C enum
const (
	EventQRCode           = C.EVENT_QR_CODE
	EventMessageReceived  = C.EVENT_MESSAGE_RECEIVED
	EventConnectionStatus = C.EVENT_CONNECTION_STATUS
	EventError            = C.EVENT_ERROR
)

// Connection status matching C enum
const (
	StatusDisconnected = C.STATUS_DISCONNECTED
	StatusConnecting   = C.STATUS_CONNECTING
	StatusConnected    = C.STATUS_CONNECTED
	StatusLoggedOut    = C.STATUS_LOGGED_OUT
)

// Error codes matching C enum
const (
	ErrorNone          = C.ERROR_NONE
	ErrorInvalidHandle = C.ERROR_INVALID_HANDLE
	ErrorNotConnected  = C.ERROR_NOT_CONNECTED
	ErrorInvalidJID    = C.ERROR_INVALID_JID
	ErrorSendFailed    = C.ERROR_SEND_FAILED
	ErrorInternal      = C.ERROR_INTERNAL
)

// Event data structures for JSON serialization
type QRCodeEvent struct {
	Code string `json:"code"`
}

// MessageType represents different types of WhatsApp messages
type MessageType struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

// TextMessageData represents text message data
type TextMessageData struct {
	Text string `json:"text"`
}

// MediaMessageData represents media message data (for Image, Video, Sticker)
type MediaMessageData struct {
	Caption   *string   `json:"caption,omitempty"`
	MediaInfo MediaInfo `json:"media_info"`
}

// AudioMessageData represents audio message data
type AudioMessageData struct {
	MediaInfo MediaInfo `json:"media_info"`
	IsVoice   bool      `json:"is_voice"`
}

// DocumentMessageData represents document message data
type DocumentMessageData struct {
	Caption   *string   `json:"caption,omitempty"`
	MediaInfo MediaInfo `json:"media_info"`
	Filename  *string   `json:"filename,omitempty"`
}

// MediaInfo contains metadata about media files
type MediaInfo struct {
	MimeType      string  `json:"mime_type"`
	FileSize      *uint64 `json:"file_size,omitempty"`
	FileSha256    *string `json:"file_sha256,omitempty"`
	FileEncSha256 *string `json:"file_enc_sha256,omitempty"`
	MediaKey      *string `json:"media_key,omitempty"`
	DirectPath    *string `json:"direct_path,omitempty"`
	URL           *string `json:"url,omitempty"`
	Thumbnail     []byte  `json:"thumbnail,omitempty"`
	Width         *uint32 `json:"width,omitempty"`
	Height        *uint32 `json:"height,omitempty"`
	Duration      *uint32 `json:"duration,omitempty"`
}

type MessageEvent struct {
	From        string      `json:"from"`
	MessageType MessageType `json:"message_type"`
	Timestamp   int64       `json:"timestamp"`
	MessageID   string      `json:"message_id"`
}

type ConnectionStatusEvent struct {
	Status int    `json:"status"`
	Reason string `json:"reason,omitempty"`
}

type ErrorEvent struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// marshalEvent centralizes JSON serialization of events
func marshalEvent(evt interface{}) (string, error) {
	jsonData, err := json.Marshal(evt)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

// Helper function to send events via callback with safety checks
func sendEvent(callback C.EventCallback, userData unsafe.Pointer, eventType C.int, data interface{}) {
	// Safety check: ensure callback is not null
	if callback == nil {
		return
	}

	// Additional safety: defer recovery from potential panics
	defer func() {
		if r := recover(); r != nil {
			// Log the panic but don't crash the entire process
			fmt.Printf("Recovered from panic in sendEvent: %v\n", r)
		}
	}()

	jsonString, err := marshalEvent(data)
	if err != nil {
		// Send error event instead using marshalEvent for consistency
		errorData := ErrorEvent{
			Code:    int(ErrorInternal),
			Message: "Failed to serialize event data: " + err.Error(),
		}
		if errorJson, jsonErr := marshalEvent(errorData); jsonErr == nil {
			cStr := C.CString(errorJson)
			if cStr != nil {
				defer func() {
					if r := recover(); r != nil {
						fmt.Printf("Recovered from panic during error C.free: %v\n", r)
					} else {
						C.free(unsafe.Pointer(cStr))
					}
				}()

				// Safe callback invocation for error events
				func() {
					defer func() {
						if r := recover(); r != nil {
							fmt.Printf("Recovered from panic during error callback: %v\n", r)
						}
					}()
					C.call_event_callback(callback, C.EVENT_ERROR, cStr, userData)
				}()
			}
		}
		return
	}

	cStr := C.CString(jsonString)
	if cStr != nil {
		defer func() {
			// Additional safety: recover from potential panics during C.free
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic during C.free: %v\n", r)
			} else {
				C.free(unsafe.Pointer(cStr))
			}
		}()

		// Additional safety: recover from potential panics during callback
		func() {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("Recovered from panic during callback: %v\n", r)
				}
			}()
			C.call_event_callback(callback, eventType, cStr, userData)
		}()
	}
}
