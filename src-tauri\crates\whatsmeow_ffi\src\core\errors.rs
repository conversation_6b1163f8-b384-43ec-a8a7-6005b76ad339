use thiserror::Error;

/// Comprehensive error types for the WhatsApp FFI client with specific error categories
#[derive(Error, Debug)]
pub enum WhatsAppError {
    // === FFI-specific errors ===
    #[error("Library loading failed: {0}")]
    LibraryLoad(#[from] libloading::Error),

    #[error("Symbol not found: {symbol}")]
    SymbolNotFound { symbol: String },

    #[error("Invalid event type: {event_type}")]
    InvalidEventType { event_type: i32 },

    #[error("FFI call failed with code {code}: {message}")]
    FfiCallFailed { code: i32, message: String },

    #[error("Null pointer encountered in FFI call: {context}")]
    NullPointer { context: String },

    #[error("Memory alignment error: expected alignment {expected}, got {actual}")]
    MemoryAlignment { expected: usize, actual: usize },

    #[error("Panic caught in FFI callback: {details}")]
    CallbackPanic { details: String },

    // === Client state errors ===
    #[error("Invalid client handle: {handle}")]
    InvalidHandle { handle: usize },

    #[error("Client not connected")]
    NotConnected,

    #[error("Client already connected")]
    AlreadyConnected,

    #[error("Client is shutting down")]
    ShuttingDown,

    // === Message and communication errors ===
    #[error("Invalid JID format: {jid} - {reason}")]
    InvalidJid { jid: String, reason: String },

    #[error("Failed to send message to {jid}: {reason}")]
    SendFailed { jid: String, reason: String },

    #[error("Message too large: {size} bytes (max: {max_size})")]
    MessageTooLarge { size: usize, max_size: usize },

    #[error("Unsupported message type: {message_type}")]
    UnsupportedMessageType { message_type: String },

    // === Data processing errors ===
    #[error("JSON parsing error: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("Invalid UTF-8 in C string: {context}")]
    InvalidUtf8 { context: String },

    #[error("Event parsing failed for type {event_type}: {reason}")]
    EventParsingFailed { event_type: i32, reason: String },

    // === Channel and async errors ===
    #[error("Channel error: {operation} - {reason}")]
    ChannelError { operation: String, reason: String },

    #[error("Timeout occurred during {operation} after {duration_ms}ms")]
    Timeout { operation: String, duration_ms: u64 },

    #[error("Task join error: {0}")]
    TaskJoinError(#[from] tokio::task::JoinError),

    // === Configuration and logging errors ===
    #[error("Invalid log level: {level}")]
    InvalidLogLevel { level: i32 },

    #[error("Configuration error: {parameter} - {reason}")]
    ConfigurationError { parameter: String, reason: String },

    // === Media-related errors ===
    #[error("Media error: {0}")]
    Media(#[from] crate::core::media::MediaError),

    // === Generic internal errors ===
    #[error("Internal error: {0}")]
    Internal(String),

    // Legacy compatibility - will be deprecated
    #[error("FFI library error: {0}")]
    #[deprecated(note = "Use more specific error types instead")]
    FfiError(String),
}

impl From<i32> for WhatsAppError {
    fn from(error_code: i32) -> Self {
        match error_code {
            0 => WhatsAppError::Internal("No error".to_string()),
            1 => WhatsAppError::InvalidHandle { handle: 0 },
            2 => WhatsAppError::NotConnected,
            3 => WhatsAppError::InvalidJid {
                jid: "unknown".to_string(),
                reason: "Invalid JID format".to_string(),
            },
            4 => WhatsAppError::SendFailed {
                jid: "unknown".to_string(),
                reason: "Send operation failed".to_string(),
            },
            5 => WhatsAppError::Internal("Internal error occurred".to_string()),
            _ => WhatsAppError::FfiCallFailed {
                code: error_code,
                message: format!("Unknown error code: {}", error_code),
            },
        }
    }
}

impl WhatsAppError {
    /// Create a more specific FFI call error with context
    pub fn ffi_call_failed(code: i32, message: impl Into<String>) -> Self {
        Self::FfiCallFailed {
            code,
            message: message.into(),
        }
    }

    /// Create a symbol not found error
    pub fn symbol_not_found(symbol: impl Into<String>) -> Self {
        Self::SymbolNotFound {
            symbol: symbol.into(),
        }
    }

    /// Create an invalid event type error
    pub fn invalid_event_type(event_type: i32) -> Self {
        Self::InvalidEventType { event_type }
    }

    /// Create a channel error with operation context
    pub fn channel_error(operation: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ChannelError {
            operation: operation.into(),
            reason: reason.into(),
        }
    }

    /// Create an invalid JID error with context
    pub fn invalid_jid(jid: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::InvalidJid {
            jid: jid.into(),
            reason: reason.into(),
        }
    }

    /// Create a send failed error with context
    pub fn send_failed(jid: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::SendFailed {
            jid: jid.into(),
            reason: reason.into(),
        }
    }

    /// Create an invalid handle error
    pub fn invalid_handle(handle: usize) -> Self {
        Self::InvalidHandle { handle }
    }

    /// Create a timeout error
    pub fn timeout(operation: impl Into<String>, duration_ms: u64) -> Self {
        Self::Timeout {
            operation: operation.into(),
            duration_ms,
        }
    }

    /// Create an event parsing error
    pub fn event_parsing_failed(event_type: i32, reason: impl Into<String>) -> Self {
        Self::EventParsingFailed {
            event_type,
            reason: reason.into(),
        }
    }

    /// Create a null pointer error
    pub fn null_pointer(context: impl Into<String>) -> Self {
        Self::NullPointer {
            context: context.into(),
        }
    }

    /// Create a callback panic error
    pub fn callback_panic(details: impl Into<String>) -> Self {
        Self::CallbackPanic {
            details: details.into(),
        }
    }

    /// Get the error code for FFI interop (if applicable)
    pub fn error_code(&self) -> Option<i32> {
        match self {
            Self::InvalidHandle { .. } => Some(1),
            Self::NotConnected => Some(2),
            Self::InvalidJid { .. } => Some(3),
            Self::SendFailed { .. } => Some(4),
            Self::Internal(_) => Some(5),
            Self::FfiCallFailed { code, .. } => Some(*code),
            Self::InvalidEventType { event_type } => Some(*event_type),
            _ => None,
        }
    }

    /// Check if this is a recoverable error
    pub fn is_recoverable(&self) -> bool {
        match self {
            // Recoverable errors - can retry or continue operation
            Self::NotConnected
            | Self::Timeout { .. }
            | Self::ChannelError { .. }
            | Self::SendFailed { .. } => true,

            // Non-recoverable errors - require restart or reconfiguration
            Self::LibraryLoad(_)
            | Self::SymbolNotFound { .. }
            | Self::InvalidHandle { .. }
            | Self::NullPointer { .. }
            | Self::MemoryAlignment { .. }
            | Self::CallbackPanic { .. } => false,

            // Context-dependent
            Self::FfiCallFailed { code, .. } => *code >= 100, // Assume codes >= 100 are recoverable
            Self::JsonError(_) | Self::InvalidUtf8 { .. } => true,
            Self::EventParsingFailed { .. } => true,

            // Default to non-recoverable for safety
            _ => false,
        }
    }

    /// Get a user-friendly error message
    pub fn user_message(&self) -> String {
        match self {
            Self::NotConnected => "Please connect to WhatsApp first".to_string(),
            Self::InvalidJid { jid, .. } => format!("Invalid phone number: {}", jid),
            Self::SendFailed { reason, .. } => format!("Failed to send message: {}", reason),
            Self::Timeout {
                operation,
                duration_ms,
            } => {
                format!(
                    "Operation '{}' timed out after {}ms",
                    operation, duration_ms
                )
            }
            Self::LibraryLoad(_) => "Failed to load WhatsApp library".to_string(),
            Self::AlreadyConnected => "Already connected to WhatsApp".to_string(),
            Self::ShuttingDown => "Client is shutting down".to_string(),
            _ => self.to_string(),
        }
    }
}

pub type Result<T> = std::result::Result<T, WhatsAppError>;
