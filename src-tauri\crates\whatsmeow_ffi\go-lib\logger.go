package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"sync"

	waLog "go.mau.fi/whatsmeow/util/log"
)

// LogLevel represents different logging levels
type LogLevel int

const (
	LogLevelOff LogLevel = iota
	LogLevelError
	LogLevelWarn
	LogLevelInfo
	LogLevelDebug
)

// String returns the string representation of the log level
func (l LogLevel) String() string {
	switch l {
	case LogLevelOff:
		return "OFF"
	case LogLevelError:
		return "ERROR"
	case LogLevelWarn:
		return "WARN"
	case LogLevelInfo:
		return "INFO"
	case LogLevelDebug:
		return "DEBUG"
	default:
		return "UNKNOWN"
	}
}

// LoggerInterface defines a flexible logging interface
type LoggerInterface interface {
	Debug(msg string, args ...interface{})
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	SetLevel(level LogLevel)
	GetLevel() LogLevel
}

// ConfigurableLogger implements LoggerInterface with level control
type ConfigurableLogger struct {
	mu       sync.RWMutex
	level    LogLevel
	output   io.Writer
	logger   *log.Logger
	disabled bool
}

// NewConfigurableLogger creates a new configurable logger
func NewConfigurableLogger(level LogLevel, output io.Writer) *ConfigurableLogger {
	if output == nil {
		output = os.Stdout
	}

	return &ConfigurableLogger{
		level:  level,
		output: output,
		logger: log.New(output, "", log.LstdFlags),
	}
}

// SetLevel sets the logging level
func (l *ConfigurableLogger) SetLevel(level LogLevel) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.level = level
	l.disabled = (level == LogLevelOff)
}

// GetLevel returns the current logging level
func (l *ConfigurableLogger) GetLevel() LogLevel {
	l.mu.RLock()
	defer l.mu.RUnlock()
	return l.level
}

// Debug logs a debug message
func (l *ConfigurableLogger) Debug(msg string, args ...interface{}) {
	l.log(LogLevelDebug, "DEBUG", msg, args...)
}

// Info logs an info message
func (l *ConfigurableLogger) Info(msg string, args ...interface{}) {
	l.log(LogLevelInfo, "INFO", msg, args...)
}

// Warn logs a warning message
func (l *ConfigurableLogger) Warn(msg string, args ...interface{}) {
	l.log(LogLevelWarn, "WARN", msg, args...)
}

// Error logs an error message
func (l *ConfigurableLogger) Error(msg string, args ...interface{}) {
	l.log(LogLevelError, "ERROR", msg, args...)
}

// log is the internal logging method
func (l *ConfigurableLogger) log(msgLevel LogLevel, levelStr, msg string, args ...interface{}) {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.disabled || msgLevel > l.level {
		return
	}

	formattedMsg := fmt.Sprintf(msg, args...)
	l.logger.Printf("[%s] %s", levelStr, formattedMsg)
}

// WhatsAppLoggerAdapter adapts our logger to whatsmeow's logging interface
type WhatsAppLoggerAdapter struct {
	logger LoggerInterface
	module string
}

// NewWhatsAppLoggerAdapter creates a new adapter for whatsmeow logging
func NewWhatsAppLoggerAdapter(logger LoggerInterface, module string) waLog.Logger {
	return &WhatsAppLoggerAdapter{
		logger: logger,
		module: module,
	}
}

// Debugf implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Debugf(msg string, args ...interface{}) {
	w.logger.Debug(fmt.Sprintf("[%s] %s", w.module, fmt.Sprintf(msg, args...)))
}

// Debugfln implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Debugfln(msg string, args ...interface{}) {
	w.logger.Debug(fmt.Sprintf("[%s] %s", w.module, msg), args...)
}

// Infof implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Infof(msg string, args ...interface{}) {
	w.logger.Info(fmt.Sprintf("[%s] %s", w.module, fmt.Sprintf(msg, args...)))
}

// Infofln implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Infofln(msg string, args ...interface{}) {
	w.logger.Info(fmt.Sprintf("[%s] %s", w.module, msg), args...)
}

// Warnf implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Warnf(msg string, args ...interface{}) {
	w.logger.Warn(fmt.Sprintf("[%s] %s", w.module, fmt.Sprintf(msg, args...)))
}

// Warnfln implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Warnfln(msg string, args ...interface{}) {
	w.logger.Warn(fmt.Sprintf("[%s] %s", w.module, msg), args...)
}

// Errorf implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Errorf(msg string, args ...interface{}) {
	w.logger.Error(fmt.Sprintf("[%s] %s", w.module, fmt.Sprintf(msg, args...)))
}

// Errorfln implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Errorfln(msg string, args ...interface{}) {
	w.logger.Error(fmt.Sprintf("[%s] %s", w.module, msg), args...)
}

// Sub implements waLog.Logger
func (w *WhatsAppLoggerAdapter) Sub(module string) waLog.Logger {
	subModule := w.module
	if subModule != "" {
		subModule += ":" + module
	} else {
		subModule = module
	}
	return NewWhatsAppLoggerAdapter(w.logger, subModule)
}

// Global logger instance
var (
	globalLogger LoggerInterface
	loggerMu     sync.RWMutex
)

// InitializeLogger initializes the global logger
func InitializeLogger(level LogLevel, output io.Writer) {
	loggerMu.Lock()
	defer loggerMu.Unlock()
	globalLogger = NewConfigurableLogger(level, output)
}

// GetGlobalLogger returns the global logger instance
func GetGlobalLogger() LoggerInterface {
	loggerMu.RLock()
	defer loggerMu.RUnlock()
	if globalLogger == nil {
		// Default logger if not initialized
		globalLogger = NewConfigurableLogger(LogLevelInfo, os.Stdout)
	}
	return globalLogger
}

// SetGlobalLogLevel sets the global logging level
func SetGlobalLogLevel(level LogLevel) {
	logger := GetGlobalLogger()
	logger.SetLevel(level)
}

// GetGlobalLogLevel gets the current global logging level
func GetGlobalLogLevel() LogLevel {
	logger := GetGlobalLogger()
	return logger.GetLevel()
}

// DiscardLogger creates a logger that discards all output
func DiscardLogger() LoggerInterface {
	return NewConfigurableLogger(LogLevelOff, io.Discard)
}

// ParseLogLevel parses a string into a LogLevel
func ParseLogLevel(level string) LogLevel {
	switch strings.ToUpper(level) {
	case "OFF":
		return LogLevelOff
	case "ERROR":
		return LogLevelError
	case "WARN", "WARNING":
		return LogLevelWarn
	case "INFO":
		return LogLevelInfo
	case "DEBUG":
		return LogLevelDebug
	default:
		return LogLevelInfo
	}
}
