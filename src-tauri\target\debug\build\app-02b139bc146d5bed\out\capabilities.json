{"default": {"identifier": "default", "description": "Capability for the main window", "local": true, "windows": ["main"], "permissions": ["core:default", "core:window:allow-close", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-maximize", "core:window:allow-minimize", "core:window:allow-unmaximize", "core:window:allow-toggle-maximize", "core:window:deny-unminimize", "core:window:allow-set-always-on-top", "core:window:allow-start-dragging", "core:window:allow-set-size", "core:window:allow-set-position", "core:path:default", "core:resources:default"]}}