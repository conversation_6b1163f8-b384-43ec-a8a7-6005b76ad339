{"rustc": 16591470773350601817, "features": "[\"bigdecimal\", \"chrono\", \"postgres-array\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-postgres\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"ipnetwork\", \"mac_address\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-any\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 2357794343378131723, "profile": 15657897354478470176, "path": 10650141606276709978, "deps": [[2995469292676432503, "uuid", false, 6769240453884269147], [7161281228672193341, "sea_query", false, 3205212379452710621], [9897246384292347999, "chrono", false, 541124106390407368], [12409575957772518135, "time", false, 16913109520808322110], [14647456484942590313, "bigdecimal", false, 16054569543174291623], [16119793329258425851, "rust_decimal", false, 15692620249086610884], [16362055519698394275, "serde_json", false, 7262301444845053042], [17982831385697850842, "sqlx", false, 8997539274098623822]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sea-query-binder-46936238a831e2e6\\dep-lib-sea_query_binder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}