{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 14562594517924070096, "deps": [[40386456601120721, "percent_encoding", false, 10973999686076924311], [654232091421095663, "tauri_utils", false, 3594253780372848497], [1200537532907108615, "url<PERSON><PERSON>n", false, 2736535913396215474], [2013030631243296465, "webview2_com", false, 17853286909194364467], [3150220818285335163, "url", false, 5161306808710849531], [3331586631144870129, "getrandom", false, 16756474605138717772], [4143744114649553716, "raw_window_handle", false, 8084507311091100797], [4494683389616423722, "muda", false, 9406010006366867345], [4919829919303820331, "serialize_to_javascript", false, 12326904768701323194], [5986029879202738730, "log", false, 14504179726639124161], [9010263965687315507, "http", false, 15283923244554771932], [9689903380558560274, "serde", false, 1190822165864209379], [10229185211513642314, "mime", false, 1056189065056296338], [10806645703491011684, "thiserror", false, 3797447290076662653], [11989259058781683633, "dunce", false, 6805626572801907050], [12092653563678505622, "build_script_build", false, 14190772732382438435], [12304025191202589669, "tauri_runtime_wry", false, 5666364065376449486], [12565293087094287914, "window_vibrancy", false, 5920909377522603241], [12943761728066819757, "tauri_runtime", false, 2637670673797008882], [12986574360607194341, "serde_repr", false, 11609501881873135158], [13077543566650298139, "heck", false, 1474043532582966568], [13116089016666501665, "windows", false, 6388814616047702111], [13405681745520956630, "tauri_macros", false, 18425195977002371339], [13625485746686963219, "anyhow", false, 13018930408720609840], [16362055519698394275, "serde_json", false, 10974075219193167317], [16928111194414003569, "dirs", false, 1820458944158000022], [17155886227862585100, "glob", false, 13970887638437548295], [17531218394775549125, "tokio", false, 1823595253064958708]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-3c1e34db6db64b6d\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}