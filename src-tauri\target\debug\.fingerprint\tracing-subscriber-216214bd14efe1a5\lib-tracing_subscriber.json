{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 15939273003786564609, "deps": [[1017461770342116999, "sharded_slab", false, 7998385626293153321], [6048213226671835012, "smallvec", false, 9000949444894952891], [8614575489689151157, "nu_ansi_term", false, 3153992907096629763], [10806489435541507125, "tracing_log", false, 13849270071129061231], [11033263105862272874, "tracing_core", false, 1941578643623782416], [12427285511609802057, "thread_local", false, 5452421818686238881]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-216214bd14efe1a5\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}